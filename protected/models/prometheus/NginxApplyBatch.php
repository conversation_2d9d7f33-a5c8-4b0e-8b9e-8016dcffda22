<?php
/**
 * User: beenzhang
 * Date: 2022/11/9
 * Email: been<<EMAIL>>
 */

namespace common\models\prometheus;

use common\components\PrometheusModel;

/**
 * This is the model class for table "tbl_nginx_apply_batch".
 *
 * The followings are the available columns in table 'tbl_nginx_apply_batch':
 * @property string $batch_id
 * @property integer $batch_union_id
 * @property integer $dc_id
 * @property string $cluster
 * @property integer $type
 * @property string $user_id
 * @property int $iteration_id
 * @property int $release_plan_id
 * @property integer $status
 * @property string $apply_time
 * @property string $fail_msg
 * @property string $create_time
 * @property string $update_time
 */
class NginxApplyBatch extends PrometheusModel
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_nginx_apply_batch';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array();
    }
}