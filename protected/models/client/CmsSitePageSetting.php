<?php

/**
 * This is the model class for table "tbl_cms_site_page_setting".
 *
 * The followings are the available columns in table 'tbl_cms_site_page_setting':
 * @property integer $client_id
 * @property string $site_id
 * @property string $key
 * @property string $value
 * @property integer $enable_flag
 * @property integer $update_user
 * @property string $create_time
 * @property string $update_time
 */
class CmsSitePageSetting extends ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_cms_site_page_setting';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('client_id, site_id, key, create_time, update_time', 'required'),
            array('client_id, enable_flag, update_user', 'numerical', 'integerOnly'=>true),
            array('site_id', 'length', 'max'=>11),
            array('key', 'length', 'max'=>128),
            // The following rule is used by search().
            // @todo Please remove those attributes that should not be searched.
            array('client_id, site_id, key, value, enable_flag, update_user, create_time, update_time', 'safe', 'on'=>'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'client_id' => '客户client_id',
            'site_id' => '网站id',
            'key' => '设置项key',
            'value' => '配置值',
            'enable_flag' => '状态：0:删除 1:未删除',
            'update_user' => '更新用户id',
            'create_time' => '创建日期',
            'update_time' => '更新时间',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('client_id',$this->client_id);
        $criteria->compare('site_id',$this->site_id,true);
        $criteria->compare('key',$this->key,true);
        $criteria->compare('value',$this->value,true);
        $criteria->compare('enable_flag',$this->enable_flag);
        $criteria->compare('update_user',$this->update_user);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return CmsSitePageSetting the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}