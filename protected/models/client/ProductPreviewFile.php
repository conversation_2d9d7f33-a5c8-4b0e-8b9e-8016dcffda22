<?php

/**
 * This is the model class for table "tbl_product_file".
 *
 * The followings are the available columns in table 'tbl_product_file':
 * @property string $product_id
 * @property integer $client_id
 * @property integer $file_language
 * @property string $image_file_id
 * @property string $pdf_file_id
 * @property string $create_time
 * @property string $update_time
 */
class ProductPreviewFile extends PgActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_product_preview_file';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return [];
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
            'product_id' => 'Product',
            'client_id' => 'Client',
            'file_language' => 'File Language',
            'image_file_id' => 'Image File',
            'pdf_file_id' => 'Pdf File',
            'create_time' => 'Create Time',
            'update_time' => 'Update Time',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria=new CDbCriteria;

        $criteria->compare('product_id',$this->product_id,true);
        $criteria->compare('client_id',$this->client_id);
        $criteria->compare('file_language',$this->file_language);
        $criteria->compare('image_file_id',$this->image_file_id,true);
        $criteria->compare('pdf_file_id',$this->pdf_file_id,true);
        $criteria->compare('create_time',$this->create_time,true);
        $criteria->compare('update_time',$this->update_time,true);

        return new CActiveDataProvider($this, array(
            'criteria'=>$criteria,
        ));
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return ProductPreviewFile the static model class
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }
}