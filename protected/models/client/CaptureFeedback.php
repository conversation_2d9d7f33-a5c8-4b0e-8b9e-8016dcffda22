<?php
/**
 * Created by PhpStorm.
 * User: tony
 * Date: 2019-12-09
 * Time: 14:42
 */


namespace common\models\client;

/**
 * This is the model class for table "tbl_ai_capture_feedback".
 *
 * The followings are the available columns in table 'tbl_ai_capture_feedback':
 *
 * @property integer $feedback_id
 * @property integer $biz_id
 * @property integer $biz_type
 * @property integer $client_id
 * @property integer $user_id
 * @property integer $tag_no
 * @property string $data
 * @property string $display
 * @property string $create_time
 * @property string $update_time
 */
class CaptureFeedback extends \ProjectActiveRecord
{
    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'tbl_ai_capture_feedback';
    }

    public function autoIncrementColumn()
    {
        return 'feedback_id';
    }

    public function primaryKey()
    {
        return 'feedback_id';
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return \PgActiveRecord
     */
    public static function model($className=__CLASS__)
    {
        return parent::model($className);
    }

    public static function getModelTableName()
    {
        return self::model()->tableName();
    }
}