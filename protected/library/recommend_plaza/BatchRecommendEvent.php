<?php
/**
 * Created by PhpStorm.
 * Author: <PERSON>(<PERSON>)
 * Date: 2023/12/13
 * Time: 20:17
 */

namespace common\library\recommend_plaza;

use xiaoman\orm\common\BatchObject;

/**
 * @method RecommendEventFormatter getFormatter()
 * @method RecommendEventOperator getOperator()
 */
class  BatchRecommendEvent extends BatchObject {

    public static function getMetadataClass()
    {
        return RecommendEventMetadata::class;
    }
}