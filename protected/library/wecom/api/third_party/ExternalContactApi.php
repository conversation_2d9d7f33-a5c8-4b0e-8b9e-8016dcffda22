<?php

namespace common\library\wecom\api\third_party;


use common\library\wecom\cache\AccessToken;

class ExternalContactApi
{
    private AccessToken $access_token;

    public function __construct(AccessToken $accessToken)
    {
        $this->access_token = $accessToken;
    }


    /**
     * 获取客户详情
     * 企业可通过此接口，根据外部联系人的userid，拉取客户详情。
     * https://developer.work.weixin.qq.com/document/path/92265
     * @param string $external_userid
     * @return array
     */
    public function getExternalUserInfo(string $external_userid): array
    {
        $externalContact = new ExternalContact($this->access_token);
        return $externalContact->getExternalUserInfo($external_userid);
    }
}