<?php

/**
 * This file is part of xiaoman-crm.
 *
 * Copyright © 2012 - 2019 Xiaoman. All Rights Reserved.
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> <she<PERSON><PERSON><PERSON>@xiaoman.cn>, on 2019/06/17.
 */

namespace common\library\sns\client;

use common\library\sns\FeedPublish;
use JsonException;
use LogUtil;
use Overtrue\Socialite\Config;
use ProcessException;
use Yii;

class FacebookClient extends AbstractOAuthClient
{

    protected $apiUri = 'https://graph.facebook.com/v21.0/';

    /**
     * 获取 driver 的名字，需要先在 manager 注册
     *
     * @return string
     */
    public function getDriver(): string
    {
        return 'facebook_custom';
    }

    /**
     * 发布消息、分享、帖子
     *
     * @param Config $config
     * @param FeedPublish $feed
     *
     * @return array|mixed
     */
    public function publish(Config $config, FeedPublish $feed)
    {

        $ret = $errors = [];

        $pages = $feed->getFacebookPages();

        $pageInfo = $this->getPages($config, $config->get('sns_account_id'));

        $pageToken = array_column($pageInfo['data'] ?? [], 'access_token', 'id');

        if ($images = $feed->getImageUrls()) {
            $params = [
                'url' => $images[0] ?? '',
                'caption' => $feed->text,
                'access_token' => $config->get('access_token'),
            ];
            $apiSuffix = '/photos';
        } else {
            $params = [
                'message' => $feed->text,
                'access_token' => $config->get('access_token'),
            ];
            $apiSuffix = '/feed';
        }

        foreach ($pages as $page) {

            $params['access_token'] = $pageToken[$page['page_id']] ?? null;

            if (empty($params['access_token'])) {
                $errors[] = "[facebook.feed.publish]: {$page['page_id']} 获取 page_token 失败\n";
                continue;
            }

            // send request

            try {
                $response = $this->getApiClient()->request('POST', $this->apiUri . "{$page['page_id']}{$apiSuffix}", ['form_params' => $params]);

                $data = \json_decode($response->getBody(), true);

                $ret[$page['page_id']] = $data;

            } catch (\Exception $ex) {
                $errors[] = "[facebook.feed.publish]: {$ex->getMessage()}\n";
            } catch (\GuzzleHttp\Exception\GuzzleException $ex) {
                $errors[] = "[facebook.feed.publish]: {$ex->getMessage()}\n";
            }

        }

        $this->apiErrors = $errors;

        return $ret;
    }

    /**
     * 获取 Driver 配置
     *
     * @return array
     */
    protected function getDriverConfig(): array
    {
        return [
            'client_id' => \Yii::app()->params['sns_oauth_client']['facebook']['client_id'],
            'client_secret' => \Yii::app()->params['sns_oauth_client']['facebook']['client_secret'],
            'redirect' => \Yii::app()->params['sns_oauth_client']['facebook']['redirect']
        ];
    }

    public function getAccountNickname()
    {
        return  $this->authUser()->getOriginal()['name'] ?? $this->authUser()->getName();
    }

    public function getAccountName()
    {
        return $this->authUser()->getEmail() ?? $this->authUser()->getName();
    }

    /**
     * 获取账号主页链接
     *
     * @return mixed
     */
    public function getAccountHomepageUrl()
    {
        return $this->authUser()->getOriginal()['link'] ?? 'https://facebook.com';
    }

    /**
     * 获取账号粉丝数量
     *
     * @return mixed
     */
    public function getAccountFansCount()
    {
        return 0;
    }

    /**
     * 获取账号朋友数量
     *
     * @return mixed
     */
    public function getAccountFriendsCount()
    {
        return 0; // 权限缺失；暂时去除
       /* $accessToken = $this->getAccountAccessToken();
        $appSecretProof = hash_hmac('sha256', $accessToken, $this->getDriverConfig()['client_secret']);
        $params = [
            'fields' => 'friends',
            'access_token' => $accessToken,
            'appsecret_proof' => $appSecretProof
        ];
        $bodyContent = $this->getApiClient()->get($this->apiUri . 'me?' . \http_build_query($params))->getBody();
        $info = \json_decode($bodyContent, true);

        return (int) ($info['friends']['summary']['total_count'] ?? 0);*/
    }

    /**
     * 获取账号 AccessToken 过期时间
     *
     * @see https://developers.facebook.com/docs/facebook-login/access-tokens
     *
     * @return mixed
     */
    public function getAccountAccessTokenExpiredAt()
    {
        return date('Y-m-d H:i:s', strtotime('+60 day'));
    }

    /**
     * 获取公共主页
     *
     * @param Config $tokenConfig
     * @param $accountId
     * @return mixed
     */
    public function getPages(Config $tokenConfig, $accountId)
    {
        $response = $this->getApiClient()->get("{$this->apiUri}{$accountId}/accounts?access_token=" . $tokenConfig->get('access_token'));
        return \json_decode($response->getBody()->getContents(), true);
    }

    /**
     * 获取已获得的权限
     */
    public function getPermissionsGranted(Config $tokenConfig, $accountId): array
    {
        $response = $this->getApiClient()->get("{$this->apiUri}{$accountId}/permissions?access_token=" . $tokenConfig->get('access_token'));
        $result =  json_decode($response->getBody()->getContents(), true) ?: [];
        $permissions = array_filter($result['data'] ?? [], function ($item) {
            return $item['status'] === 'granted';
        });

        return array_column($permissions, 'permission');
    }

    /**
     * 验证签名、解析请求
     *
     * @param string $signedRequest
     * @return mixed
     * @throws ProcessException|JsonException
     * @link  https://developers.facebook.com/docs/games/build/legacy-web-games/get-started/login#parsingsr
     */
    public function parseSignedRequest(string $signedRequest)
    {
        // 用"."分割签名和数据
        [$encoded_sig, $payload] = explode('.', $signedRequest, 2);

        $secret = Yii::app()->params['facebook']['client_secret'];

        // 对比签名
        $sig = $this->base64_url_decode($encoded_sig);
        $expected_sig = hash_hmac('sha256', $payload, $secret, true);
        if ($sig !== $expected_sig) {
            LogUtil::error("facebook signedRequest validate error sig: $signedRequest");
            throw new ProcessException("Facebook签名验证失败,");
        }

        $data = $this->base64_url_decode($payload);
        return json_decode($data, true, 255, JSON_THROW_ON_ERROR);
    }

    private function base64_url_decode($input): string
    {
        return base64_decode(strtr($input, '-_', '+/'));
    }
}