<?php

namespace common\library\setting\user\process;

use common\library\privilege_v3\PrivilegeConstants;

class AssistantTabSetting extends Base
{
    const ASSISTANT_TAB_PREFIX = 'assistant.tab.setting:';

    const TAB_INFORMATION = 'information'; //资料
    const TAB_DYNAMIC = 'dynamic'; //动态
    const TAB_OPPORTUNITY_TRANSACTIONS = 'opportunity_transactions'; //商机&交易
    const TAB_CUSTOMER_PORTRAIT = 'customer_portrait'; //客户画像
    const TAB_FILING_SUGGESTIONS = 'filing_suggestions'; //建档建议
    const TAB_INQUIRY = 'inquiry'; //询盘
    const TAB_SALES_GUIDE = 'sales_guide'; //谈单指南
    const TAB_DOCUMENTATION = 'documentation'; //文档记录
    const TAB_LOGISTICS_PRICING = 'logistics_pricing'; //物流查价
    const TAB_CORRESPONDENCE_EMAILS = 'correspondence_emails'; //往来邮件
    const TAB_SCHEDULE = 'schedule'; //日程
    const TAB_TIPS = 'tips'; //Tips
    const TAB_CORRELATION = 'correlation'; //相关
    const TAB_AI_REPLY = 'ai_reply'; // AI回复
    const TAB_BACKGROUND_CHECK = 'background_check'; // 智能背调（Leads AI）
    const TAB_SMART_BACKGROUND = 'smart_background'; // 智能背调（销售助手）
    const TAB_WEBSITE_BEHAVIOR = 'website_behavior'; // 网站行为

    const TAB_OKKI_AI = 'okki_ai'; // OKKI AI
    const TAB_DATA_ANALYSIS = 'data_analysis';

    // 销售助手默认Tab
    const DEFAULT_TAB_SETTING = [
        self::TAB_INFORMATION,
        self::TAB_DYNAMIC,
        self::TAB_OPPORTUNITY_TRANSACTIONS,
        self::TAB_DATA_ANALYSIS,
        self::TAB_CUSTOMER_PORTRAIT, // 客户画像
        self::TAB_FILING_SUGGESTIONS,
        self::TAB_INQUIRY,
        self::TAB_SALES_GUIDE,
        self::TAB_DOCUMENTATION,
        self::TAB_LOGISTICS_PRICING,
        self::TAB_CORRESPONDENCE_EMAILS,
        self::TAB_SCHEDULE,
        self::TAB_TIPS,
        self::TAB_CORRELATION,
        self::TAB_BACKGROUND_CHECK,
    ];

    //输出
    public function formatResult($value)
    {
        if (empty($value)) {
            $value = self::DEFAULT_TAB_SETTING;
        } else {
            $value = json_decode($value, true);
        }
        return $value;
    }

    //输入
    public function formatValue($value, $oldValue)
    {
        if (empty($value)) {
            throw new \RuntimeException(\Yii::t('opportunity', "{key} writing cannot be empty",['{key}' => $this->key]));
        }

        $value = json_decode($value, true);

        $cacheKey = self::ASSISTANT_TAB_PREFIX.':{'.$this->clientId.'}:{'.$this->userId.'}';
        \RedisService::cache()->del([$cacheKey]);

        return json_encode($value);
    }
}
