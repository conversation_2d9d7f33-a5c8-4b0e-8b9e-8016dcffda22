<?php
    
namespace common\library\button\purchase_order;

use common\library\privilege_v3\PrivilegeConstants;

class GenerateCostInvoiceProcess extends BaseButtonProcess
{
    public $current_privilege = PrivilegeConstants::PRIVILEGE_CRM_COST_INVOICE_CREATE;
    /**
    * @return mixed
    */
    public function process()
    {
        try {
            if (!$this->hasPrivilege()) {
                $this->setAccess(false);
                $this->setTip(\Yii::t('button', '暂无权限'));
                return;
            }
            $this->setAccess(true);
        } finally {
            $this->addExtend('record_data', []);
        }
    }
}