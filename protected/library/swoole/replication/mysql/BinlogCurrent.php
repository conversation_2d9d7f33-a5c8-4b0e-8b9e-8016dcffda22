<?php
/**
 * Created by PhpStorm.
 * User: andy
 * Date: 2020/6/18
 * Time: 17:46
 */
namespace common\library\swoole\replication\mysql;

use Predis\Client;

class BinlogCurrent
{
    const PREFIX = 'crm:binlog:';
    /**
     * @var Client
     */
    private $redis;

    private $host;
    private $filename;
    private $position;
    private $timestamp;

    /**
     * @var GtidCollection
     */
    private $gtidCollection;

    public function __construct($host, $filename = null, $position = null, $timestamp = null)
    {
        $this->host = $host;
        $this->filename = $filename;
        $this->position = $position;
        $this->timestamp = $timestamp;
    }

    public function setGtidCollection(GtidCollection $gtids)
    {
        $this->gtidCollection = $gtids;
    }

    public function updateGtid($sid, $gno)
    {
        $this->gtidCollection->update($sid, $gno);
    }

    public function setRedis($redis)
    {
        $this->redis = $redis;
    }

    public function setFilename($fileName)
    {
        $this->filename = $fileName;
    }

    public function setPosition($pos, $timestamp = null)
    {
        $this->position = $pos;

        if ($timestamp !== null)
            $this->timestamp = $timestamp;
    }

    public function getGtidCollection()
    {
        return $this->gtidCollection;
    }

    public function getFilename()
    {
        return $this->filename;
    }

    public function getPosition()
    {
        return $this->position;
    }

    public function landing()
    {
        $key = self::PREFIX . $this->host;

        $value = json_encode([
            $this->filename,
            $this->position,
            $this->timestamp,
            $this->gtidCollection->toString()
        ]);

        return apcu_store($key, $value);
    }

    public function restore()
    {
        $key = self::PREFIX . $this->host;

        $result = apcu_fetch($key);

        if (!$result)
            return false;

        [$this->filename, $this->position, $this->timestamp, $gtidCollection] = json_decode($result);

        $this->setGtidCollection(GtidCollection::makeCollectionFromString($gtidCollection));

        return true;
    }

    public function saveToRedis()
    {
        $key = self::PREFIX . $this->host;

        $value = json_encode([
            $this->filename,
            $this->position,
            $this->timestamp,
            $this->gtidCollection->toString()

        ]);

        return $this->redis->set($key, $value);
    }

    public function restoreFromRedis()
    {
        $key = self::PREFIX . $this->host;

        $result = $this->redis->get($key);

        if (!$result)
            return false;

        [$this->filename, $this->position, $this->timestamp, $gtidCollection] = json_decode($result);

        $this->setGtidCollection(GtidCollection::makeCollectionFromString($gtidCollection));

        return true;
    }

    public function dump()
    {
        $date = date("Y-m-d H:i:s", $this->timestamp);
        $gtid = $this->gtidCollection->toString();
        echo "[$date]{$this->host} {$this->filename}({$this->position}) $gtid\n";
    }
}
