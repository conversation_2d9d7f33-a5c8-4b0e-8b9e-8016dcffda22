<?php
/*
$a = ['a' => 'aa', 'b' => 100];
$validator = [
    'andy' => [
        'a' => [
            'name' => '啊',
            'type' => 'json',
            'elem' => [
                'a' => [
                    'name' => '啊啊',
                    'rules' => [
                        'numerical' => array(
                            'allowEmpty' => false,
                            'message' => '{attribute}只能为数字'
                        )
                    ]
                ]
            ]
        ]
    ]
];

$v = new Validator();
$v->validateConfig($validator['andy'], ['a' => '123']);
$v->validateConfig($validator['andy'], ['a' => $a]);
*/

class Validator
{
    protected $config;
    protected static $validators = array();
    protected $inputData;

    /**
     * @param string $key
     * @return array
     */
    public function getConfig($key)
    {
        return array_key_exists($key, $this->config) ? $this->config[$key] : null;
    }

    public function setConfig($config)
    {
        return $this->config = $config;
    }

    /**
     * 如果不指定controller 请调用setConfig
     * Validator constructor.
     * @param Controller|null $controller
     */
    public function __construct(Controller $controller = null)
    {
        if ($controller)
            $this->config = $controller->getvalidatorRule();
        $this->setInputData($_REQUEST);
    }

    public function setInputData($data)
    {
        $this->inputData = $data;
    }

    public function getInputData($param)
    {
        return isset($this->inputData[$param]) ? $this->inputData[$param] : '';
    }

    public function validateConfig(array $config, $inputData = null)
    {
        if ($inputData === null)
            $inputData = $this->inputData;

        foreach ($config as $param => $detail)
        {
            $name = $detail['name'] ?? $param;

            $item = new ValidatorItem();
            $item->itemLabel = $name;
            $item->itemValue = $inputData[$param] ?? null;

            if (isset($detail['rules']))
            {
                foreach ($detail['rules'] as $type=>$elem)
                {
                    $validator = self::createValidator($type, $elem);
                    $validator->validate($item);
                    if (($error = $item->getError('itemValue')) !== null)
                    {
                        if (is_array($item->itemValue))
                            $str = var_export($item->itemValue, true);
                        else
                            $str = $item->itemValue;

                        throw new RuntimeException(@strtr($error, array('{value}'=>$str)));
                    }
                }
            }

            $type = $detail['type'] ?? '';

            switch ($type)
            {
                case 'json':
                    $validator = new CTypeValidator();
                    $validator->attributes = array('itemValue');
                    $validator->type = 'array';
                    $validator->validate($item);

                    if (($error = $item->getError('itemValue')) !== null)
                    {
                        throw new RuntimeException(@strtr($error, array('{value}'=>$item->itemValue)));
                    }

                    $this->validateConfig($detail['elem'] ?? [], $inputData[$param]);

                    break;
                case 'array':
                    $validator = new CTypeValidator();
                    $validator->attributes = array('itemValue');
                    $validator->type = 'array';
                    $validator->validate($item);

                    if (($error = $item->getError('itemValue')) !== null)
                    {
                        throw new RuntimeException(@strtr($error, array('{value}'=>$item->itemValue)));
                    }

                    $i = 0;
                    foreach ($item->itemValue as $datum)
                    {
                        $key = $name.'.'.$i;

                        $this->validateConfig([$key => $detail['elem']], [$key => $datum]);
                        ++$i;
                    }
                    break;
            }
        }
    }
    
    public function validate($action = null)
    {
        if ($action === null)
            $action = Yii::app()->controller->action->id;

        $config = $this->getConfig($action);

        if ($config === null) return true;
        
        $this->validateConfig($config);
    }

    /**
     * @param $type
     * @param $detail
     * @return CValidator
     */
    public static function createValidator($type, $detail)
    {
        if (array_key_exists($type, CValidator::$builtInValidators))
            $class = CValidator::$builtInValidators[$type];
        else if (class_exists($type))
            $class = $type;
        else
            throw new RuntimeException("校验器{$type}不存在，提交失败");
        
        $object = new $class;
        $object->attributes = array('itemValue');
        foreach ($detail as $key=>$value)
        {
            $object->$key = $value;
        }
        return $object;
    }        
}