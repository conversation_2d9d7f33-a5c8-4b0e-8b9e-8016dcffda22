<?php
namespace common\library\oms\product_transfer\dynamic\builder;

class HelperCreateDynamic extends HelperDynamic{
    //@#处理人(handlers)#任务还未完成。请及时生成#下游业务单据(downstream_invoice_name)#。点击#上游单据编号#，查看更多#上游单据类型(upstream_invoice_name)#信息。
    protected $requireParamKey = ["handlers", "refer_id"];
    protected $userFields = ['handlers'];
    protected $upstreamIdField = 'refer_id';
    protected $keywordsMap = [
        "handlers" => ["type" => "call_at"],
        "refer_id" => [     // 上游单据
            'type' => 'action',
            'name' => 'view_upstream_no',
            'action_type' => 'viewUpStreamInvoice',
            'params'=>[
                'refer_id' => ['class' => self::class, 'method' => 'getValue', 'params'=>'refer_id'],   // $this->getValue('refer_id')
                'refer_type' => ['class' => self::class, 'method' => 'getDownstreamInvoiceType']
            ]
        ],
//        "refer_type" => ['type' => 'text'],
        "downstream_invoice_name" => [
            'type' => 'action',
            'name' => 'view_downstream_invoice',    // name属性会配置在message兼容中英双语
            'action_type' => 'create_downstream_invoice',
            'params'=>[
                'type' => ['class' => self::class, 'method' => 'getDownstreamInvoiceType']
            ]
        ],
        "upstream_invoice_name" => [
            'type' => 'text',
            'name' => 'view_upstream_invoice',    // name属性会配置在message兼容中英双语
        ]
    ];

    public function format(){
        $template = $this->_getTemplate();
        $this->template = sprintf(
            $template,
            '{{$info.handlers}}',
            '{{render(\'create_downstream_invoice\', $info.downstream_invoice_name, $info.downstream_params)}}',
            '{{render(\'refer_drawer\', $info.upstream_invoice_no, $info.upstream_params)}}',
            '{{$info.upstream_invoice_name}}'
        );

        $downstreamParams = $this->getCreateDownstreamInvoiceParams();
        $this->templateInfo = [
            'handlers' => $this->getCallAtNames($this->getDataValue('handlers')),
            'downstream_invoice_name' => $this->formatByKeywordName($this->keywordsMap['downstream_invoice_name']['name']),
            'downstream_params' => $downstreamParams,
            'upstream_invoice_no' => $this->formatByKeywordName($this->keywordsMap['refer_id']['name']),
            'upstream_params' => [
                'refer_id' => $this->getDataValue('refer_id'),
                'refer_type' => $this->getUpstreamInvoiceType()
            ],
            'upstream_invoice_name' => $this->formatByKeywordName($this->keywordsMap['upstream_invoice_name']['name']),
        ];
    }
}