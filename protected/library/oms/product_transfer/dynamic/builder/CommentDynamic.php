<?php
namespace common\library\oms\product_transfer\dynamic\builder;

class CommentDynamic extends BaseDynamic{
    // #user# 说：#comment#
    protected $requireParamKey = ["user"];
    protected $userFields = ['user'];
    protected $fileIdFields = ['file_ids'];
    protected $keywordsMap = [
        "user" => ["type" => "text"],
        "comment" => ["type" => "text"],
        "file_ids" => ["type" => "text"],
        'schedule_comment_id' => ["type" => "text"],
    ];

    public function format(){
        $template = $this->_getTemplate();
        if($this->dynamicData['enable_flag'] == \Constants::ENABLE_FLAG_TRUE){
            $this->template = sprintf($template, '{{$info.user}}', '{{render(\'remark\',$info.comment,$info.params)}}');
            $comment = $this->getDataValue('comment');
        }else{
            $this->template = sprintf($template, '{{$info.user}}', '{{render(\'remark\',$info.comment,$info.params)}}');
            $comment = \Yii::t('transfer_dynamic', 'comment_delete');
        }

        // 附件填充
        $fileIds = [];
        foreach($this->fileIdFields as $fileIdField){
            if(!isset($this->dataMap[$fileIdField])){
                continue;
            }
            $curFileIds = $this->getDataValue($fileIdField);
            if(!is_array($curFileIds)){
                $curFileIds = [$curFileIds];
            }
            $fileIds = array_merge($fileIds, $curFileIds);
        }

        $files = [];
        if(isset(self::$extraData['file_map'])) {
            foreach ($fileIds as $fileId) {
                if(isset(self::$extraData['file_map'][$fileId])){
                    $files[] = self::$extraData['file_map'][$fileId];
                }
            }
        }
        $this->templateInfo = [
            'user' => $this->getUserNames($this->getDataValue('user')),
            'comment' => $comment,
            'params' => [
                'enable_flag' => $this->dynamicData['enable_flag'],
                'dynamic_id' => $this->dynamicData['dynamic_id'],
                'can_delete' => intval($this->canDelete()),
                'files' => $files
            ]
        ];
    }

    public function schema()
    {
        parent::schema();
        if($this->dynamicData['enable_flag'] == \Constants::ENABLE_FLAG_TRUE){
            $comment = $this->getDataValue('comment');
        }else{
            $comment = \Yii::t('transfer_dynamic', 'comment_delete');
        }
        $this->content = [
            [
                'value_type' => 'text',
                'value' => \Util::plainText($comment)
            ]
        ];
    }

    public function canDelete(){
        $loginUser = \User::getLoginUser();
        return $this->dynamicData['create_user'] == $loginUser->getUserId();
    }
}