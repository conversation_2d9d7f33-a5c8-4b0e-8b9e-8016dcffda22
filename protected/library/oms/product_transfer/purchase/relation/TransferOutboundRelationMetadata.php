<?php

namespace common\library\oms\product_transfer\purchase\relation;

use xiaoman\orm\metadata\Metadata;

class TransferOutboundRelationMetadata extends Metadata
{
    protected  $columns = [
        'relation_id' => [
            'type' => 'bigint',
            'name' => 'relation_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'type' => [
            'type' => 'bigint',
            'name' => 'type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'transfer_invoice_id' => [
            'type' => 'bigint',
            'name' => 'transfer_invoice_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'transfer_invoice_record_id' => [
            'type' => 'bigint',
            'name' => 'transfer_invoice_record_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'refer_id' => [
            'type' => 'bigint',
            'name' => 'refer_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'sub_refer_id' => [
            'type' => 'bigint',
            'name' => 'sub_refer_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'create_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'update_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'enable_flag' => [
            'type' => 'smallint',
            'name' => 'enable_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
    ];


    public static function table()
    {
        return 'tbl_purchase_transfer_to_outbound_relation';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return TransferOutboundRelation::class;
    }

    public static function batchObject()
    {
        return BatchTransferOutboundRelation::class;
    }


    public static function objectIdKey()
    {
        return 'relation_id';
    }

    public static function formatter()
    {
        return TransferOutboundRelationFormatter::class;
    }

    public static function operator()
    {
        return TransferOutboundRelationOperator::class;
    }

    public static function filter()
    {
        return TransferOutboundRelationFilter::class;
    }


}