<?php

namespace common\library\oms\outbound_invoice\other;

use common\library\oms\outbound_invoice\OutboundInvoiceSearcher;
use common\library\product_v2\search\SearchMetadataTrait;
use common\models\es_search\OutboundInvoiceSearch;
use xiaoman\orm\metadata\Metadata;
use common\library\object\traits\BizObjectClass;

/**
 * Class OtherOutboundInvoiceMetadata
 * @package common\library\oms\outbound_invoice\other
 */
class OtherOutboundInvoiceMetadata extends Metadata
{
    use SearchMetadataTrait;
    use BizObjectClass;
    protected  $columns = [
        'outbound_invoice_id' => [
            'type' => 'bigint',
            'name' => 'outbound_invoice_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'client_id' => [
            'type' => 'bigint',
            'name' => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'type' => [
            'type' => 'smallint',
            'name' => 'type',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],

        'serial_id' => [
            'type' => 'character',
            'name' => 'serial_id',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => ['enable' => true, 'batch' => true,]
        ],
        'warehouse_invoice_time' => [
            'type' => 'datetime',
            'name' => 'warehouse_invoice_time',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'status' => [
            'type' => 'smallint',
            'name' => 'status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'remark' => [
            'type' => 'text',
            'name' => 'remark',
            'nullable' => 0,
            'php_type' => 'string',
            'filter' => [ 'enable' => false,]
        ],
        'handler' => [
            'type' => 'array',
            'name' => 'handler',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'array' => true,]
        ],
        'attachment' => [
            'type' => 'jsonb',
            'name' => 'attachment',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'create_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'create_user' => [
            'type' => 'bigint',
            'name' => 'create_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'update_time' => [
            'type' => 'datetime',
            'php_type' => 'string',
            'filter' => ['enable' => true, 'range' => true],
        ],
        'update_user' => [
            'type' => 'bigint',
            'name' => 'update_user',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'external_field_data' => [
            'type' => 'jsonb',
            'name' => 'external_field_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter' => [ 'enable' => true, 'json' => true,]
        ],
        'delete_flag' => [
            'type' => 'smallint',
            'name' => 'delete_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true, 'batch' => true,]
        ],
        'cause_id' => [
            'type' => 'bigint',
            'name' => 'cause_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => [ 'enable' => true,]
        ],
        'product_total_count' => [
            'type' => 'numeric',
            'name' => 'product_total_count',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => []
        ],
        'invoice_warehouse_id' => [
            'type' => 'bigint',
            'name' => 'invoice_warehouse_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter' => ['enable' => true,]
        ],
        'scope_user_ids' => [
            'type' => 'array',
            'name' => 'scope_user_ids',
            'nullable' => '0',
            'php_type' => 'array',
            'filter' => [
                'enable' => true,
                'batch' => true
            ]
        ],
        'cost_total_rmb' => [
            'type' => 'numeric',
            'name' => 'cost_total_rmb',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => []
        ],
        'cost_total_usd' => [
            'type' => 'numeric',
            'name' => 'cost_total_usd',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => []
        ],
        'cost_total' => [
            'type' => 'numeric',
            'name' => 'cost_total',
            'nullable' => 0,
            'php_type' => 'float',
            'filter' => []
        ],
        'scope_user_ids' => [
            'type' => 'array',
            'name' => 'scope_user_ids',
            'nullable' => '0',
            'php_type' => 'array',
            'filter' => [
                'enable' => true,
                'batch' => true
            ]
        ],
    ];


    public static function table()
    {
        return 'tbl_outbound_invoice';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return OtherOutboundInvoice::class;
    }

    public static function batchObject()
    {
        return BatchOtherOutboundInvoice::class;
    }


    public static function objectIdKey()
    {
        return 'outbound_invoice_id';
    }

    public static function formatter()
    {
        return OtherOutboundInvoiceFormatter::class;
    }

    public static function operator()
    {
        return OtherOutboundInvoiceOperator::class;
    }

    public static function filter()
    {
        return OtherOutboundInvoiceFilter::class;
    }

    public static function index_name()
    {
        return OutboundInvoiceSearch::index();
    }

    protected function searchAnalysis(): array
    {
        return OutboundInvoiceSearch::getAnalysis();
    }

    public function searchColumns(): array
    {
        return OutboundInvoiceSearch::getColumns();
    }

    public static function searcher()
    {
        return OutboundInvoiceSearcher::class;
    }

    public static function getModuleType()
    {
        return \Constants::TYPE_OTHER_OUTBOUND_INVOICE;
    }

    public static function objectName()
    {
        return \common\library\object\object_define\Constant::OBJ_OTHER_OUTBOUND_INVOICE;
    }
}
