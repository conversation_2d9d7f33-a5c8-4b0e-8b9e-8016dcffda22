<?php

namespace common\library\oms\inbound_invoice\purchase\record;

use common\library\oms\inbound_invoice\record\InboundRecord;

/**
 * Class PurchaseInboundRecord
 * @package common\library\oms\inbound_invoice\purchase\record
 * @property mixed is_master_product
 * @property mixed master_id
 * @property string $product_image
 * @property string $product_name
 * @property string $product_cn_name
 * @property string $product_model
 * @property string $product_unit
 * @method PurchaseInboundRecordOperator getOperator()
 * @method PurchaseInboundRecordFormatter getFormatter()
 */
class PurchaseInboundRecord extends InboundRecord
{
    use InitPurchaseInboundRecordMetadata;

    public function __construct($clientId, $inbound_invoice_id = null)
    {
        parent::__construct($clientId);
        if (!is_null($inbound_invoice_id)) {
            $this->load([ 'inbound_invoice_id'=> $inbound_invoice_id]);
        }
    }
}


