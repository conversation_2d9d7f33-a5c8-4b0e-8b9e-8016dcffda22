<?php

namespace common\library\oms\order_product;

use common\components\BaseObject;
use common\library\APIConstant;
use common\library\custom_field\FieldList;
use common\library\object\object_define\Constant;
use common\library\oms\common\OmsConstant;
use common\library\oms\order\OrderConstants;
use common\library\oms\order\OrderFilter;
use common\library\oms\product_transfer\ProductTransferHelper;
use common\library\oms\shipping_invoice\record\ShippingRecordFilter;
use common\library\oms\shipping_invoice\ShippingInvoiceFilter;
use common\library\oms\task\formatter\ProductToPurchaseByOrderProductInfoTask;
use common\library\orm\pipeline\formatter\FieldV2FormatTask;
use common\library\orm\pipeline\formatter\order\RecordShowOrderInfoTask;
use common\library\platform_product\PlatformProductAPI;
use common\library\platform_product\sku\PlatformSkuAPI;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\product_v2\ProductConstant;
use common\library\product_v2\ProductFilter;
use common\library\product_v2\sku\SkuAPI;
use common\library\purchase\purchase_order_product\PurchaseOrderProductFilter;
use common\library\util\Arr;
use xiaoman\orm\common\FormatterV2;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\NotIn;


/**
 * class InvoiceProductRecordFormatter
 * @method displayProductInfo($flag)
 * @method displaySkuInfo($flag)
 * @method displayProductSkuInfo($flag)
 * @method displayPlatformProductSkuInfo($flag)
 * @method displayPlatformProductInfo($flag)
 * @method displayListOrderInfo($flag)
 * @method displayListProductInfo($flag)
 * @method displayListSkuInfo($flag)
 * @method displayFieldUnit($flag)
 * @method displayCombineProduct($flag)
 * @method displayShippingRecordInfo(bool $flag)
 * @method displayToBePurchaseCount(bool $flag)
 * @method displayCalculateGrossMargin(bool $flag)
 * @method displayMergeAllExternalField(bool $flag)
 * @method displayProductImageInfoList(bool $flag) //产品图片显示为file列表，下推时 与其他单据的product_image结构保持一致
 * @method displayFieldFormatter(bool|FieldV2FormatTask $flag)
 * @package common\library\oms\invoice_product_record
 */
class OrderProductFormatter extends FormatterV2
{
    protected $scene;
    const MAPPING_SETTING = [
        'order_product_snapshot' => [
            'build_function' => 'buildOrderProductSnapshot',
            'mapping_function' => 'mapOrderProductSnapshot',
        ],
        'field_formatter' => [
            'task_class' => FieldV2FormatTask::class
        ],
        'product_info' => [
            'build_function' => 'buildProductInfo',
            'mapping_function' => 'mapProductInfo',
        ],
        'sku_info' => [
            'build_function' => 'buildSkuInfo',
            'mapping_function' => 'mapSkuInfo',
        ],
        'platform_sku_info' => [
            'build_function' => 'buildPlatformProductSkuInfo',
            'mapping_function' => 'mapPlatformProductSkuInfo',
        ],
        'platform_product_info' => [
            'build_function' => 'buildPlatformProductInfo',
            'mapping_function' => 'mapPlatformProductInfo',
        ],
        'invoice_info' => [
            'build_function' => 'buildInvoiceInfo',
            'mapping_function' => 'mapInvoiceInfo',
        ],
        'combine_product' => [
            'build_function' => 'buildCombineProduct',
            'mapping_function' => 'mapCombineProduct'
        ],
        'shipping_record_info' => [
            'build_function' => 'buildShippingRecordInfo',
            'mapping_function' => 'mapShippingRecordInfo',
        ],
        'product_image_info_list' => [
            'build_function' => 'buildProductImageInfoList',
            'mapping_function' => 'mapProductImageInfoList',
        ],
        'to_be_purchase_count' => [
            'build_function' => 'buildToBePurchaseCount',
            'mapping_function' => 'mapToBePurchaseCount',
        ],
        'product_to_purchase_by_order_product_info' => [
            'task_class' => ProductToPurchaseByOrderProductInfoTask::class,
        ],
        // 订单产品列表展示订单信息task，注意用的时候别搞成循环调用了
        'list_order_info' => [
            'task_class' => RecordShowOrderInfoTask::class,
        ],
        // 订单产品列表展示产品一些信息
//        'list_product_info' => [
//            'build_function' => 'buildListProductInfo',
//            'mapping_function' => 'mapListProductInfo',
//        ],
        'list_sku_info' => [
            'build_function' => 'buildListSkuInfo',
            'mapping_function' => 'mapListSkuInfo',
        ],
        // 必须要在list_order_info之后，依赖订单的汇率
        'calculate_gross_margin' => [
            'mapping_function' => 'mapCalculateGrossMargin',
        ],
        // 这个要放在倒数第二
        'field_unit' => [
            'build_function' => 'buildFieldUnit',
            'mapping_function' => 'mapFieldUnit',
        ],
        // 这个要放在倒数第一，这里会合并产品扩展字段和订单的扩展字段到数据结构的第一层中，前端显示用
        'merge_all_external_field' => [
            'mapping_function' => 'mapMergeAllExternalField'
        ],
    ];

    public function setScene($scene)
    {
        $this->scene = $scene;
    }

    public function webListSetting()
    {
        $this->displayFields = (new OrderProductMetadata())->getMappedAttributeKeys();
        $this->displayCombineProduct(true);
    }
    
    public function webOrderProductListSetting()
    {
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->functionFieldInfoSetting(['valueNeedString' => true]);
        $this->displayListOrderInfo(true);
        // $this->displayListProductInfo(true);
        $this->displayListSkuInfo(true);
        $this->displayCalculateGrossMargin(true);
        $this->displayFieldUnit(true);
        $this->displayMergeAllExternalField(true);
        $this->setFormatterV2Privilege(false, true, PrivilegeFieldV2::SCENE_OF_VIEW);
    }

    public function taskListSetting()
    {
        $this->displayFields = (new OrderProductMetadata())->getMappedAttributeKeys();
        $this->functionFieldInfoSetting();
    }

    public function functionFieldInfoSetting($params = [])
    {
        extract($params);
        $this->fieldSetting();
        $fieldFormatterTask = new  FieldV2FormatTask($this->clientId);
        $fieldFormatterTask->setFieldInfo($this->fieldSetting);
        $fieldFormatterTask->setObjName(Constant::OBJ_ORDER_PRODUCT);
        $fieldFormatterTask->setDisplayFields($this->displayFields);
        if (!empty($valueNeedString)) {
            $fieldFormatterTask->setValueNeedString($valueNeedString);
        }
        $this->displayFieldFormatter($fieldFormatterTask);
    }

    protected function buildProductInfo($key, array $data)
    {
        $productMap = [];

        $productIds = array_filter(array_unique(array_column($data, 'product_id')));
        if (!empty($productIds)) {
            $productFilter = new ProductFilter($this->clientId);
            $productFilter->product_id = $productIds;
            $productBatch = $productFilter->find();
//            $productBatch->getFormatter()->skuItemsInfoSetting();
            $productBatch->getFormatter()->displayFields([
                'disable_flag',// 订单产品列表需要是否已经停售
                'name',
                'model',
                'product_id',
                'product_no',
                'product_type',
                'enable_flag',
            ]);
            $productBatch->getFormatter()->displaySkuAttributesSettingInfo(true);
            $productBatch->getFormatter()->displaySkuItems(true, null, [], true);
            $productList = $productBatch->getAttributes();
            $productMap = array_column($productList, null, 'product_id');
        }
        return $productMap;
    }

    protected function mapProductInfo(array &$result, $key, array $data)
    {
        $productInfo = $this->getPipelinePrepareData($key)[$result['product_id']] ?? [];
        $skuInfos = array_column($productInfo['sku_items'] ?? [], null,'sku_id');       // 对于阿里订单明细，不一定有sku_items
        $productInfo['product_enable_flag'] = $skuInfos[$result['sku_id']]['enable_flag'] ?? $productInfo['enable_flag'] ?? 0;
        $result += $productInfo;
    }
//    
//    protected function buildListProductInfo($key, array $data)
//    {
//        $productMap = [];
//        $productIds = Arr::uniqueFilterValues(array_column($data, 'product_id'));
//        if (!empty($productIds)) {
//            $productFilter = new ProductFilter($this->clientId);
//            $productFilter->product_id = $productIds;
//            $productFilter->select(['product_id', 'product_no']);
//            $productList = $productFilter->rawData();
//            $productMap = array_column($productList, null, 'product_id');
//        }
//        return $productMap;
//    }
//    
//    protected function mapListProductInfo(array &$result, $key, array $data)
//    {
//        $productInfo = $this->getPipelinePrepareData($key)[$result['product_id']] ?? [];
//        // 注意产品和订单产品都有的字段互相覆盖的问题，暂时没有这么写
//        $result += $productInfo;
//    }

    protected function buildSkuInfo($key, array $data)
    {
        $skuIds = array_unique(array_column($data, 'sku_id'));
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $productSkuMap = array_column($skuApi->items($skuIds), null,'sku_id');
        }
        return $productSkuMap ?? [] ;
    }

    protected function mapSkuInfo(array &$result, $key, array $data)
    {
        $skuInfo = $this->getPipelinePrepareData($key)[$result['sku_id']] ?? [];
        $result['sku_info'] = $skuInfo;
    }
    
    protected function buildListSkuInfo($key, array $data)
    {
        $skuIds = array_unique(array_column($data, 'sku_id'));
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $productSkuMap = array_column($skuApi->items($skuIds, APIConstant::SCENE_ORDER_PRODUCT_LIST), null,'sku_id');
        }
        return $productSkuMap ?? [] ;
    }

    protected function mapListSkuInfo(array &$result, $key, array $data)
    {
        $skuInfo = $this->getPipelinePrepareData($key)[$result['sku_id']] ?? [];
        $result['product_id'] = $skuInfo['product_id'] ?? 0;
        $result['product_no'] = $skuInfo['sku_code'] ?? '';
        $result['sku_info']   = $skuInfo;
    }
    
    protected function mapCalculateGrossMargin(array &$result, $key, array $data)
    {
        $exchangeRate    = floatval($result['order_info']['exchange_rate'] ?? 0);
        $exchangeRateUsd = floatval($result['order_info']['exchange_rate_usd'] ?? 0);
        $grossMargin     = floatval($result['gross_margin'] ?? 0);
        $grossMarginCny  = floatval($result['gross_margin_cny'] ?? 0);
        $grossMarginUsd  = floatval($result['gross_margin_usd'] ?? 0);
        if (empty($grossMargin)) {
            return;
        }
        if (empty($grossMarginCny)) {
            $result['gross_margin_cny'] = round($grossMargin * $exchangeRate / 100, 4);
        }
        if (empty($grossMarginUsd)) {
            $result['gross_margin_usd'] = round($grossMargin * $exchangeRateUsd / 100, 4);
        }
    }
    
    protected function mapMergeAllExternalField(array &$result, $key, array $data)
    {
        // 合并订单和产品的自定义字段
        foreach (($result['order_info'] ?? []) as $field => $orderItem) {
            if (is_numeric($field)) {
                $result[$field] = $orderItem;
                unset($result['order_info'][$field]);
            }
        }
        $orderExternalFields2 = $result['order_info']['external_field_data'] ?? [];
        unset($result['order_info']['external_field_data']);
        $result['external_field_data'] = $result['external_field_data'] + $orderExternalFields2;
    }

    protected function buildFieldUnit($key, array $data)
    {
        $customFieldList = new FieldList($this->clientId);
        $customFieldList->setType(\Constants::TYPE_ORDER);
        $customFieldList->setEnableFlag(\Constants::ENABLE_FLAG_TRUE);
        $customFieldList->setIsList(1);
        $customFieldList->setFields(['id', 'name', 'field_type', 'relation_field', 'relation_field_type', 'relation_origin_type','relation_origin_field','relation_origin_field_type']);
        $fieldMapList = $customFieldList->find();
        $fieldMap = array_column($fieldMapList, null, 'id');
        return $fieldMap;
    }

    protected function mapFieldUnit(array &$result, $key, array $data)
    {
        /*
            1. 包装体积carton_volume: 'm³',
            2. 单箱毛重carton_gross_weight: 'kg',
            3. 单箱净重carton_net_weight: 'kg',
            4. 包装体积package_volume: 'm³',
            5. 包装毛重package_gross_weight: 'kg',
            6. 产品体积product_volume: 'm³',
            7. 产品净重product_net_weight: 'kg',
            8. 包装毛重小计package_gross_weight_subtotal: 'kg',
            9. 包装体积小计package_volume_subtotal: 'm³',
            10. 增值税率vat_rate: '%',
            11. 退税率tax_refund_rate: '%',
         */
         $unitMap = [
            'carton_volume'                 => 'm³',
            'carton_gross_weight'           => 'kg',
            'carton_net_weight'             => 'kg',
            'package_volume'                => 'm³',
            'package_gross_weight'          => 'kg',
            'product_volume'                => 'm³',
            'product_net_weight'            =>'kg',
            'package_gross_weight_subtotal' => 'kg',
            'package_volume_subtotal'       => 'm³',
            'vat_rate'                      => '%',
            'tax_refund_rate'               => '%',
        ];
         $productFieldMap = $this->getPipelinePrepareData($key) ?? [];
         foreach ($unitMap as $fieldId => $unit) {
             if (isset($result[$fieldId]) && is_numeric($result[$fieldId])) {
                 $result[$fieldId] = round(floatval($result[$fieldId]), 4).$unit;
             }
         }
         $unitFields = array_keys($unitMap);
         foreach ($result as $fieldId => $fieldValue) {
             $fieldInfo = $productFieldMap[$fieldId] ?? [];
             if (empty($fieldInfo)) continue; 
             if (is_numeric($fieldId) 
             && array_intersect([$fieldInfo['relation_field'], $fieldInfo['relation_origin_field']], $unitFields)) {
                 if (is_numeric($result[$fieldId])) {
                     $unit = $unitMap[$fieldInfo['relation_origin_field']] ?? ($unitMap[$fieldInfo['relation_field']] ?? '');
                     $result[$fieldId] = round(floatval($result[$fieldId]), 4).$unit;
                 }
             }
         }
    }

    protected function buildPlatformProductSkuInfo($key, array $data)
    {
        $platformSkuIds = array_unique(array_column($data, 'platform_sku_id'));
        if (!empty($platformSkuIds)) {
            $platSkuApi = new PlatformSkuAPI($this->clientId);
            $platformProductSkuMap = array_column($platSkuApi->productSkusByIds($platformSkuIds, true), null,'platform_sku_id');
        }
        return $platformProductSkuMap;
    }

    protected function mapPlatformProductSkuInfo(array &$result, $key, array $data)
    {
        $platformProductSkuInfo = $this->getPipelinePrepareData($key)[$result['platform_sku_id']] ?? [];
        $result['platform_product_sku_info'] = $platformProductSkuInfo;
    }

    protected function buildPlatformProductInfo($key, array $data)
    {
        $platformProductIds = array_unique(array_column($data, 'platform_product_id'));
        if (!empty($platformProductIds)) {
            $platApi = new PlatformProductAPI($this->clientId);
            $filter = $platApi->buildFilter(['platform_product_id' => $platformProductIds]);
            $filter->select(['from_url', 'platform_product_id', 'third_product_id']);
            $platformProductMap = array_column($filter->rawData(), null,'platform_product_id');
        }
        return $platformProductMap ?? [];
    }

    protected function mapPlatformProductInfo(array &$result, $key, array $data)
    {
        $platformProductInfo = $this->getPipelinePrepareData($key)[$result['platform_product_id']] ?? [];
        $productNo = '';
        if (!empty($result['product_type'])) {
            $productNo = ($result['product_type'] == ProductConstant::PRODUCT_TYPE_SPU) ? ($result['sku_info']['product_no'] ?? '') : ($result['sku_info']['sku_code'] ?? '');
        } else {
            //组合产品的子产品 product_type = 0
            isset($result['product_no']) && $productNo = $result['product_no'];
        }
        // $result['product_no'] = $platformProductInfo['third_product_id'] ?? $productNo; //阿里产品，编号需要重新获取值
        $result['product_no'] = $productNo; //阿里产品，编号不需要重新获取值
        $result['platform_product_info'] = $platformProductInfo;
    }
    
    // 订单拆分采购-明细供应商信息
    public function displayProductToPurchaseByOrderProductInfo($argument = true)
    {
        $this->setting['product_to_purchase_by_order_product_info'] = [
            'argument'  => $argument,
            'scene' => OrderConstants::ORDER_INFO_SCENE_PUSH_DOWN_ORDER_SPLIT_PURCHASE,
            'column_key' => 'id',
        ];
    }

    public function displayOrderProductSnapshot($argument = true, array $orderIs = [])
    {
        $this->setting['order_product_snapshot'] = [
            'argument'  => $argument,
            'order_ids' => $orderIs,
        ];
    }

    public function buildOrderProductSnapshot($key, array $data)
    {
        $orderIds = $this->setting[$key]['order_ids'] ?? [];
        if (empty($orderIds)) return [];
        $orderProductFilter = new OrderFilter($this->clientId);
        $orderProductFilter->order_id = $orderIds;
        $orderProductFilter->select(['order_id', 'product_list']);
        $orders   = $orderProductFilter->rawData();
        $orders   = array_column($orders, null, 'order_id');
        $orderMap = [];
        foreach ($orders as $order) {
            $orderId = $order['order_id'];
            if (empty($order['product_list'])) continue;
            foreach ($order['product_list'] as $item) {
                if (!empty($item['unique_id']) && !empty($item['platform_product_info'])) {
                    $orderMap[$orderId][$item['unique_id']] = $item['platform_product_info'];
                }
            }
        }
        return $orderMap;
    }

    public function mapOrderProductSnapshot(array &$result, $key, array $data)
    {
        $id = $result['id'] ?? 0;
        $orderId = $result['refer_id'] ?? 0;
        $productSnapshot = $this->getPipelinePrepareData($key)[$orderId][$id] ?? [];
        if (!empty($productSnapshot)) {
            $result['product_snapshot']['ali_product_id']      = $productSnapshot['ali_product_id'] ?? 0;
            $result['product_snapshot']['platform_product_id'] = $productSnapshot['platform_product_id'] ?? 0;
            $result['product_snapshot']['platform_sku_id']     = $productSnapshot['platform_sku_id'] ?? 0;
            $result['product_snapshot']['product_name']        = $productSnapshot['product_name'] ?? '';
            $result['product_snapshot']['product_model']       = $productSnapshot['product_model'] ?? '';
            $result['product_snapshot']['product_images']      = $productSnapshot['product_images'] ?? [];
            $result['product_snapshot']['sku_attributes']      = $productSnapshot['sku_attributes'] ?? [];
        }
    }

    protected function buildInvoiceInfo($key, array $data)
    {
    }

    protected function mapInvoiceInfo(array &$result, $key, array $data)
    {
    }


    protected function buildCombineProduct($key, array $data)
    {
        $combineRecordMap = [];
        $combineRecordIds = array_unique(array_filter(array_column($data, 'combine_record_id')));
        if (!empty($combineRecordIds)) {
            $filter = new OrderProductFilter($this->clientId);
            $filter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
            $filter->id = $combineRecordIds;
            $filter->select(['product_id','id', 'combine_record_id','record_name', 'description', 'product_remark', 'external_field_data', 'sku_attributes', 'package_remark']);
            $combineRecordList = $filter->rawData();

            $productIds = array_column($combineRecordList, 'product_id');
            $productMap = [];
            if ($productIds) {
                $productFilter = new ProductFilter($this->clientId);
                $productFilter->product_id = $productIds;
                $productFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
                $productFilter->select(['product_id', 'product_no']);
                $productMap = array_column($productFilter->rawData(), 'product_no', 'product_id');
            }

            foreach ($combineRecordList as $key => $combineRecordItem) {
                $combine_product_id = $combineRecordItem['product_id'];
                $combineRecordList[$key]['combine_product_id'] = $combine_product_id;
                $combineRecordList[$key]['combine_product_no'] = empty($combine_product_id) ? '' : $productMap[$combine_product_id] ?? '';
            }
            $combineRecordMap = array_column($combineRecordList, null, 'id');
        }
        return $combineRecordMap;
    }

    protected function mapCombineProduct(array &$result, $key, array $data)
    {
        $map = $this->getPipelinePrepareData($key);
        $combineRecord = $map[$result['combine_record_id']] ?? [];
        $isSubProduct = empty($combineRecord) ? 0 : 1;
        $result['is_sub_product'] = $isSubProduct;
        $result['combine_product_id'] = $isSubProduct ? $combineRecord['combine_product_id'] : 0;
        $result['combine_product_no'] = $isSubProduct ? $combineRecord['combine_product_no'] : '';
    }

    public function buildShippingRecordInfo($key, array $data)
    {
        $orderIds = array_column($data, 'refer_id');
        $uniqueIds = array_column($data, 'id');
        $spInvoiceFilter = new ShippingInvoiceFilter($this->clientId);
        $spInvoiceFilter->order_ids = new InArray($orderIds);
        $spInvoiceFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $spInvoiceFilter->shipping_status = new NotIn([OmsConstant::SHIPPING_INVOICE_STATUS_DISCARD]);
        $spRecordFilter = new ShippingRecordFilter($this->clientId);
        $spRecordFilter->filterSubProduct();
        $spRecordFilter->invoice_product_id = $uniqueIds;
        $spRecordFilter->order_id =  $orderIds;
        $spRecordFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $spRecordFilter->select(['invoice_product_id','shipping_count' => function ($column, $table) {
            return "sum($table.$column) as  have_shipping_count";
        }]);
        $spInvoiceFilter->initJoin()
            ->leftJoin($spRecordFilter)
            ->on('shipping_invoice_id','shipping_invoice_id')
            ->joinGroupBy('invoice_product_id', $spRecordFilter->getTableName());

        $spInvoiceList = $spInvoiceFilter->rawData();
        $shippingRecordMap = array_column($spInvoiceList,'have_shipping_count','invoice_product_id');

        return $shippingRecordMap;
    }

    public function mapShippingRecordInfo(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['have_shipping_count'] = $map[$result['id']] ?? 0;
        $result['todo_shipping_count'] = max(($result['count'] - $result['have_shipping_count']), 0);
    }

    public function buildSubProduct($key, array $data){

    }

    public function buildToBePurchaseCount($key, array $data)
    {
        $orderIds = array_column($data, 'refer_id');
        if (empty($orderIds)) {
            return [];
        }
        $purchaseOrderProductFilter = new PurchaseOrderProductFilter($this->clientId);
        $purchaseOrderProductFilter->order_id = $orderIds;
        $purchaseOrderProductFilter->enable_flag = \Constants::ENABLE_FLAG_TRUE;
        $purchaseOrderProductFilter->select(['invoice_product_id', 'all_purchase_count' => function ($column) {
            return "sum(count) as all_purchase_count";
        }]);
        $purchaseOrderProductFilter->groupBy('invoice_product_id');
        $hasPurchaseProductCountMap = array_column($purchaseOrderProductFilter->rawData(), 'all_purchase_count', 'invoice_product_id');;

        return $hasPurchaseProductCountMap;
    }

    public function mapToBePurchaseCount(&$result, $key)
    {
        $map = $this->getPipelinePrepareData($key);
        $result['has_purchase_count'] = $map[$result['id']] ?? 0;
        $result['to_purchase_count'] = ProductTransferHelper::formatTransferCount($result['count'] - $result['has_purchase_count']);   //待采购数量
        if($result['to_purchase_count'] < 0){
            $result['to_purchase_count'] = 0;
        }
    }

    public function buildProductImageInfoList($key, array $data)
    {
        // todo @young 暂时使用产品库图片，需要改造为订单明细图片
        $skuIds = array_unique(array_column($data, 'sku_id'));
        if (!empty($skuIds)) {
            $skuApi = new SkuAPI($this->clientId);
            $productSkuMap = array_column($skuApi->items($skuIds), null,'sku_id');
        }
        // 获取产品的图片
        $productImageIds = [];
        foreach (array_column($data, 'product_images') as $images) {
            foreach ($images as $image) {
                $productImageIds[] = $image['file_id'] ?? 0;
            }
        }
        $imgId2InfoMap = [];
        if (!empty($productImageIds)) {
            $productImageIds = Arr::uniqueFilterValues($productImageIds);
            $imgId2InfoMap = \common\library\file\Helper::fileUrlMap($productImageIds);
        }
        if (!empty($imgId2InfoMap)) {
            $productSkuMap['imgId2InfoMap'] = $imgId2InfoMap;
        }
        return $productSkuMap ?? [] ;
    }

    public function mapProductImageInfoList(&$result, $key)
    {
        $skuInfo       = $this->getPipelinePrepareData($key)[$result['sku_id']] ?? [];
        $imgId2InfoMap = $this->getPipelinePrepareData($key)['imgId2InfoMap'] ?? [];
        $result['product_image'] = [];
        if (!empty($result['product_images'][0]['file_id'])) {
            $imageInfo = $imgId2InfoMap[$result['product_images'][0]['file_id']] ?? [];
            if (!empty($imageInfo)) {
                $imageInfo = [array_merge($imageInfo, $result['product_images'][0])];
                $result['product_image'] = $imageInfo;
            }
        }
        if (empty($result['product_image']) && !empty($skuInfo['image_info'])) {
            $result['product_image'] = [array_merge($skuInfo['image_info'],
                ['user_id' => (string)($result['user_id'][0] ?? 0) , 'create_time' => xm_function_now()])
            ];
        }
    }

}