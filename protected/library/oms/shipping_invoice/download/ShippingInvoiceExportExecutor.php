<?php
/**
 * Created by PhpStorm.
 * User: amu
 * Date: 2021/7/28
 * Time: 2:45 下午
 */

namespace common\library\oms\shipping_invoice\download;


use common\library\APIConstant;
use common\library\CommandRunner;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\export_v2\ExportConstants;
use common\library\export_v2\ExportExecutor;
use common\library\export_v2\ExportFile;
use common\library\io_handler\downloader\DownloadTask;
use common\library\io_handler\IOTaskPool;
use common\library\object\field\FieldConstant;
use common\library\oms\common\OmsConstant;
use common\library\oms\invoice_export\InvoiceExportFieldFormatter;
use common\library\oms\shipping_invoice\ShippingInvoiceApi;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;

class ShippingInvoiceExportExecutor extends ExportExecutor
{
    protected $shippingInvoiceBaseFields = [];
    protected $shippingInvoiceRecordFields = [];
    protected $exportRowCount = 0;
    protected $customImgIds = [];
    protected string $error = '';

    use InvoiceExportFieldFormatter;

    public function runAsync()
    {
        CommandRunner::run('shippingInvoice', 'export', [
            'user_id' => $this->export->user_id,
            'export_id' => $this->export->export_id
        ], '/tmp/shipping_invoice_export.log');
    }

    public function access()
    {
        if (!$this->export->hasExportAccess(PrivilegeConstants::PRIVILEGE_CRM_SHIPPING_INVOICE_DOWNLOAD)) {
            throw new \RuntimeException('当前用户无下载出运单权限');
        }
    }

    private function getCustomImgField()
    {
        $fieldList = new FieldList($this->getClientId());
        $fieldList->setFields(['id','relation_origin_field_type','field_type']);
        $fieldList->setType(\Constants::TYPE_SHIPPING_INVOICE);
        $fieldList->setDisableFlag(false);
        $fieldList =  $fieldList->find();
        $ids = array_values(array_map(function ($item) {
            return $item['id'];
        }, (array)array_filter($fieldList, function ($item) {
            return $item['relation_origin_field_type'] == CustomFieldService::FIELD_TYPE_IMAGE || $item['field_type'] == CustomFieldService::FIELD_TYPE_IMAGE ;
        })));
        $this->customImgIds = $ids;
        return $ids;
    }


    public function export()
    {
        $this->initExportFile();

        try {
            $customImgField = $this->getCustomImgField();
            $data = $this->getExportData();
            $dir = '/tmp/shipping_product_download_img' . $this->export->export_id. time();
            $this->exportFile->setImgDir($dir);
            $imgFieldMap = $this->downloadImgFromList($data, $customImgField);
            $imgFieldMap = $this->batchDownloadExportImage($imgFieldMap);
            $this->exportFile->setImgFieldMap($imgFieldMap);
            $this->exportFile->setImgField($customImgField);
            $this->exportFile->setData($data);
            $tempFilePath = $this->exportFile->getNewWriter()->writer();
            $upload = $this->uploadExportFile($tempFilePath);
            $data = [
                'result_file_id' => $upload->getFileId(),
                'finish_time' => xm_function_now(),
                'total' => $this->exportRowCount
            ];
            $this->export->getOperator()->success($data);

        } catch (\Throwable $throwable) {
            \LogUtil::info('Error in writing Excel taskId:' . $this->export->export_id . "| error with file : " . $throwable->getFile() . " | Line : " . $throwable->getLine() . " | Msg : " . $throwable->getMessage());
            $this->error = 'Error in writing Excel | error with file : ' . $throwable->getFile() . " | Line : " . $throwable->getLine() . " | Msg : " . $throwable->getMessage();
        } finally {
            if (!empty($dir)) {
                $this->delTemporaryDir($dir);
            }
            if (isset($tempFilePath) && is_file($tempFilePath)) {
                unlink($tempFilePath);
            }
        }

        return true;
    }

    public function initExportFile()
    {
        $this->exportFile = new ExportFile();
        $this->exportFile->setFileHeader($this->getExportFileHeader());
        $this->exportFile->setFileName($this->getExportFilename());
        $this->exportFile->setFileType($this->export->file_type);
    }

    protected function getCostItemFieldName()
    {
        $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER);
        foreach ($costItemInvoiceRelationList as &$item){
            $item['item_name'] = strtr($item['item_name'], ['=' => '/=']);
        }
        return array_column($costItemInvoiceRelationList, null, 'relation_id');
    }

    public function getExportFileHeader()
    {
        static $headerFieldName;
        if (is_null($headerFieldName)) {
            $fields = \common\library\customer\Helper::getFullFieldList($this->export->client_id, $this->export->user_id, $this->export->type);
            $fields = array_merge($fields['fields'] ?? [], $fields['extra'] ?? []);
            if (empty($fields)) {
                throw new \RuntimeException('获取导出文件头部字段错误');
            }

            foreach ($fields as &$field) {
                if (($field['group_id'] ?? CustomFieldService::SHIPPING_INVOICE_GROUP_BASIC) == CustomFieldService::SHIPPING_INVOICE_GROUP_PRODUCT_BASE) {
                    $this->shippingInvoiceRecordFields[] = $field['id'];
                } else {
                    $this->shippingInvoiceBaseFields[] = $field['id'];
                }

                if (!empty($field['suffix'])) {
                    $field['name'] = $field['name'] . ' [' . $field['suffix'] . ']';
                }
            }


            $fieldsMap = array_column($fields, 'name', 'id');

            $headerFieldName = [];
            unset($field);
            foreach ($this->export->params['fields'] as $field) {
                //附加款项
                if ($field == 'extra_cost_item') {
                    $costItemRelationMap = $this->getCostItemFieldName();
                    if ($costItemRelationMap) {
                        foreach ($costItemRelationMap as $key => $item) {
                            //放到base字段中
                            $this->shippingInvoiceBaseFields[] = $item['relation_id'];
                            $headerFieldName[$key] = $item['item_name'];
                        }
                    }
                    continue;
                }

                !empty($fieldsMap[$field]) && $headerFieldName[$field] = $fieldsMap[$field];
            }

        }

        return $headerFieldName;
    }

    public function getExportFilename()
    {
        static $filename = null;

        if (is_null($filename)) {
            $str = \Yii::t('field', '出运单');
            $prefix = !empty($this->export->params['query_params']['show_product']) ? '详细版' : '简洁版';
            $prefix = \Yii::t('field', $prefix);
            $filename = $prefix . $str . date('YmdHis') . '.xlsx';
        }
        return $filename;
    }


    public function getExportData()
    {
        ini_set("memory_limit", "4096M");
        $params = $this->export->params['query_params'];
        $shipping_invoice_ids = implode(",", $params['shipping_invoice_ids'] ?? []);
        $shipping_invoice_no = $params['shipping_invoice_no'] ?? null;
        $shipping_voucher_no = $params['shipping_voucher_no'] ?? null;
        $order_no = $params['order_no'] ?? null;
        $forwarder_name = $params['forwarder_name'] ?? null;
        $forwarder_contact_name = $params['forwarder_contact_name'] ?? null;
        $company_name = $params['company_name'] ?? null;
        $customer_name = $params['customer_name'] ?? null;
        $tracking_status = $params['tracking_status'] ?? [];
        $outbound_status = $params['outbound_status'] ?? [];
        $shipping_status = $params['shipping_status'] ?? [];
        if (!is_array($shipping_status)) {
            $shipping_status = [$shipping_status];
        }
        $handler = $params['handler'] ?? [];
        $company_id = $params['company_id'] ?? [];
        $customer_id = $params['customer_id'] ?? [];
        $order_ids = $params['order_ids'] ?? [];
        $create_user = $params['create_user'] ?? [];
        $update_user = $params['update_user'] ?? [];

        $scene = APIConstant::SCENE_DOWNLOAD;
        $currency = $params['currency'] ?? null;

        $shipping_deadline_start = $params['shipping_deadline_start'] ?? null;
        $shipping_deadline_end = $params['shipping_deadline_end'] ?? null;
        $shipping_time_start = $params['shipping_time_start'] ?? null;
        $shipping_time_end = $params['shipping_time_end'] ?? null;
        $arrival_time_start = $params['arrival_time_start'] ?? null;
        $arrival_time_end = $params['arrival_time_end'] ?? null;
        $finish_time_start = $params['finish_time_start'] ?? null;
        $finish_time_end = $params['finish_time_end'] ?? null;
        $create_time_start = $params['create_time_start'] ?? null;
        $create_time_end = $params['create_time_end'] ?? null;
        $update_time_start = $params['update_time_start'] ?? null;
        $update_time_end = $params['update_time_end'] ?? null;
        $sort_field = $params['sort_field'] ?? 'create_time';
        $sort_type = $params['sort_type'] ?? 'desc';
        $page_size = 100;
        $page_no = 1;
        $client_id = $this->getClientId();
        $data = (new ShippingInvoiceApi($client_id, $this->getUserId()))->getList(
            $shipping_invoice_ids,
            $shipping_invoice_no,
            $shipping_voucher_no,
            $order_no,
            $forwarder_name,
            $forwarder_contact_name,
            $company_name,
            $customer_name,
            $tracking_status,
            $outbound_status,
            $shipping_status,
            $handler,
            $company_id,
            $customer_id,
            $order_ids,
            $create_user,
            $update_user,
            $scene,
            $currency,
            $shipping_deadline_start,
            $shipping_deadline_end,
            $shipping_time_start,
            $shipping_time_end,
            $arrival_time_start,
            $arrival_time_end,
            $finish_time_start,
            $finish_time_end,
            $create_time_start,
            $create_time_end,
            $update_time_start,
            $update_time_end,
            $sort_field,
            $sort_type,
            $page_size,
            $page_no
        );
        $this->exportRowCount = $data['count'] ?? 0;
        $list = $data['list'] ?? [];
        //数据映射
        $dataMap = [];

        $total = $data['count'] ?? 0;
        $maxPage = ceil($total / $page_size);
        while ($page_no <= $maxPage) {

            //list处理
            foreach ($list as $datum) {
                //隐藏字段处理
                $disableFields = $datum['field_privilege_stats'][0]['disable'] ?? [];

                if (!empty($datum['cost_list'])) {
                    foreach ($datum['cost_list'] as $cost) {
                        $id = $cost['cost_item_relation_id'] ?? -1;
                        $amount = $cost['cost'];
                        if (!isset($datum[$id])) {
                            $datum[$id] = $amount;
                        } else {
                            $datum[$id] += $amount;
                        }
                    }
                }
                $temp = [];
                foreach ($this->getExportFileHeader() as $field => $name) {

                    //隐藏字段，下载时为空数据处理
                    if (in_array($field, $disableFields)) {
                        $temp[$field] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
                        continue;
                    }

                    switch ($field) {
                        case 'create_user':
                            $temp[$field] = $datum['create_user_info']['nickname'] ?? '';
                            break;
                        case 'update_user':
                            $temp[$field] = $datum['update_info']['nickname'] ?? '';
                            break;
                        case 'arrival_time':
                        case 'finish_time':
                        case 'shipping_time':
                        case 'shipping_deadline':
                            if ($datum[$field] == '1970-01-01') {
                                $temp[$field] = '';
                            } else {
                                $temp[$field] = $datum[$field];
                            }
                            break;
                        case 'shipping_status':
                            $temp[$field] = OmsConstant::SHIPPING_INVOICE_SUPPORT_CHANGE_STATUS_MAP[$datum[$field]] ?? '';
                            break;
                        case 'outbound_status':
                            $temp[$field] = OmsConstant::OUTBOUND_INVOICE_STATUS_MAP[$datum[$field]] ?? '';
                            break;
                        case 'handler':
                            $temp[$field] = implode(',', array_column($datum['handler_info'] ?? [], 'nickname'));
                            break;
                        case 'company_id':
                            $temp[$field] = $datum['company_info']['name'] ?? '';
                            break;
                        case 'customer_id':
                            $temp[$field] = $datum['customer_info']['name'] ?? '';
                            break;
                        case 'forwarder_id':
                            $temp[$field] = $datum['forwarder_info']['name'] ?? '';
                            break;
                        case 'forwarder_contact_id':
                            $temp[$field] = $datum['forwarder_contact_info']['contact_name'] ?? '';
                            break;
                        case 'order_ids':
                            $temp[$field] = implode(',', array_column($datum['order_infos'] ?? [], 'order_no'));
                            break;
                        case 'shipping_information':
                            $temp[$field] = $datum['shipping_voucher_no'] ?? '';
                            break;
                        case 'shipping_cycle':
                            if (($datum['shipping_time'] ?? '') != '1970-01-01' && ($datum['arrival_time'] ?? '') != '1970-01-01') {
                                $shipping_time = strtotime($datum['shipping_time']);
                                $arrival_time = strtotime($datum['arrival_time']);
                                $diff = $arrival_time - $shipping_time;
                                $temp[$field] = floor($diff / (60 * 60 * 24));
                            } else {
                                $temp[$field] = 0;
                            }
                            break;
                        default:
                            $temp[$field] = $datum[$field] ?? '';
                            if (is_array($temp[$field] ?? '')) {
                                if (in_array($field, $this->customImgIds)) {
                                    if (!empty($temp[$field][0]['file_path'])) {
                                        $temp[$field] = [$temp[$field][0]['file_path']];
                                    } else if (!empty($temp[$field])) {
                                        $temp[$field] = [$temp[$field]];
                                    }
                                }

                                if (!$this->canImplodeArray($temp[$field] ?? '')) {
                                    $temp[$field] = [];
                                }
                                $temp[$field] = implode(",", $temp[$field]);
                            }
                    }

                    //产品字段处理
                    if (in_array($field, $this->shippingInvoiceRecordFields)) {
                        unset($temp[$field]);
                        $productList = $datum['record_list'] ?: [];
                        foreach ($productList as $product) {

                            switch ($field) {
                                case 'sku_attributes':
                                    $skuStr = [];
                                    if (!empty($product[$field] ?? [])) {
                                        foreach ($product[$field] as $attr) {
                                            $skuStr[] = ($attr['item_name'] ?? '') . ': ' . ($attr['value']['item_name'] ?? '');
                                        }
                                    }
                                    $skuStr = implode(";\n", $skuStr);
                                    $product[$field] = $skuStr;
                                    break;
                                case 'carton_size':
                                    $product[$field] = $this->formatCartonSizeField($product[$field] ?? []);
                                    break;
                                case 'product_size':
                                    $product[$field] = $this->formatProductSizeField($product[$field] ?? []);
                                    break;
                                case 'package_size':
                                    $product[$field] = $this->formatPackageSizeField($product[$field] ?? []);
                                    break;
                                case 'product_image':
                                    $product[$field] = $product['sku_product_image_info'] ?? [];
                                    break;
                            }
                            if (is_array($product[$field] ?? '')) {
                                if (in_array($field, $this->customImgIds)) {
                                    if (!empty($product[$field][0]['file_path'])) {
                                        $product[$field] = [$product[$field][0]['file_path']];
                                    } else if (!empty($product[$field])) {
                                        $product[$field] = [$product[$field]];
                                    }
                                }
                                if (!$this->canImplodeArray($product[$field] ?? '')) {
                                    $product[$field] = [];
                                }
                                $product[$field] = implode(",", $product[$field]);
                            }

                            $temp['product_list'][$product['shipping_record_id']][$field] = $product[$field] ?? '';
                        }
                    }
                }
                $dataMap[] = $temp;
            }


            $page_no++;
            $data = (new ShippingInvoiceApi($client_id, $this->getUserId()))->getList(
                $shipping_invoice_ids,
                $shipping_invoice_no,
                $shipping_voucher_no,
                $order_no,
                $forwarder_name,
                $forwarder_contact_name,
                $company_name,
                $customer_name,
                $tracking_status,
                $outbound_status,
                $shipping_status,
                $handler,
                $company_id,
                $customer_id,
                $order_ids,
                $create_user,
                $update_user,
                $scene,
                $currency,
                $shipping_deadline_start,
                $shipping_deadline_end,
                $shipping_time_start,
                $shipping_time_end,
                $arrival_time_start,
                $arrival_time_end,
                $finish_time_start,
                $finish_time_end,
                $create_time_start,
                $create_time_end,
                $update_time_start,
                $update_time_end,
                $sort_field,
                $sort_type,
                $page_size,
                $page_no
            );

            $list = $data['list'] ?? [];
        }


        if (($this->export->params['query_params']['download_format'] ?? ExportConstants::FORMAT_PRODUCT_MERGE) == ExportConstants::FORMAT_PRODUCT_SINGLE) {
            $resData = [];
            foreach ($dataMap as $item) {
                $productList = $item['product_list'] ?? [];
                unset($item['product_list']);

                if (empty($productList)) {
                    $resData[] = $item;
                    continue;
                }

                foreach ($productList as $productItem) {
                    $row = $item;
                    foreach ($productItem as $productField => $fieldValue) {
                        $row[$productField] = $fieldValue;
                    }
                    $resData[] = $row;
                }
            }

            return $resData;
        }

        return $dataMap;
    }

    public function uploadExportFile($tempFilePath)
    {
        return $upload = \UploadService::uploadRealFile($tempFilePath, $this->getExportFilename(), \UploadService::getFileKey($tempFilePath));
    }

    private function downloadImgFromList(array &$data, array $customImgField)
    {
        $imgFieldMap = [];
        //这里直接遍历就好
        foreach ($data as &$item) {
            foreach ($customImgField as $fieldId) {
                $imgUrl = $item[$fieldId] ?? '';
                if (!empty($imgUrl)) {
                    $urlMd5 = md5($imgUrl);
                    if (!isset($imgFieldMap[$urlMd5])) {
                        $imgFieldMap[$urlMd5] = $imgUrl;
                    }
                    $item[$fieldId] = $urlMd5;
                }
            }

            $product_list = $item['product_list'] ?? [];
            foreach ($product_list as $key => &$datum) {
                // 收集产品列表相关图片url 重复不下载
                foreach ($customImgField as $fieldId) {
                    $imgUrl = $datum[$fieldId] ?? '';
                    if (!empty($imgUrl)) {
                        $urlMd5 = md5($imgUrl);
                        if (!isset($imgFieldMap[$urlMd5])) {
                            $imgFieldMap[$urlMd5] = $imgUrl;
                        }
                        $datum[$fieldId] = $urlMd5;
                    }
                }
            }
            $item['product_list'] = $product_list;
        }
        return $imgFieldMap;

    }

    //协程调用
    protected function batchDownloadExportImage($images)
    {     // $urls是个关联数组，key是图片的唯一id
        $result = [];
        if (empty($images)) {
            return $result;
        }
        $targetDir = $this->exportFile->getImgDir();
        if (!is_dir($targetDir)) {
            mkdir($targetDir);
        }
        $concurrentNum = 20;
        $taskPool = new IOTaskPool($concurrentNum, $this->export->export_id);
        $taskPool->setAsync(false);     // 可自由切换串行或并发下载
        foreach ($images as $uniqueId => $imageUrl) {
            $task = new DownloadTask($imageUrl, $targetDir);
            $task->setTaskId($uniqueId);
            $task->setNeedSize(true);
            $taskPool->addTask($task);
        }
        $taskPool->batchRun();
        foreach ($taskPool->getTasks() as $task) {
            $uniqueId = $task->getTaskId();
            $result[$uniqueId] = $task->getResult();
        }
        \LogUtil::info("batchDownloadExportImage download end");
        \LogUtil::info("{$this->export->export_id}  res msg " . json_encode($result));
        return $result;
    }

    protected function delTemporaryDir($temporaryDir)
    {
        if (empty($temporaryDir) || !is_dir($temporaryDir)) {
            return;
        }

        $dh = opendir($temporaryDir);
        while (($fn = readdir($dh)) !== false) {
            if ('.' == $fn || '..' == $fn) {
                continue;
            }
            $fp = rtrim($temporaryDir, '/') . '/' . $fn;
            unlink($fp);
        }
        rmdir($temporaryDir);
    }

    function canImplodeArray($array): bool
    {
        // 检查数组的维度
        if (!is_array($array)) {
            return false;
        }
        // 检查数组元素的类型
        foreach ($array as $element) {
            if (is_array($element)) {
                return false;
            }
        }

        return true;
    }

}
