<?php
namespace common\library\invoice\export;

use common\library\oms\warehouse_return_invoice\purchase\PurchaseReturnInvoice;

/**
 * Created by PhpStorm.
 * User: onegong
 * Date: 22/10/12
 * Time: 上午11:20
 */
class PurchaseReturnExportFile extends AbstractExportFile
{
    /**
     * @var PurchaseReturnInvoice
     */
    protected $invoice;

    /**
     * @param $id
     */
    public function loadInvoiceById($id)
    {
        $invoice = new PurchaseReturnInvoice($this->operator->getClientId(), $id);
        $this->invoice = $invoice;
    }

    /**
     * @param  $no
     */
    public function loadInvoiceByNo($no)
    {
        $invoice = new PurchaseReturnInvoice($this->operator->getClientId());
        $invoice->loadByNo(\Constants::TYPE_PURCHASE_RETURN_INVOICE, $no);
        $this->invoice = $invoice;
    }

    protected function getReferType(){
        return \Constants::TYPE_PURCHASE_RETURN_INVOICE;
    }
}