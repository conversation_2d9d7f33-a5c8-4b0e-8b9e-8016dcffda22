<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/13
 * Time: 11:30 AM
 */

namespace common\library\alibaba\services\queue;


use common\library\account\Client;
use common\library\alibaba\Constant;
use Exception;
use LogUtil;

abstract  class BaseHandler
{
    protected $userId;
    protected $clientId;
    protected $data;
    protected $processData = [];

    /**
     * @var ClientLimiter
     */
    protected $limiter;

    abstract public function process();

    public function addProcessData($key, $value)
    {
        $this->processData[$key] = $value;
    }

    public function initData($data)
    {
        $this->data = $data;
        $this->clientId = $data['client_id'];
        $this->userId = $data['user_id'];
    }

    /**
     * @param mixed $clientId
     */
    public function setClientId($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @param ClientLimiter $limiter
     */
    public function setLimiter( $limiter)
    {
        $this->limiter = $limiter;
    }



    protected function switchLogin()
    {
        //清理静态缓存
        \User::cleanUserMap();
        Client::cleanCacheMap();
        \ProjectActiveRecord::resetConnection();
        \PgActiveRecord::resetConnection();

        if( !$this->userId )
        {
            throw new \ProcessException('需要配置操作的用户 client_id:'.$this->clientId);
        }

        \User::setLoginUserById($this->userId, $this->clientId);

    }

    protected function before()
    {
        if( $handler = $this->getLimiterHandler() )
        {
            \LogUtil::info("[checkLimit] 被限制不执行, 回调中处理 {$this->clientId} {$this->userId} ".json_encode($this->data) . ' handler ' . get_class($handler));
            $runHandlerRes = $this->limiter->runHandler($handler, $this->data);
            if (!$runHandlerRes) {
                \LogUtil::info("[checkLimit] 被限制不执行, 回调处理失败 {$this->clientId} {$this->userId} ".json_encode($this->data) . ' handler ' . get_class($handler));
            }
            return false;
        }

        return true;
    }

    public function getLimiterHandler()
    {
        if( !$this->clientId || !$this->limiter ||  !$this->limiter->enable )
        {
            return false;
        }

       return  $this->limiter->getMatchHandler($this->clientId);
    }

    /**
     * 检查客户是否断约
     *
     * @return bool 断约超过7天返回false，正常情况、断约7天内返回true
     * @throws Exception
     */
    public function checkClientExpire(): bool
    {
        if ($this->clientId && Client::getClient($this->clientId)->getExpiredDays() > Constant::STOP_SYNC_DAYS) {
            // 过期超过7天
            LogUtil::error("Client: $this->clientId Client过期超过7天停止同步");
            return false;
        }

        return true;
    }

    public function run()
    {
        try {
            $this->switchLogin();

            if( !$this->before())
            {
                return false;
            }

            $this->process();

            $this->after();

        } finally {
            $this->saveMessage();
            $this->clean();
        }

        return true;

    }


    protected function after()
    {

    }

    protected function saveMessage()
    {

    }

    public function clean()
    {
        \User::cleanUserMap();
        Client::cleanCacheMap();

        /** @var \CDbConnection $db */
        $db = \Yii::app()->db;
        $db->setActive(false);

        if( $this->clientId )
        {
            \ProjectActiveRecord::releaseDbByClientId($this->clientId);
            \PgActiveRecord::releaseDbByClientId($this->clientId);
        }
    }
}
