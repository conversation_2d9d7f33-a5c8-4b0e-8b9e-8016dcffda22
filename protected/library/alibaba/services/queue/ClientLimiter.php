<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/16
 * Time: 5:59 PM
 */

namespace common\library\alibaba\services\queue;


use common\library\GreyEnvHelper;

class ClientLimiter
{
    public $enable = false;

    /**
     * @var array LimiterHandlerAbstract
     */
    protected $handlers = [];

    /**
     * ClientLimiter constructor.
     * @param bool $enable
     */
    public function __construct(bool $enable)
    {
        $this->enable = $enable;
    }

    public function addHandler(LimiterHandlerAbstract $handler)
    {
        $this->handlers[] = $handler;
    }

    public function getAllHandler()
    {
        return $this->handlers;
    }

    public function removeHandler($handlerClass)
    {
        foreach ($this->handlers as $key => $handler) {
            if ($handler instanceof $handlerClass) {
                unset($this->handlers[$key]);
            }
        }
    }

    public function getMatchHandler($clientId)
    {
        if (!$this->enable)
            return false;

        if (empty($this->handlers)) {
            return false;
        }


        /**
         * @var $handler LimiterHandlerAbstract
         */
        foreach ($this->handlers as $handler) {
            if (!$handler->pass($clientId)) {
                return $handler;
            }
        }

        return false;
    }

    /**
     * @param $handler LimiterHandlerAbstract
     * @param $data
     * @return bool
     */
    public function runHandler($handler, $data)
    {
        if ($handler) {
            return $handler->handle($data) ?: true;
        }

        return false;
    }

}
