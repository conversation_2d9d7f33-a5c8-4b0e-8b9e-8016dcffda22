<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/13
 * Time: 11:31 AM
 */

namespace common\library\alibaba\services\queue;


use common\library\alibaba\Constant;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\alibaba\Helper;
use common\library\alibaba\store\AlibabaStore;
use common\library\lead\Lead;
use common\library\lead\LeadCustomer;
use common\library\setting\item\Api;

class LeadHandler extends BaseHandler
{

    protected $leadId;
    protected $leadCustomerId;
    protected $buyerAccountId;
    protected $sellerAccountId;

    public function initData($data)
    {
        $this->data = $data;
        $this->clientId = $data['client_id'];
        $this->userId = $data['user_id'];
        $this->leadId = $data['lead_id'];
        $this->leadCustomerId = $data['lead_customer_id'];
        $this->buyerAccountId = $data['buyer_account_id'];
        $this->sellerAccountId = $data['seller_account_id'];

        if( isset($data['current_retry_count']))
        {
            \LogUtil::info("init data retry {$data['current_retry_count']} data:".json_encode($data));
        }
    }

    public function process()
    {

        if( empty($this->userId) || $this->userId  != \User::getLoginUser()->getUserId() )
        {
            throw new \ProcessException('user_id 异常,需要检查');
        }

        \LogUtil::info("process client_id:{$this->clientId} user_id:{$this->userId} from_account_id:{$this->buyerAccountId}  seller_account_id:{$this->sellerAccountId} lead_id:{$this->leadId}  lead_customer_id:{$this->leadCustomerId}");
        $lead = new Lead($this->clientId,$this->leadId);
        if($lead->isNew()){
            \LogUtil::info("leadNotExist");
            return false;
        }
        $alibabaData = Helper::getBindAccountInfoBySellerId($this->sellerAccountId);
        if(!$alibabaData || $alibabaData['oauth_flag'] != Constant::OAUTH_FLAG_BIND_AND_AUTH)
        {
            if (empty($alibabaData['store_id']))
            {
                \LogUtil::info(" sellerAccount oauth status  Error: sellerAccountId: {$this->sellerAccountId}, oauth_flag:".($alibabaData['oauth_flag']??'')  ." from_account_id:{$this->buyerAccountId}");
                return false;
            }

            $alibabaStore = new AlibabaStore($this->clientId);
            $alibabaStore->loadByStoreId($this->clientId, $alibabaData['store_id']);
            $accessToken = $alibabaStore->isAuth() ? $alibabaStore->access_token:'';
            \LogUtil::info(" sellerAccount oauth fail get store auth : {$alibabaData['store_id']} {$this->sellerAccountId}, {$alibabaData['oauth_flag']}   from_account_id:{$this->buyerAccountId}");
        }else
        {
            $accessToken = $alibabaData['access_token'];
        }

        if($accessToken){
            //获取阿里买家信息
            $buyerInfo = CustomerSyncHelper::getAlibabaBuyerInfo($accessToken,$this->buyerAccountId);
        } else {
            $buyerInfo = [];
        }
        if(!$buyerInfo){
            \LogUtil::info("error seller_account_id: {$this->sellerAccountId}  noBuyerInfo accessToken:{$accessToken} oauth_flag {$alibabaData['oauth_flag']}  buyerAccount:{$this->buyerAccountId}");
            return false;
        }
        $customerList  = [];
        $lead->name = $lead->name?$lead->name:$buyerInfo['name'];
        $lead->company_name = $lead->company_name?$lead->company_name:$buyerInfo['company_name'];
        $lead->country = $lead->country?$lead->country:$buyerInfo['country'] ;
        $lead->province = $lead->province ? $lead->province:$buyerInfo['province'];
        $lead->city = $lead->city?$lead->city:$buyerInfo['city'];
        $lead->address = $lead->address?$lead->address:$buyerInfo['address'];
        $lead->fax = $lead->fax?$lead->fax:$buyerInfo['full_fax'];
        $lead->tel_full = $lead->tel_full?$lead->tel_full:$buyerInfo['fax'];
        $lead->homepage  = $lead->homepage?$lead->homepage:$buyerInfo['homepage'];

        $customer = (new LeadCustomer($this->clientId,$this->leadCustomerId));
        if(!$customer->isNew()){
            $customer->name = $customer->name? :$buyerInfo['name'];
            $customer->gender = $customer->gender?:$buyerInfo['gender'];
            $customer->contact = $customer->contact?:$buyerInfo['contact'] ;
            $customer->post =  $customer->post?:$buyerInfo['post'];
            $customer->main_customer_flag  =  $buyerInfo['main_customer_flag'];
            $customer->tel_list = $customer->tel_list?:$buyerInfo['tel_list'];

            if(!$customer->image_list && $buyerInfo['avatar_url']){
                $imageList=[];
                if(  $imageFileId = Helper::downloadImage($this->clientId,$alibabaData['store_id'], $buyerInfo['avatar_url']??''))
                {
                    $imageList[] = $imageFileId;
                }
                $customer->image_list = $imageList;
            }

	        $customer->growth_level = Api::growthLevel(\Constants::TYPE_COMPANY)->getExtraIdByName($buyerInfo['growth_level'] ?? '');

            $customerList[] = $customer;
            $lead->setCustomerList($customerList);
        }
        $res = $lead->save();
        \LogUtil::info("updateLeadRes  lead_id:{$this->leadId} lead_customer_id:{$this->leadCustomerId} res:{$res}");

    }
}
