<?php
/**
 * Created by PhpStorm.
 * User: june
 * Date: 2021/10/25
 * Time: 2:15 PM
 */

namespace common\library\alibaba\services;


class TableHelper
{

    //生成阿里消息日志表
    public static function createMessageTable($year)
    {

        $db = \Yii::app()->alibaba_message_db;

        foreach (['01','02','03','04','05','06','07','08','09','10','11','12'] as $month) {

            $date = $year.$month;

            if($date < date('Ym')){
                continue;
            }

            $chat = <<< CHART
CREATE TABLE  IF NOT EXISTS `tbl_chat_message_{$date}` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL COMMENT '客户id',
  `user_id` int(11) NOT NULL COMMENT '卖家用户id',
  `uuid` bigint(20) NOT NULL DEFAULT '0' COMMENT '消息id',
  `seller_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '卖家id',
  `seller_account_email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卖家邮箱',
  `store_id` int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
  `buyer_account_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '买家id',
  `buyer_email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '买家邮箱',
  `business_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '业务id',
  `business_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型:IM消息询盘:IM_IMQUIRY 实体表单发送询盘：NORMAL_INQUIRY',
  `message_type` int(11) NOT NULL DEFAULT '0' COMMENT '消息类型',
  `trade_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1收到买家回复信息 2收到卖家回复信息',
  `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间',
  `message_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '初始参数',
  `handler_params` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息传入参数',
  `handler_result` longblob NOT NULL COMMENT '消息处理结果',
  `attempt_count` int(11) NOT NULL DEFAULT '0' COMMENT '尝试次数',
  `create_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  UNIQUE KEY `idx_uuid` (`uuid`) USING BTREE,
  KEY `idx_seller_account_email` (`seller_account_email`) USING BTREE,
  KEY `idx_buyer_email` (`buyer_email`) USING BTREE,
  KEY `idx_client_id_user_id` (`client_id`,`user_id`) USING BTREE,
  KEY `idx_client_store_time` (`client_id`,`store_id`,`message_time`) USING BTREE,
  KEY `idx_buyer_account_id_message_time` (`buyer_account_id`,`message_time`),
  KEY `idx_seller_account_id_message_time` (`seller_account_id`,`message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='阿里聊天消息表';
CHART;

            $customer = <<< CUSTOMER
CREATE TABLE  IF NOT EXISTS `tbl_customer_message_{$date}` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) DEFAULT '0' COMMENT '客户id',
  `user_id` int(11) DEFAULT '0' COMMENT '用户id',
  `store_id` int(11) DEFAULT '0' COMMENT '店铺id',
  `type` tinyint(1) DEFAULT '0' COMMENT '类型(1:客户小记, 2:客户)',
  `customer_id` bigint(20) DEFAULT '0' COMMENT '阿里客户id',
  `taobao_user_id` bigint(20) DEFAULT '0' COMMENT '淘宝user_id',
  `note_id` bigint(20) DEFAULT '0' COMMENT '阿里小记id',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密钥',
  `sync_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '同步类型',
  `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间',
  `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '初始参数',
  `handler_result` longblob COMMENT '处理结果',
  `attempt_count` int(11) DEFAULT '0' COMMENT '尝试次数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT '1970-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_client_id_user_id` (`client_id`,`user_id`) USING BTREE,
  KEY `idx_client_store_message` (`client_id`,`store_id`,`message_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='阿里客户同步消息表';
CUSTOMER;

            $order = <<< ORDER
CREATE TABLE  IF NOT EXISTS `tbl_order_notify_{$date}` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL DEFAULT '0' COMMENT '客户id',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `taobao_user_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '淘宝user_id',
  `login_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账号id',
  `trade_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '阿里订单id',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态',
  `store_id` int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
  `sync_order_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '同步订单开关',
  `session_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权key',
  `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间',
  `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '初始参数',
  `order_info` longblob NOT NULL COMMENT '订单信息',
  `sync_result` longblob NOT NULL COMMENT '同步结果',
  `attempt_count` int(11) NOT NULL DEFAULT '0' COMMENT '尝试次数',
  `create_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_client_id_user_id` (`client_id`,`user_id`) USING BTREE,
  KEY `idx_client_store_time` (`client_id`,`store_id`,`message_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='阿里订单消息表';
ORDER;

            $delay = <<< DELAY
CREATE TABLE  IF NOT EXISTS `tbl_delay_message_${date}`  (
  `message_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '延迟消息标识',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '延迟消息类型：1:客户延时消息，2:线索延时消息，3:同步客户线索延时消息，4:订单重试延时消息',
  `client_id` int(11) NOT NULL DEFAULT 0 COMMENT '客户id',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `store_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '阿里店铺id',
  `buyer_account_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '买家id',
  `seller_account_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '卖家id',
  `message_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '延时消息内容',
  `delay_time` datetime(0) NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '消费状态：0:未消费，1:已消费',
  `create_time` datetime(0) NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT '1970-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  INDEX `idx_client_id_store_id_buyer_account_id_user_id`(`client_id`, `store_id`, `buyer_account_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id_buyer_account_id`(`user_id`, `buyer_account_id`) USING BTREE,
  INDEX `idx_client_id_user_id`(`client_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '延时消息表';
DELAY;
            $db->createCommand($chat)->execute();
            $db->createCommand($customer)->execute();
            $db->createCommand($order)->execute();
            $db->createCommand($delay)->execute();

        }

        $unbind = <<< UNBIND
CREATE TABLE  IF NOT EXISTS `tbl_unbind_cancel_auth_{$year}` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL DEFAULT '0' COMMENT '客户id',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型(1:会员解绑, 2:阿里账号个人取消授权, 3:阿里店铺取消授权)',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT 'type=1时为ali_account_id,type=2/3时为taobao_user_id',
  `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间',
  `message_data` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '初始参数',
  `attempt_count` int(11) NOT NULL DEFAULT '0' COMMENT '尝试次数',
  `create_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`message_id`),
  KEY `idx_client_id_user_id` (`client_id`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='阿里消息会员解绑/取消授权表';
UNBIND;

        $db->createCommand($unbind)->execute();
    }

    //添加消息时间字段
    public static function addMessageTime($year)
    {

        $db = \Yii::app()->alibaba_message_db;

        foreach (['01','02','03','04','05','06','07','08','09','10','11','12'] as $month) {

            $date = $year.$month;

            if($date < '202110'){
                continue;
            }

            $customer = <<< CUSTOMER
ALTER TABLE `tbl_customer_message_{$date}` 
ADD COLUMN `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间' AFTER `sync_type`;
CUSTOMER;

            $order = <<< ORDER
ALTER TABLE `tbl_order_notify_{$date}` 
ADD COLUMN `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间' AFTER `session_key`;
ORDER;

            try {
                $db->createCommand($customer)->execute();
                $db->createCommand($order)->execute();
            } catch (\Exception $e) {
                $endMsg = sprintf("addMessageTime: error_msg[%s]",$e->getMessage());
                \LogUtil::info($endMsg);
                echo "$endMsg";
                continue;
            }

        }

        $unbind = <<< UNBIND
ALTER TABLE `tbl_unbind_cancel_auth_{$year}` 
ADD COLUMN `message_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '消息时间' AFTER `relation_id`;
UNBIND;

        $db->createCommand($unbind)->execute();
    }

    //更新消息时间
    public static function updateMessageTime($year)
    {

        $db = \Yii::app()->alibaba_message_db;

        $limit = 1000;

        foreach (['01','02','03','04','05','06','07','08','09','10','11','12'] as $month) {

            $date = $year.$month;

            if($date < '202110'){
                continue;
            }

            //tbl_chat_message_{$date}
            $messageId = 0;

            do{
                $chatSql = "SELECT message_id,message_data FROM tbl_chat_message_{$date} WHERE message_id > {$messageId} ORDER BY message_id ASC LIMIT {$limit};";

                $chatData = $db->createCommand($chatSql)->queryAll(true);

                if(empty($chatData)){
                    break;
                }

                $position = count($chatData)-1;

                $messageId = $chatData[$position]['message_id'];

                foreach($chatData as $item){

                    if(empty($item['message_data'])){
                        continue;
                    }

                    $messageData = json_decode($item['message_data'],true);

                    $pubTime = $messageData['pub_time'];

                    $sql = "UPDATE `tbl_chat_message_{$date}` SET `message_time` = '{$pubTime}' where message_id = {$item['message_id']};";

                    $db->createCommand($sql)->execute();

                }


            } while(true);

            //tbl_customer_message_{$date}
            $messageId = 0;

            do{
                $customerSql = "SELECT message_id,message_data FROM tbl_customer_message_{$date} WHERE message_id > {$messageId} ORDER BY message_id ASC LIMIT {$limit};";

                $customerData = $db->createCommand($customerSql)->queryAll(true);

                if(empty($customerData)){
                    break;
                }

                $position = count($customerData)-1;

                $messageId = $customerData[$position]['message_id'];

                foreach($customerData as $item){

                    if(empty($item['message_data'])){
                        continue;
                    }

                    $messageData = json_decode($item['message_data'],true);

                    $pubTime = $messageData['pub_time'];

                    $sql = "UPDATE `tbl_customer_message_{$date}` SET `message_time` = '{$pubTime}' where message_id = {$item['message_id']};";

                    $db->createCommand($sql)->execute();
                }

            } while(true);

            //tbl_order_notify_{$date}
            $messageId = 0;

            do{
                $orderSql = "SELECT message_id,message_data FROM tbl_order_notify_{$date} WHERE message_id > {$messageId} ORDER BY message_id ASC LIMIT {$limit};";

                $orderData = $db->createCommand($orderSql)->queryAll(true);

                if(empty($orderData)){
                    break;
                }

                $position = count($orderData)-1;

                $messageId = $orderData[$position]['message_id'];

                foreach($orderData as $item){

                    if(empty($item['message_data'])){
                        continue;
                    }

                    $messageData = json_decode($item['message_data'],true);

                    $pubTime = $messageData['pub_time'];

                    $sql = "UPDATE `tbl_order_notify_{$date}` SET `message_time` = '{$pubTime}' where message_id = {$item['message_id']};";

                    $db->createCommand($sql)->execute();

                }

            } while(true);

        }

        //tbl_unbind_cancel_auth_{$date}
        $messageId = 0;

        do{
            $unbindSql = "SELECT message_id,message_data FROM tbl_unbind_cancel_auth_{$year} WHERE message_id > {$messageId} ORDER BY message_id ASC LIMIT {$limit};";

            $unbindData = $db->createCommand($unbindSql)->queryAll(true);

            if(empty($unbindData)){
                break;
            }

            $position = count($unbindData)-1;

            $messageId = $unbindData[$position]['message_id'];

            foreach($unbindData as $item){

                if(empty($item['message_data'])){
                    continue;
                }

                $messageData = json_decode($item['message_data'],true);

                $pubTime = $messageData['pub_time'];

                $sql = "UPDATE `tbl_unbind_cancel_auth_{$year}` SET `message_time` = '{$pubTime}' where message_id = {$item['message_id']};";

                $db->createCommand($sql)->execute();

            }

        } while(true);

    }

}
