<?php
/**
 *
 * Author: ruisenlin
 * Date: 2021/12/6
 */

namespace common\library\alibaba\services\message;

use common\library\alibaba\Constant;
use common\library\alibaba\services\queue\QueueHelper;

class DelayMessageService
{

    private $clientId;
    private $userId;
    private $storeId;

    private $delayMessage;

    private $buyerAccountId;
    /**
     * @param $clientId
     * @param $userId
     * @param $storeId
     */
    public function __construct($clientId, $userId, $storeId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->storeId = $storeId;
        $this->delayMessage = new DelayMessage($clientId,$userId,$storeId);
    }

    /**
     * @return mixed
     */
    public function getDelayMessage()
    {
        return $this->delayMessage;
    }

    /**
     * @param mixed $buyerAccountId
     */
    public function setBuyerAccountId($buyerAccountId): void
    {
        $this->buyerAccountId = $buyerAccountId;
    }


    /**
     * 通过消息id来判断是否已消费
     * @param int $messageId
     * @return mixed
     */
    public function judgeConsumeById(int $messageId,$delayTime)
    {
        $result = $this->delayMessage->selectById($messageId);
        if(empty($result) && date('d',$delayTime) == 1){
            $this->delayMessage->setSuffix((new \DateTime())->modify('first day of -1 month')->format('Ym'));
            $result = $this->delayMessage->selectById($messageId);
        }
        return $result['status'];
    }

    /**
     * 查找未消费消息
     * @return array
     */
    public function find($type)
    {
        $result = [];

        if($type == Constant::DELAY_MESSAGE_TYPE_SYNC_CUSTOMER_FOR_LEAD ){
            $this->delayMessage->setFilter([
                'buyer_account_id' => $this->buyerAccountId
            ]);
        }
        $this->delayMessage->setType($type);
        if( date('d') == 1){
            $this->delayMessage->setSuffix((new \DateTime())->modify('first day of -1 month')->format('Ym'));

            $result = array_merge($result,$this->delayMessage->find());
        }
        $this->delayMessage->setSuffix(date("Ym"));
        return array_merge($result,$this->delayMessage->find());
    }

    /**
     * 推送消息到延时队列
     * @param $type
     * @param $params
     * @param $delayTime
     */
    public function pushQueue($type,$params,$delayTime)
    {
        $params['suffix'] = $this->delayMessage->getSuffix();
        $this->delayMessage->setParams($params);
        $this->delayMessage->setType($type);
        switch ($type){

            case Constant::DELAY_MESSAGE_TYPE_CUSTOMER:
                $this->delayMessage->save();
                QueueHelper::pushCustomerDelayHandle($params['client_id'], $params['user_id'], $params['store_id'], 0, 0, $params['alibaba_company_id'], 0,
                    $params['suffix'],
                    Constant::SYNC_TYPE_ORDER_CUSTOMER, Constant::OKKI_SYNC_ORDER_CUSTOMER,
                    $this->delayMessage->getMessageId());

                $stringParams = json_encode($params);
                \LogUtil::info("pushDelay type:[$type] params:[$stringParams] delayTime:[$delayTime]");
                break;

            case Constant::DELAY_MESSAGE_TYPE_LEAD:
                $this->delayMessage->save();
                QueueHelper::pushLeadDelayHandle($params['client_id'],$params['user_id'],$params['store_id'],$params['lead_id'],$params['lead_customer_id'],
                    $params['buyer_account_id'],$params['seller_account_id'] ,$delayTime,
                    $params['suffix'],$this->delayMessage->getMessageId());

                $stringParams = json_encode($params);
                \LogUtil::info("pushDelay type:[$type] params:[$stringParams] delayTime:[$delayTime]");
                break;

            case Constant::DELAY_MESSAGE_TYPE_SYNC_CUSTOMER_FOR_LEAD:
                $delayRetry = 0;
                $stringParams = json_encode($params);
                if (!empty($params['delay_retry'])) {
                    $delayRetry = $params['delay_retry'];
                    if ($delayRetry >= 2 ){
                        \LogUtil::info("pushDelay above retry times params: [$stringParams] retry [$delayRetry]");
                        break;
                    }
                }
                $params['delay_retry'] = ++$delayRetry;
                $params['__process_until'] = time()+$delayTime;
                $this->delayMessage->setParams($params);
                $this->delayMessage->save();
                QueueHelper::pushSyncCustomerForLeadHandle(json_decode($params['params'],true), $delayTime, $params['suffix'], $delayRetry,
                    $this->delayMessage->getMessageId());

                \LogUtil::info("pushDelay type:[$type] params:[$stringParams] delayTime:[$delayTime]");
                break;

            case Constant::DELAY_MESSAGE_TYPE_ORDER_RETRY:
                $this->delayMessage->save();
                $params['message_id'] = $this->delayMessage->getMessageId();
                $params['processDelayTime'] = time() + $delayTime;
                QueueHelper::pushOrderRetryHandle($params);
                break;

            case Constant::DELAY_MESSAGE_TYPE_VISITOR_MARKETING_NOTIFY:
                $this->delayMessage->save();
                QueueHelper::pushVisitorMarketingNotifyHandle($params['client_id'], $params['user_id'], $params['seller_account_id'], $params['seller_account_email'], $params['buyer_account_id'], $params['message_time'], $delayTime,
                    $params['suffix'],
                    $this->delayMessage->getMessageId(), $params['sec_token'] ?? '');


                $stringParams = json_encode($params);
                \LogUtil::info("pushDelay type:[$type] params:[$stringParams] delayTime:[$delayTime]");
                break;
        }
    }
}