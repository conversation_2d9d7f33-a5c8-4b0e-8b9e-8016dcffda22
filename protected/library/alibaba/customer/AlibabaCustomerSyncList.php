<?php
namespace common\library\alibaba\customer;
use common\library\alibaba\Constant;
use common\library\util\SqlBuilder;
use common\models\client\AlibabaCustomerSyncTask;

/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 2020/10/29
 * Time: 17:53
 */
/**
 * Class AlibabaCustomerSyncList
 * @package common\library\alibab\customer
 */
class AlibabaCustomerSyncList extends \MysqlList{

    protected $clientId;
    protected $userId;
    protected $fields;
    protected $storeId;
    protected $status;
    protected $maxTaskId;
    protected $taskType;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
        $this->setOrder('desc');
        $this->setOrderBy('create_time');
    }

    private function getModelTableName()
    {
        return \common\library\alibaba\customer\AlibabaCustomerSyncTask::getModelTableName();
    }

    /**
     * @param mixed $fields
     */
    public function setFields(array $fields): void
    {
        $this->fields = empty($fields) ? '*' : implode(',',$fields);
    }

    /**
     * @param mixed $userId
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;
    }

    /**
     * @param mixed $storeId
     */
    public function setStoreId($storeId)
    {
        $this->storeId = $storeId;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @param mixed $maxTaskId
     */
    public function setMaxTaskId($maxTaskId)
    {
        $this->maxTaskId = $maxTaskId;
    }

    /**
     * @param mixed $taskType
     */
    public function setTaskType($taskType)
    {
        $this->taskType = $taskType;
    }


    //构建搜索的参数
    public function buildParams()
    {
        $sql    = "client_id=:client_id";
        $params = [":client_id" => $this->clientId];

        if($this->userId){
            SqlBuilder::buildIntWhere('','user_id',$this->userId, $sql, $params);
        }

        if($this->storeId){
            SqlBuilder::buildIntWhere('','store_id',$this->storeId, $sql, $params);
        }

        if( $this->status !== null )
        {
            SqlBuilder::buildIntWhere('','status',$this->status, $sql, $params);
        }

        if( $this->maxTaskId )
        {
            $sql .= ' AND task_id <:max_task_id';
            $params[':max_task_id'] = $this->maxTaskId;
        }

        if( $this->taskType )
        {
            SqlBuilder::buildIntWhere('','task_type',$this->taskType, $sql, $params);
        }

        $sql .= " AND enable_flag =".Constant::TYPE_ENABLE;
        return [$sql, $params];
    }

    public function find()
    {
        list($where, $params) = $this->buildParams();
        if (empty($where)) {
            return [];
        }
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        $limit  = $this->buildLimit();
        $orderBy = $this->buildOrderBy();
        $table = $this->getModelTableName();

        if (empty($this->fields)) {
            $fields = '*';
        } else {
            $fields = $this->fields;
        }
        $sql = "SELECT $fields FROM {$table} WHERE {$where} {$orderBy} {$limit}";

        $command = $db->createCommand($sql);

        $result = $command->queryAll(true, $params)?:[];
        $list =[];
        foreach ($result as $item )
        {
            $list[] = $this->format($item);
        }

        return $list??[];
    }

    public function count()
    {
        list($where, $params) = $this->buildParams();
        if (empty($where)) {
            return 0;
        }
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        $table = $this->getModelTableName();
        $sql   = "SELECT count(1) FROM {$table} WHERE {$where}";

        $count = $db->createCommand($sql)->queryScalar($params);

        return $count;

    }

    protected function format($elem)
    {
        if( isset($elem['result_file_id']) && $elem['result_file_id'] )
        {
            $fileId = $elem['result_file_id'];
            $file = new \AliyunUpload();
            $file->loadByFileId($fileId, $this->userId);
            $elem['file_name'] = $file->getFileName();
            $elem['result_url'] = $file->getFileUrl();
        }

        return $elem;
    }
}
