<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/11
 * Time: 6:20 PM
 */

namespace common\library\alibaba\customer;


use common\components\BaseObject;

/**
 * Class AlibabaCustomerNoteRelation
 * @package common\library\alibaba\customer
 * @property string $relation_id
 * @property string $client_id
 * @property string $store_id
 * @property string $company_id
 * @property string $trail_id
 * @property string $alibaba_company_id
 * @property string $alibaba_note_id
 * @property string $node_type
 * @property string $note_code
 * @property string $note_label
 * @property string $content
 * @property string $note_time
 * @property string $seller_account_id
 * @property string $seller_name
 * @property string $create_time
 * @property string $update_time
 * @property string $delete_flag
 */
class AlibabaCustomerNoteRelation extends BaseObject
{
    protected $clientId;

    /**
     * AlibabaCustomerNoteRelation constructor.
     * @param $clientId
     */
    public function __construct($clientId, $id)
    {
        $this->clientId = $clientId;

        if($id )
        {
            $this->loadById($id);
        }

    }

    public function  loadById($id)
    {
        $model = $this->getModelClass()::model()->find('relation_id=:relation_id',[':relation_id' => $id]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    /**
     * @return string|\AlibabaCustomerNoteRelationModel
     */
    public function getModelClass()
    {
        return \AlibabaCustomerNoteRelationModel::class;
    }
}
