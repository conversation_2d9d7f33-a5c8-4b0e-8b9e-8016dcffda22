<?php
namespace common\library\alibaba\customer;

use common\components\BaseObject;
use common\library\alibaba\Constant;
use common\library\alibaba\store\AlibabaStore;
use common\library\history\setting\AlibabaSyncSettingHistoryBuilder;
use common\library\history\setting\ItemSettingHistoryCompare;

/**
 * Created by PhpStorm.
 * User: yoyo
 * Date: 2021-01-05
 * Time: 19:18
 */

/**
 * This is the model class for table "tbl_alibaba_customer_sync_setting".
 *
 * The followings are the available columns in table 'tbl_alibaba_customer_sync_setting':
 * @property integer $setting_id
 * @property integer $client_id
 * @property integer $store_id
 * @property string $pool_id
 * @property string $customer_status
 * @property integer $sync_status
 * @property string $last_sync_time
 * @property string $update_user
 * @property string $last_open_time
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @property integer $sync_customer_extent
 * @property string $archive_time_start
 * @property string $archive_time_end
 * @property string $sync_owner_change
 * @property string $update_type
 * @property string $update_status_flag
 * @property integer $move_public_flag
 * @property integer $company_rename_flag
 * @method AlibabaCustomerSyncSettingFormatter getFormatter()
 */
class AlibabaCustomerSyncSetting extends BaseObject
{
    const SYNC_STATUS_DOING = 1;//同步中
    const SYNC_STATUS_DONE = 2;//同步完成
    protected $clientId;
    protected $userId;
    protected $settingId;
    protected $storeId;
    protected $store;

    public function __construct($clientId, $storeId = 0)
    {
        $this->clientId = $clientId;
        $this->storeId = $storeId;
        if ($storeId) {
            $this->loadByClientStore($storeId);
        }
        $this->_formatter = new AlibabaCustomerSyncSettingFormatter($this->clientId);
    }



    public function getModelClass()
    {
        return \AlibabaCustomerSyncSettingModel::class;

    }

    /**
     * @return mixed
     */
    public function getStore()
    {
        if ($this->store == null)
            $this->store = new AlibabaStore($this->clientId, $this->storeId);

        return $this->store;
    }

    public function loadByClientStore($storeId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and store_id=:store_id',
            [
                ':client_id' => $this->clientId,
                ':store_id' => $storeId,
            ]);
        if ($model) {
            $this->setModel($model);
        }

        return $this;
    }


    public function beforeSave()
    {
        if ($this->isNew())
        {
            $this->client_id = $this->client_id?:$this->clientId;
            $this->store_id = $this->store_id?:$this->storeId;
            $this->create_time = date('Y-m-d H:i:s');

            if( !$this->client_id || !$this->store_id )
            {
                throw new \ProcessException('client_id和store_id 配置参数不能为空');
            }
        }

        return parent::beforeSave();
    }

    public function afterSave()
    {
        $compareEditSetting = new ItemSettingHistoryCompare($this->clientId);
        $compareEditSetting->setModule(ItemSettingHistoryCompare::SETTING_TYPE_ALIBABA_SETTING);
        $compareEditSetting->setData($this->_attributes, $this->_oldAttributes);
        $compareEditSetting->setExtraData(['refer_id' => $this->store_id]);
        // 由于要记录每一个独立不同设置操作，设置公海分组映射
        if(
            isset($this->_attributes['pool_id'], $this->_oldAttributes['pool_id']) &&
            $this->_attributes['pool_id'] != $this->_oldAttributes['pool_id']
        ) {
            $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_ALI_CUSTOMER_TYPE_GROUP);
            $compareEditSetting->build($this->update_user);
        }

        // 由于要记录每一个独立不同设置操作，设置客户通状态映射
        if(
            isset($this->_attributes['sync_owner_change'], $this->_oldAttributes['sync_owner_change']) &&
            $this->_attributes['sync_owner_change'] != $this->_oldAttributes['sync_owner_change']
        ) {
            $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_ALI_CUSTOMER_TYPE_SYNC_OWNER);
            $compareEditSetting->build($this->update_user);
        }

        // 由于要记录每一个独立不同设置操作，设置客户通状态映射
        if(
            isset($this->_attributes['customer_status'], $this->_oldAttributes['customer_status']) &&
            $this->_attributes['customer_status'] != $this->_oldAttributes['customer_status']
        ) {
            $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_ALI_CUSTOMER_TYPE_STATUS);
            $compareEditSetting->build($this->update_user);
        }

        // 由于要记录每一个独立不同设置操作，设置数据同步范围
        if(
            (
                isset($this->_attributes['sync_customer_extent'], $this->_oldAttributes['sync_customer_extent']) &&
                $this->_attributes['sync_customer_extent'] != $this->_oldAttributes['sync_customer_extent']
            ) || (
                isset($this->_attributes['archive_time_start'], $this->_oldAttributes['archive_time_start']) &&
                $this->_attributes['archive_time_start'] != $this->_oldAttributes['archive_time_start']
            ) || (
                isset($this->_attributes['archive_time_end'], $this->_oldAttributes['archive_time_end']) &&
                $this->_attributes['archive_time_end'] != $this->_oldAttributes['archive_time_end']
            )
        ) {
            $compareEditSetting->setType(ItemSettingHistoryCompare::SETTING_ALI_CUSTOMER_TYPE_SYNC);
            $compareEditSetting->build($this->update_user);
        }

        parent::afterSave();
    }

}
