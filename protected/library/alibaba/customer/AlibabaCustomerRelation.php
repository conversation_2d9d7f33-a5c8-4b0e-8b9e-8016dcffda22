<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/7
 * Time: 10:31 AM
 */

namespace common\library\alibaba\customer;


use common\components\BaseObject;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\customer\Helper;
use common\library\sns\Constants;
use common\library\sns\customer\CustomerContactService;
use common\library\social_auth\Constant;
use common\models\client\AlibabaCustomerRelation as AlibabaCustomerRelationModel;
use RuntimeException;
use User;
use Yii;

/**
 * Class AlibabaCustomerRelation
 * @package common\library\alibaba\customer
 *
 * @property string $relation_id
 * @property string $client_id
 * @property string $alibaba_customer_id
 * @property string $customer_id
 * @property integer $store_id
 * @property string $create_time
 * @property string $sync_time
 * @property string $update_time
 * @property string $alibaba_company_id
 * @property string $buyer_account_id
 * @property string $buyer_account_encrypt
 * @property string $buyer_email
 * @property string $is_add
 * @property string $sync_count
 * @property string $alibaba_customer_name
 * @property string $growth_level
 */
class AlibabaCustomerRelation extends BaseObject
{

    protected $clientId;

    /**
     * AlibabaCompanyRelation constructor.
     * @param $clientId
     * @param int $id
     */
    public function __construct($clientId, $id=0)
    {
        $this->clientId = $clientId;
        if( $id )
            $this->loadById($id);
    }

    /**
     * @return string | AlibabaCustomerRelationModel
     */
    public function getModelClass()
    {
        return AlibabaCustomerRelationModel::class;
    }

    public function  loadById($id)
    {
        $model = $this->getModelClass()::model()->find('relation_id=:relation_id',[':relation_id' => $id]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function  loadByAlibabaCustomerId($customerId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and alibaba_customer_id=:alibaba_customer_id',[':client_id' => $this->clientId, ':alibaba_customer_id' => $customerId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function  loadByCustomerId($customerId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and customer_id=:customer_id',[':client_id' => $this->clientId, ':customer_id' => $customerId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function loadByStoreBuyerAccountId($storeId, $buyerAccountId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and buyer_account_id=:buyer_account_id and store_id=:store_id', [':client_id' => $this->clientId, ':buyer_account_id' => $buyerAccountId, ':store_id' => $storeId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }


    /**
     * 根据店铺ID和买家ID获取关联的customer_id
     *
     * 注意：(store_id + buyer_account_id)并不是唯一的，例如在阿里客户通建档、删除、再重新建档，
     *      会导致有2条记录，它们buyer_account_id是一样的，但是alibaba_company_id不一样
     * 所以这里需要过滤出 customer_id > 0 的记录
     *
     * @param $storeId
     * @param $buyerAccountId
     * @return int
     */
    public function getCustomerIdByStoreBuyerAccountId($storeId, $buyerAccountId): int
    {
        $model = $this->getModelClass()::model()
            ->find('client_id=:client_id and buyer_account_id=:buyer_account_id and store_id=:store_id and customer_id > 0',
                [':client_id' => $this->clientId, ':buyer_account_id' => $buyerAccountId, ':store_id' => $storeId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this->isNew() ? 0 : (int) $this->customer_id;
    }

	protected function afterSave() {

		if ($this->isNew() || (($this->_attributes['customer_id'] ?? 0) != ($this->_oldAttributes['customer_id'] ?? 0))) {

			$customer = new Customer($this->client_id, $this->customer_id);

			Helper::refreshCompanyTmTime($this->clientId, $customer->company_id, $this->store_id);

            // 更新用户关联社交账号表
            $service = (new CustomerContactService($this->clientId));
            $service->updateCompanyRelation(
                Constants::SNS_CLIENT_TM,
                $this->store_id,
                $this->buyer_account_id,
                (int) $customer->company_id,
                (int) $this->customer_id
            );
        }

		parent::afterSave(); // TODO: Change the autogenerated stub
	}

    public function bind($storeId, $buyerAccountId, $customerId, $userId, bool $ignoreConflict = false)
    {
        $customer = new \common\library\customer_v3\customer\orm\Customer($this->clientId, $customerId);
        if ($customer->isNew()) {
            throw new RuntimeException("customer $customerId not found");
        }
        $company = new Company($this->clientId, $customer->company_id);
        if (!$company->isEditable(User::getUserObject($userId))) {
            throw  new RuntimeException(Yii::t('customer', 'Unable to edit this customer'));
        }

        $now = date('Y-m-d H:i:s');
        $buyerInfo = \common\library\alibaba\Helper::fetchBuyerInfo($this->clientId, $storeId, $buyerAccountId);
        if ($this->isNew()) {
            $this->client_id = $this->clientId;
            $this->alibaba_customer_id = $buyerInfo['alibaba_customer_id'] ?? 0;
            $this->alibaba_company_id = $buyerInfo['alibaba_company_id'] ?? 0;
            $this->buyer_account_id = $buyerAccountId;
            $this->buyer_account_encrypt = $buyerInfo['buyer_account_encrypt'] ?? '';
            $this->buyer_email = $buyerInfo['email'] ?? '';
            $this->alibaba_customer_name = $buyerInfo['name'] ?? '';
            $this->is_add = 1;
            $this->store_id = $storeId;
            $this->create_time = $now;
            $this->sync_time = $now;
        }
        \LogUtil::info("clientId: {$this->clientId}, bind $buyerAccountId from {$this->customer_id} to customer $customerId");

        $this->update_time = $now;
        $this->customer_id = $customerId;
        $this->save();

        $service = new TMAlibabaCustomerService($this->clientId, $userId);
        $customer = $service->assignCustomer($customer, $storeId, $buyerInfo);
        $customer->setSkipDuplicateCheck($ignoreConflict);
        $customer->save();
    }
}
