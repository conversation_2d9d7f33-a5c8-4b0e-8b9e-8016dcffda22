<?php
namespace common\library\alibaba\customer;
use common\library\account\Client;
use common\library\alibaba\Constant;
use common\library\alibaba\services\AlibabaCompany;
use common\library\alibaba\services\message\DelayMessageService;
use common\library\alibaba\store\AlibabaStore;
use common\library\customer\Helper;
use xiaoman\orm\database\PgsqlUtil;

/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 07/01/2021
 * Time: 15:04
 */
class AlibabaCustomerService{


    /**
     * 同步单个客户
     * @param $clientId
     * @param $userId
     * @param $async_flag
     * @param int $alibabaCompanyId
     * @param int $companyId
     * @param int $checkSyncSwitch
     * @return array
     * @throws \ProcessException
     */
    public static function syncSingleCustomer(
        $clientId,
        $userId,
        $async_flag,
        $alibabaCompanyId=0,
        $companyId=0,
        $checkSyncSwitch=1,
        int $syncType = Constant::SYNC_TYPE_RESYNC,
        int $updateType = Constant::UPDATE_TASK_TYPE_APPEND,
        int $updateOwnerFlag = Constant::UPDATE_DISABLE_FLAG,
        int $updateStatusFlag = Constant::UPDATE_DISABLE_FLAG,
        int $movePublicFlag = Constant::UPDATE_DISABLE_FLAG
    )
    {
        if( !$companyId && !$alibabaCompanyId)
        {
            throw new \ProcessException('参数错误');
        }

        $result = [
            'ret' => false,
            'async_flag' => $async_flag,
            'result' => null,
            'error' => null
        ];

        $client = Client::getClient($clientId);
        if ($client->getExpiredDays() >= Constant::STOP_SYNC_DAYS) {
            $result['error'] = '断约超过7天停止同步';
            return $result;
        }

        $alibabaCompanyRelation = new AlibabaCompanyRelation($clientId);
        if( $alibabaCompanyId )
        {
            $alibabaCompanyRelation->loadByAlibabaCompanyId($alibabaCompanyId);
        }elseif( $companyId )
        {
            $alibabaCompanyRelation = $alibabaCompanyRelation->loadByCompanyId($companyId);
        }

        if( $alibabaCompanyRelation->isNew() )
        {
            $result['error'] = '非阿里店铺客户通同步下来的客户';
            return $result;
        }

        $alibabaCompanyId = $alibabaCompanyRelation->alibaba_company_id;
        $storeId = $alibabaCompanyRelation->store_id;

        $alibabaStore = new AlibabaStore($clientId, $storeId);
        $sessionKey = $alibabaStore->getAccessToken();
        if( empty($sessionKey))
        {
            $result['error'] = '店铺授权已失效';
            return $result;
        }

        if( $async_flag )
        {
            //异步处理的话, 上次执行完成5分钟后,才会重新同步
            if( $alibabaCompanyRelation->sync_status != Constant::SYNC_STATUS_RUNNING
                && strtotime($alibabaCompanyRelation->sync_time) + 300  < time() )
            {
                $delayMessageService = new DelayMessageService($clientId,$userId,$storeId);
                $params = [
                    'order_id'    => 0,
                    'client_id'  => $clientId,
                    'user_id'    => $userId,
                    'store_id'    => $storeId,
                    'buyer_account_id' => 0,
                    'alibaba_company_id' => $alibabaCompanyId,
                    '__process_until' => time(),
                    'handler'    => 'CustomerDelayHandler',
                    'max_retry_count' => 2,   //表示失败要重试的次数
                    'sync_type' =>  Constant::SYNC_TYPE_ORDER_CUSTOMER,
                    'event' => Constant::OKKI_SYNC_ORDER_CUSTOMER
                ];
                // 推送消息到延时队列
                $delayMessageService->pushQueue(Constant::DELAY_MESSAGE_TYPE_CUSTOMER,$params,0);

                $alibabaCompanyRelation->sync_status = Constant::SYNC_STATUS_RUNNING;
                $alibabaCompanyRelation->save();
                $result['ret'] = true;
            }

            return $result;
        }

        $alibabaCompany =  new AlibabaCompany($sessionKey, $alibabaCompanyId);
        $data = $alibabaCompany->getInfo();
        if( empty($data) || (isset($data['code']) && (isset($data['sub_code'])) || isset($data['sub_msg'])))
        {
            \LogUtil::error("[syncSingleCustomer] error sessionKey :{$sessionKey}  alibabaCompanyId: {$alibabaCompanyId} error:".json_encode($data));
            throw  new \RuntimeException('获取客户信息失败');
        }

        $processor = new AlibabaCustomerSyncProcessor($clientId,$userId,$storeId);
        $processor->setSessionKey($sessionKey);
        $processor->setSyncType($syncType);
        if($syncType == Constant::SYNC_TYPE_RESYNC)
        {
            $processor->setEvent(Constant::OKKI_SYNC_RESYNC);
        }
        $processor->setUpdateType($updateType);
        $processor->setUpdateOwnerFlag($updateOwnerFlag);
        $processor->setUpdateStatusFlag($updateStatusFlag);
        $processor->setMovePublicFlag($movePublicFlag);
        $processor->initDefaultSetting(null,null,$checkSyncSwitch);
        $result['ret'] = $processor->processWithLock($alibabaCompanyId,$data);


        if( !$result['ret'] )
        {
            $result['error'] = $processor->getError();
            \LogUtil::error("同步阿里客户失败 : {$storeId} {$storeId} {$alibabaCompanyId}  error".json_encode($result['error']));
            return $result;
        }

        $result['result'] = $processor->getResult();

        \LogUtil::error("同步阿里客户成功 : {$storeId} {$storeId} {$alibabaCompanyId}  res:".json_encode($result));
        return $result;
    }

    //客户设置删除公海分组
    public static function clearCustomerSettingPoolId($clientId,$pool_id){

        if(!$clientId || !$pool_id){
            return false;
        }
        $sql = "update tbl_alibaba_customer_sync_setting set pool_id = 0 where client_id = {$clientId} and pool_id = {$pool_id}";
        $res = \Yii::app()->db->createCommand($sql)->execute();
        \LogUtil::info("clearCustomerSettingPoolId:client_id:{$clientId} pool_id:{$pool_id} res:{$res}");
    }

    //客户设置删除客户状态
    public static function clearCustomerSettingStatus($clientId,$userId,array $statusIds){

        if(!$clientId || !$statusIds){
            return false;
        }
        $sql = "select * from tbl_alibaba_customer_sync_setting where client_id = {$clientId}";
        $list = \Yii::app()->db->createCommand($sql)->queryAll();
        if(!$list){
            return false;
        }
        foreach ($list as $data){
            $deleteFlag = false;
            $customerStatus = json_decode($data['customer_status'],true)??[];
            foreach ($customerStatus as $key => $item){
                if(in_array($item['status'],$statusIds)){
                    $deleteFlag = true;
                    $customerStatus[$key]['status'] = '';
                }
            }
            if($deleteFlag && $customerStatus){
                $alibabaCustomerSyncSetting  = new AlibabaCustomerSyncSetting($clientId, $data['store_id']);
                $alibabaCustomerSyncSetting->update_user = $userId;
                $alibabaCustomerSyncSetting->customer_status = json_encode($customerStatus);
                $alibabaCustomerSyncSetting->save();

                // 客户状态删除，实时同步客户开关关闭(产品需求-暂时忽略这中客户状态检查)
//                $alibabaStore = new \common\library\alibaba\store\AlibabaStore($clientId, $data['store_id']);
//                $alibabaStore->sync_customer_flag = 0;
//                $alibabaStore->save();
//                \LogUtil::info("clearCustomerSettingStatus:client_id:{$clientId} store_id={$data['store_id']} user_id:{$userId} ali push 开关关闭");

            }
        }
        \LogUtil::info("clearCustomerSettingStatus:client_id:{$clientId} user_id:{$userId} status_id:".join(',',$statusIds));
    }

    /**
     * @param $clientId
     * @param $companyId
     * @param array $columns
     * @return array|\CDbDataReader|mixed
     */
    public static function getCompanyRelationInfo($clientId, $companyId, array $columns)
    {
        $relationList = new \common\library\alibaba\customer\AlibabaCompanyRelationList($clientId);
        $relationList->setCompanyId($companyId);
        $relationList->setFields($columns);
        $relationList->getFormatter()->setSpecifyFields($columns);

        return $relationList->find();
    }

    /**
     * 通过buyer获取联系人
     * @param $clientId
     * @param $buyerAccountId
     * @param $storeId
     * @return array|\CDbDataReader|mixed
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getCustomerByStoreBuyer($clientId, $buyerAccountId, $storeId=0)
    {
        $companyRelationTable = \common\models\client\AlibabaCompanyRelation::model()->tableName();
        $customerRelationTable = \common\models\client\AlibabaCustomerRelation::model()->tableName();
        $sql = "select co.company_id, co.sync_time, co.alibaba_company_id, co.sync_status, cu.customer_id, cu.buyer_email 
                from {$companyRelationTable} as co
                join {$customerRelationTable} cu 
                    on co.alibaba_company_id = cu.alibaba_company_id and co.client_id=cu.client_id
                where co.client_id=:client_id and co.buyer_account_id=:buyer_account_id and cu.buyer_account_id=:buyer_account_id";
        $params = [
            ':client_id' => $clientId,
            ':buyer_account_id' => $buyerAccountId
        ];

        if($storeId){
            $sql .= " and co.store_id=:store_id ";
            $params[':store_id'] = $storeId;
        }else{
            $sql .= " and cu.customer_id > 0 ";
        }

        $sql .= " limit 1 ";

        $db = \common\models\client\AlibabaCompanyRelation::getDbByClientId($clientId);
        return $db->createCommand($sql)->queryRow(true, $params);
    }

    /**
     * 通过aliCompanyIds获取客户信息
     * @param $clientId
     * @param $companyIds
     * @return \CompanyModel[]
     */
    public static function getCompaniesByIds($clientId, $companyIds)
    {
        if (!$companyIds) {
            return [];
        }

        $criteria = new \CDbCriteria();
        $criteria->addInCondition('company_id', $companyIds);
        $criteria->addCondition('client_id=:client_id');
        $criteria->addCondition('is_archive=:is_archive');
        $criteria->params[':client_id'] = $clientId;
        $criteria->params[':is_archive'] = 1;

        return \CompanyModel::model()->findAll($criteria);
    }

    /**
     * 客户继承线索的ali_company
     * @param $clientId
     * @param $companyId
     * @param $leadId
     * @param $storeId
     * @param $userId
     * @return bool|int|void
     */
    public static function extendAliInfoFromLead($clientId, $companyId, $leadId, $storeId, $userId)
    {
        // 关联alibaba_company_relation
        $tradeRelationList = new \common\library\alibaba\trade\AlibabaTradeRelationList($clientId);
        $tradeRelationList->setLeadId($leadId);
        $relations = $tradeRelationList->find();
        if (!$relations) {
            \LogUtil::info("[companyExtendLeadAliCompanyId] trade relation is empty. clientId: {$clientId}, companyId: {$companyId}, leadId: {$leadId}");
            return;
        }

        $company = new \common\library\customer_v3\company\orm\Company($clientId, $companyId);

        $aliCompanyIds = [];
        $buyerAccountIds = [];
        foreach ($relations as $relation) {
            if(empty($relation['customer_id']) || empty($relation['buyer_account_id'])){
                continue;
            }

            if(!empty($relation['ali_company_id'])){
                $aliCompanyIds[$relation['ali_company_id']][] = $relation['customer_id'];
                $buyerAccountIds[] = $relation['buyer_account_id'];
            }

            $tmAlibabaCustomerService = new \common\library\alibaba\customer\TMAlibabaCustomerService($clientId, $userId);
            $tmAlibabaCustomerService->setAlibabaAccount($relation['seller_account_id']);
            $tmAlibabaCustomerService->TMArchivingCustomer($company, $relation['buyer_account_id'],$relation['customer_id']);

            \LogUtil::info("[extendAliInfoFromLead] TMArchivingCustomer . clientId: {$clientId}, companyId: {$companyId}, leadId: {$leadId}, buyer_account_id: {$relation['buyer_account_id']}, customer_id: {$relation['customer_id']}");
        }

        foreach($aliCompanyIds as $aliCompanyId => $customerIds){
            // 同步阿里小记
            $params = compact('clientId', 'companyId', 'storeId', 'userId', 'aliCompanyId');
            $params['customerIds'] = implode(',', $customerIds);
            $log = '/tmp/alibaba_syncnotebyalicompany.log';
            \common\library\CommandRunner::run(
                'alibaba',
                'SyncNoteByAliCompany',
                $params,
                $log,
            );
        }

        //更新客户的询盘时间
        if ( !empty($buyerAccountIds)) {
            //查询询盘表的最后一次询盘
            return Helper::updateLatestReceiveAliTradeTime($clientId, $companyId, $buyerAccountIds);
        }

        return true;
    }

    /**
     * 获取联系人信息
     * @param $clientId
     * @param array $companyIds
     * @return array|\CDbDataReader|mixed
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getCustomersByCompanyIds($clientId, array $companyIds)
    {
        if (!$companyIds) {
            return [];
        }
        $companyIdsStr = implode(',', $companyIds);

        $sql = "select lower(s.email) as email, s.full_tel_list, s.company_id, s.name from tbl_customer s 
                join tbl_company c on s.company_id = c.company_id 
                where s.client_id=:client_id and s.company_id in ({$companyIdsStr}) and c.is_archive=1 and s.is_archive=1";
        $params = [':client_id' => $clientId];
        $db = \PgActiveRecord::getDbByClientId($clientId);
        $data = $db->createCommand($sql)->queryAll(true, $params);
        foreach ($data as $k => $datum) {
            $data[$k]['full_tel_list'] = PgsqlUtil::unpackArray($datum['full_tel_list']);
        }

        return $data;
    }

    /**
     * 根据优先级选择客户
     * @param $clientId
     * @param array $companies
     * @param array $properties
     * @return \CompanyModel
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function chooseCompanyByPriority($clientId, array $companies, array $properties)
    {
        // 'owner', 'company_name', 'tel', 'email, 'max_order_time'

        $companyIds = array_reduce($companies, function ($res, $item) {
            $res[] = $item->company_id;
            return $res;
        }, []);

        // 跟进人
        $owners = [];
        if (isset($properties['owner'])) {
            foreach ($companies as $company) {
                $owners[$company->company_id] = $company->user_id ? PgsqlUtil::unpackArray($company->user_id) : [];
            }
        }

        if (isset($properties['email']) || isset($properties['tel'])) {
            $customers = AlibabaCustomerService::getCustomersByCompanyIds($clientId, $companyIds);
        }

        // 邮箱
        $customerEmails = [];
        if (isset($properties['email'])) {
            $customerEmails = \ArrayUtil::groupBy($customers, 'company_id', 'email');
        }

        // 联系人电话
        $customerTels = [];
        if (isset($properties['tel'])) {
            $customerTels = array_reduce($customers, function ($res, $item) {
                $res[$item['company_id']] = array_merge($res[$item['company_id']]??[], $item['full_tel_list']??[]);
                return $res;
            }, []);
        }

        // 最大联系时间
        $maxOrderTimeCompanyId = null;
        if (isset($properties['max_order_time'])) {
            [$maxOrderTimeCompanyId] = array_reduce($companies, function ($res, $item) {  // 最大联系时间
                $orderTime = strtotime($item->order_time);
                return $res[1] > $orderTime ? $res : [$item->company_id, $orderTime];
            }, [0, 0]);
        }

        // 二进制位表示优先级
        $propertyFlags = [];
        $leftShiftNum = count($properties) - 1;
        foreach ($properties as $k =>  $property) {
            $propertyFlags[$k] = 1 << $leftShiftNum;
            --$leftShiftNum;
        }

        $matchRes = [];
        foreach ($companies as $index => $value) {
            // 跟进人相同
            if (!empty($properties['owner']) && in_array($properties['owner'], $owners[$value->company_id] ?? [])) {
                $matchRes[$index] = ($matchRes[$index] ?? 0) | $propertyFlags['owner'];
            }

            // 公司名相同
            if (!empty($properties['company_name']) && $properties['company_name'] == $value->name) {
                $matchRes[$index] = ($matchRes[$index] ?? 0) | $propertyFlags['company_name'];
            }

            // 邮箱
            if (!empty($properties['email']) && array_intersect($properties['email'], $customerEmails[$value->company_id] ?? [])) {
                $matchRes[$index] = ($matchRes[$index] ?? 0) | $propertyFlags['email'];
            }

            // 电话相同
            if (!empty($properties['tel']) && array_intersect($properties['tel'], $customerTels[$value->company_id] ?? [])) {
                $matchRes[$index] = ($matchRes[$index] ?? 0) | $propertyFlags['tel'];
            }

            // 最大联系时间
            if (!empty($properties['max_order_time']) && $maxOrderTimeCompanyId == $value->company_id) {
                $matchRes[$index] = ($matchRes[$index] ?? 0) | $propertyFlags['max_order_time'];
            }
        }

        $index = 0;
        if ($matchRes) {
            // 优先级排序
            $matchRes = array_flip($matchRes);
            krsort($matchRes);
            $index = current($matchRes);
        }

        $companyIds = implode(',', $companyIds);
        \LogUtil::info("[chooseCompanyByPriority] companyIds: {$companyIds}, matched: {$companies[$index]->company_id}");

        return $companies[$index];
    }

    /**
     * 获取线索关联的有效阿里客户
     * @param $clientId
     * @param $leadIds
     * @return array|\CDbDataReader|mixed
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getAliCompaniesByLeadIds($clientId, $leadIds)
    {
        if (!$leadIds) {
            return [];
        }

        $tradeRelationList = new \common\library\alibaba\trade\AlibabaTradeRelationList($clientId);
        $tradeRelationList->setLeadId($leadIds);
        $tradeRelationList->setFields(['lead_id', 'ali_company_id']);
        $aliCompanies = array_column($tradeRelationList->find(), 'lead_id', 'ali_company_id');

        if (!$aliCompanies) {
            return [];
        }
        $aliCompanyIds = array_filter(array_keys($aliCompanies));

        $data = self::getCompaniesByAliCompanyIds($clientId, $aliCompanyIds);

        foreach ($data as $k => $value) {
            $data[$k]['lead_id'] = $aliCompanies[$value['alibaba_company_id']] ?? 0;
        }

        return $data;
    }

    /**
     * @param $clientId
     * @param $aliCompanyIds
     * @return array|\CDbDataReader|mixed
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getCompaniesByAliCompanyIds($clientId, $aliCompanyIds)
    {
        if (!$aliCompanyIds) {
            return [];
        }
        $aliCompanyIds = implode(',', $aliCompanyIds);

        $sql = "select cr.alibaba_company_id, c.company_id from tbl_alibaba_company_relation cr
                join tbl_company c on cr.company_id = c.company_id
                where cr.client_id=:client_id and cr.alibaba_company_id in ({$aliCompanyIds})  and c.is_archive=1";
        $params = [':client_id' => $clientId];
        $db = \PgActiveRecord::getDbByClientId($clientId);
        return $db->createCommand($sql)->queryAll(true, $params);
    }


}
