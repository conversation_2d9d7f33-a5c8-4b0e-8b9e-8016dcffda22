<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/7
 * Time: 5:29 PM
 */

namespace common\library\alibaba\customer;


use common\components\BaseObject;
use common\library\alibaba\Constant;
use common\library\alibaba\services\AlibabaCustomerList;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\alibaba\services\DataHelper;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\trade\AlibabaTradeRelation;
use common\library\customer_v3\company\list\CompanyList;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\Customer;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\config\ConfigApi;
use common\library\setting\library\config\ConfigConstant;
use common\library\setting\library\status\StatusApi;
use common\library\sns\Constants as SnsConstants;
use common\library\sns\customer\CustomerContactHelper;
use common\library\util\SqlBuilder;
use common\models\client\AlibabaCustomerRelation as AlibabaCustomerRelationModel;
use common\models\client\AlibabaCustomerSyncData;
use Constants;
use LogUtil;

class CustomerSyncHelper
{

    const GENDER_MAP = [
        'F' => Customer::GENDER_TYPE_WOMAN,
        'M' => Customer::GENDER_TYPE_MAN,
    ];

    const CONTACT_MAP = [
        'WhatApp' => 'whatsapp',
        'WeChat' => 'wechat',
        'Facebook' => 'facebook',
        'Linkedin' => 'linkedin',
        'Skype' => 'skype',
    ];

    const ALI_CUSTOMER_PHASE_IM = 'IM';//询盘客户
    const ALI_CUSTOMER_PHASE_SAMPLE = 'SAMPLE';//样单客户
    const ALI_CUSTOMER_PHASE_DEAL = 'DEAL';//成交客户
    const ALI_CUSTOMER_PHASE_RE_PURCHASE = 'RE_PURCHASE';//复购客户

    /**
     *  阿里客户阶段映射, 注意阿里返回大小写不稳定, 需要装为大写判断
     */
    const CUSTOMER_PHASE_MAP = [
        'NULL_GROUP' => '', //未分组
        'ENQUIRY_GROUP' => self::ALI_CUSTOMER_PHASE_IM,  //询盘客户
        'SAMPLE_GROUP' => self::ALI_CUSTOMER_PHASE_SAMPLE,   //样单客户
        'DEAL_GROUP' => self::ALI_CUSTOMER_PHASE_DEAL, //成交客户
        'REPURCHASE_GROUP' => self::ALI_CUSTOMER_PHASE_RE_PURCHASE, //复购客户
    ];

    /**
     *  阿里客户年采购额映射, 注意阿里返回大小写不稳定, 需要装为大写判断
     */
    const ANNUAL_PROCUREMENT_MAP = [
        'NA' => Company::ANNUAL_PROCUREMENT_UNKNOWN,
        'EQUALS_0K' => Company::ANNUAL_PROCUREMENT_UNKNOWN,
        'BETWEEN_0K_AND_1K' => Company::ANNUAL_PROCUREMENT_BETWEEN_0K_AND_1K,
        'BETWEEN_1K_AND_5K' => Company::ANNUAL_PROCUREMENT_BETWEEN_1K_AND_5K,
        'BETWEEN_5K_AND_10K' => Company::ANNUAL_PROCUREMENT_BETWEEN_5K_AND_10K,
        'BETWEEN_10K_AND_30K' => Company::ANNUAL_PROCUREMENT_BETWEEN_10K_AND_30K,
        'BETWEEN_30K_AND_50K' => Company::ANNUAL_PROCUREMENT_BETWEEN_30K_AND_50K,
        'BETWEEN_50K_AND_100K' => Company::ANNUAL_PROCUREMENT_BETWEEN_50K_AND_100K,
        'BETWEEN_100K_AND_300K' => Company::ANNUAL_PROCUREMENT_BETWEEN_100K_AND_300K,
        'BETWEEN_300K_AND_500K' => Company::ANNUAL_PROCUREMENT_BETWEEN_BETWEEN_300K_AND_500K,
        'BETWEEN_500K_AND_1000K' => Company::ANNUAL_PROCUREMENT_BETWEEN_500K_AND_1000K,
        'BETWEEN_1000K_AND_5000K' => Company::ANNUAL_PROCUREMENT_BETWEEN_1000K_AND_5000K,
        'MORE_THAN_5000K' => Company::ANNUAL_PROCUREMENT_MORE_THAN_5000K,
    ];

    /**
     *  阿里客户意向映射, 注意阿里返回大小写不稳定, 需要装为大写判断
     */
    const INTENTION_LEVEL_MAP =[
        'ZERO_STAR' => Company::INTENTION_LEVEL_UNKNOWN,
        'ONE_STAR' => Company::INTENTION_LEVEL_LOW,
        'TWO_STAR' => Company::INTENTION_LEVEL_MIDDLE,
        'THREE_STAR' => Company::INTENTION_LEVEL_HIGH,
    ];

    /**
     * @deprecated
     * 阿里客户来源映射, 注意阿里返回大小写不稳定, 需要装为大写判断
     */
    const ORIGIN_MAP = [
        'NA' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN,
        'Alibaba' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA, //Alibaba
        'Ta' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,   //信保
        'Onestop' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM, //电商一站式服务
        'trade show offline' => '展会',  //线下展会, 非系统字段, 需要按名字匹配的
        'search' => '互联网',  //搜索引擎,非系统字段, 需要按名字匹配的
        'social platform' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_SOCIAL_PLATFORM,  //社交平台
        'e-commerce' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ELECTRICITY_PLATFORM,  //电商平台
        'feedback' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,  //Alibaba/询盘
        'rfq' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,  //Alibaba/RFQ
        'others' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_UNKNOWN,  //其他
        'tm' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,  //旺旺聊天
        'name_card' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,  //名片授权
        'inquiry' => \common\library\setting\library\origin\Origin::SYS_ORIGIN_ALIBABA,  //询盘客户
    ];

    const NOTE_TYPE_TASK = 1;
    const NOTE_TYPE_UNSETTLED =2;
    const NOTE_TYPE_DOCUMENTARY =3;
    const NOTE_TYPE_AFTER_SALES =4;

    const NOTE_NAME_MAP = [
        self::NOTE_TYPE_TASK => '洽谈中',
        self::NOTE_TYPE_UNSETTLED => '未成交',
        self::NOTE_TYPE_DOCUMENTARY => '跟单中',
        self::NOTE_TYPE_AFTER_SALES => '售后',
    ];

    const BASIC_INFO_ALLOWABLE = 1;
    const BASIC_INFO_DISABLE = 0;

    const CONTACT_INFO_ALLOWABLE = 1;
    const CONTACT_INFO_DISABLE = 0;


    public static function parserCompanyInfo(array $data)
    {

        $countryCode = $data['address']['country'] ?? '';

        $countryName = '';

        //$countryName = $countryCode ? \CountryService::getCountryByAlpha2($countryCode)->country_ename ?? '' : '';

        $archiveTime ='';
        if($data['gmt_create']??'' )
        {
            //注意!!!,这里是北京时间, 跟gmt_字段名没关系
            $archiveTime = $data['gmt_create'];
        }
        $info = [
            'company_name' => $data['company_name']??'',
            'alibaba_company_id' => $data['customer_id']??0,
            'fax' => $data['fax']??[],
            'full_fax' => trim(($data['fax']['country_code'] ?? '') . '-' . ($data['fax']['area_code'] ?? '')
                . '-' . ($data['fax']['number'] ?? '') . '-' . ($data['fax']['ext'] ?? ''), "-\t\n\r\0\x0B"),
            'homepage' => $data['website']??'',
            'country' => DataHelper::convertCountry($data['address']['country']??''),
            'province' => $data['address']['province']??'',
            'city' => $data['address']['city']??'',
            'district' => $data['address']['district']??'',
            'street' => $data['address']['street']??'',
            'address' =>  trim($countryName . ' ' . ($data['address']['province'] ?? '') . ' ' . ($data['address']['city'] ?? '') . ' ' . ($data['address']['district'] ?? '') . ' ' . ($data['address']['street'] ?? '')),
            'owner_email' => $data['owner_email']??'',
            'owner_account_id' => $data['owner_id_member_seq']??0,
            'trail_status' => self::CUSTOMER_PHASE_MAP[strtoupper($data['customer_phase']??'')]??'',
            'annual_procurement' => self::ANNUAL_PROCUREMENT_MAP[strtoupper($data['annual_procurement']??'')]??Company::ANNUAL_PROCUREMENT_UNKNOWN,
            'intention_level' => self::INTENTION_LEVEL_MAP[strtoupper($data['importance_level']??'')]??Company::INTENTION_LEVEL_UNKNOWN,
            'archive_time' => $archiveTime,
            'remark' => $data['remark']??'',
            'company_remark' => $data['remark'] ?? '',
            'source_list' => $data['source_list']['string']??[],
            'basic_info_allowable' => strtolower($data['basic_info_allowable']??'') == 'n'?self::BASIC_INFO_DISABLE:self::BASIC_INFO_ALLOWABLE,
            'contact_info_allowable' => strtolower($data['contact_info_allowable']??'') == 'n'?self::BASIC_INFO_DISABLE:self::BASIC_INFO_ALLOWABLE,
            'detail_url' => $data['alicrm_customer_detail_url']??'',
            // 待添加客户(中间态)
            'is_protecting' => (int)($data['in_protecting'] ?? 0),
            'growth_level' => strtoupper($data['growth_level'] ?? ''),
        ];

        return $info;
    }

    public static function parserCustomerInfo(array $contactInfo)
    {
        $telList = [];
        foreach ($contactInfo['mobile_list']['mobile_co'] ?? [] as $mobileItem)
        {
            if (empty($mobileItem['mobile_phone_num']))
                continue;

            $telList[] = [$mobileItem['mobile_country_code'] ?? '', $mobileItem['mobile_phone_num']];
        }

        foreach ($contactInfo['phone_number_list']['phone_co'] ?? [] as $phoneItem)
        {
            if (empty($phoneItem['number']))
                continue;

            $telList[] = [$phoneItem['country_code'] ?? '', trim(($phoneItem['area_code'] ?? '') . ' ' . $phoneItem['number'] . ' ' . ($phoneItem['ext'] ?? ''))];
        }


        $contact = [];
        foreach ($contactInfo['im_list']['ims_co'] ?? [] as $imItem)
        {
            $contact[] = [
                'type' => self::CONTACT_MAP[$imItem['social_type']??'']??strtolower($imItem['social_type']??''),
                'value' => $imItem['social_value'] ?? ''
            ];
        }
    
        $info = [
            'alibaba_customer_id'      => $contactInfo['id'] ?? 0,
            'name'                     => trim(($contactInfo['first_name'] ?? '') . ' ' . ($contactInfo['last_name'] ?? '')),
            'gender'                   => isset($contactInfo['gender']) && isset(self::GENDER_MAP[$contactInfo['gender']]) ? self::GENDER_MAP[$contactInfo['gender']] : Customer::GENDER_TYPE_UNKNOWN,
            'avatar_url'               => $contactInfo['avatar_url'] ?? '',
            'email'                    => current($contactInfo['email_list']['string'] ?? []) ?: '',
            'tel_list'                 => $telList,
            'full_tel_list'            => array_map(function ($elem) {
            
                return \Util::numericString(implode('', $elem));
            }, $telList),
            'contact'                  => $contact,
            'main_customer_flag'       => ($contactInfo['is_main'] ?? '') == 'y' ? 1 : 0,
            'post'                     => $contactInfo['position'] ?? '',
            'remark'                   => $contactInfo['memo'] ?? '',
            'alibaba_buyer_account_id' => $contactInfo['reference_id'] ?? 0,
            'buyer_account_encrypt'    => $contactInfo['encrypt_reference_id'] ?? '',
            'sec_token'                => $contactInfo['sec_token'] ?? '',
        ];

        return $info;
    }


    public static function  saveSyncTaskData($clientId, $taskId,array $syncData)
    {
        $dateTime = date('Y-m-d H:i:s');
        $taskDataModel = AlibabaCustomerSyncData::model()->find('task_id=:task_id', [':task_id'=> $taskId]);
        if( !$taskDataModel )
        {
            $taskDataModel = new AlibabaCustomerSyncData();
            $taskDataModel->client_id = $clientId;
            $taskDataModel->task_id = $taskId;
            $taskDataModel->create_time = $dateTime;
            $taskDataModel->company_ids  = snappy_compress(json_encode([]));
        }

        if(isset($syncData['private_company_ids']))
        {
            $taskDataModel->company_ids = snappy_compress(json_encode($syncData['private_company_ids']));
        }

        if(isset($syncData['public_company_ids']))
        {
            $taskDataModel->public_company_ids = snappy_compress(json_encode($syncData['public_company_ids']));
        }

        if(isset($syncData['fail_company_ids']))
        {
            $taskDataModel->fail_company_ids = snappy_compress(json_encode($syncData['fail_company_ids']));
        }


        if(isset($syncData['alibaba_company_ids']))
        {
            $taskDataModel->alibaba_company_ids = snappy_compress(json_encode($syncData['alibaba_company_ids']));
        }

        if(isset($syncData['alibaba_public_company_ids']))
        {
            $taskDataModel->alibaba_public_company_ids = snappy_compress(json_encode($syncData['alibaba_public_company_ids']));
        }

        if(isset($syncData['alibaba_private_company_ids']))
        {
            $taskDataModel->alibaba_private_company_ids = snappy_compress(json_encode($syncData['alibaba_private_company_ids']));
        }

        $taskDataModel->update_time = $dateTime;
        $taskDataModel->save();
    }


    public static function getSyncTaskCompanyIds($taskId)
    {
        $privateCompanyIds = [];
        $publicCompanyIds = [];
        $failCompanyIds = [];
        $taskDataModel = AlibabaCustomerSyncData::model()->find('task_id=:task_id', [':task_id'=> $taskId]);
        if(!$taskDataModel)
            return [$privateCompanyIds, $publicCompanyIds, $failCompanyIds];

        if( $taskDataModel->company_ids )
        {
            $privateCompanyJson = snappy_uncompress($taskDataModel->company_ids);
            $privateCompanyIds = json_decode($privateCompanyJson, true)?:[];
        }

        if( $taskDataModel->public_company_ids)
        {
            $publicCompanyJson = snappy_uncompress($taskDataModel->public_company_ids);
            $publicCompanyIds = json_decode($publicCompanyJson, true)?:[];
        }

        if( $taskDataModel->fail_company_ids)
        {
            $failCompanyJson = snappy_uncompress($taskDataModel->fail_company_ids);
            $failCompanyIds = json_decode($failCompanyJson, true)?:[];
        }

        return [$privateCompanyIds, $publicCompanyIds, $failCompanyIds];
    }


    public static function getSyncTaskAlibabaCompanyIds($taskId)
    {
        $alibabaCompanyIds = [];
        $taskDataModel = AlibabaCustomerSyncData::model()->find('task_id=:task_id', [':task_id'=> $taskId]);
        if(!$taskDataModel)
            return $alibabaCompanyIds;

        if( $taskDataModel->alibaba_company_ids)
        {
            $alibabaCompanyJson = snappy_uncompress($taskDataModel->alibaba_company_ids);
            $alibabaCompanyIds = json_decode($alibabaCompanyJson, true)?:[];
        }

        return $alibabaCompanyIds;
    }

    /**
     * 获取阿里
     * @param $sessionKey
     * @param $buyerAccountId
     * @return array
     */
    public static function getAlibabaBuyerInfo($sessionKey, $buyerAccountId)
    {

        if(!$sessionKey || !$buyerAccountId)
        {
            \LogUtil::error("[getAlibabaCustomerInfo] param empty sessionKey :{$sessionKey}  buyerAccountId: {$buyerAccountId}");
            return [];
        }
        $info = [];
        $top = AlibabaTopClient::getInstance();
        $data = $top->getCustomer($sessionKey, $buyerAccountId);
        $data = AlibabaTopClient::object2array($data);
        if( empty($data) || (isset($data['code']) && (isset($data['sub_code'])) || isset($data['sub_msg'])))
        {
            \LogUtil::error("[getAlibabaCustomerInfo] error sessionKey :{$sessionKey}  buyerAccountId: {$buyerAccountId} error:".json_encode($data));
            return $info;
        }
        if (empty($data['customer_id'] ?? 0) && ($data['remark'] ?? '') == 'B_CUSTOMER_NOT_EXIST') {
            \LogUtil::error("[getAlibabaCustomerInfo] error sessionKey :{$sessionKey}  buyerAccountId: {$buyerAccountId} customer not exist:" . json_encode($data));
            return $info;
        }

        $contactInfo = [];
        foreach ($data['contact_open_co_list']['contact_open_co']??[] as $datum )
        {
            if( ($datum['reference_id']??0) == $buyerAccountId)
            {
                $contactInfo = $datum;
                break;
            }
        }

        $contactInfo = $contactInfo?:$data['contact_open_co_list']['contact_open_co'][0]??[];

        $companyInfo = self::parserCompanyInfo($data);
        $customerInfo = self::parserCustomerInfo($contactInfo);

        return array_merge($companyInfo, $customerInfo);
    }

    public static function getSyncAlibabaCustomerInfo($clientId, $aliAccountId)
    {
        if(!$clientId || !$aliAccountId){
            \LogUtil::error("[getAlibabaCustomerInfo] empty client_id :{$clientId}  buyerAccountId: {$aliAccountId}");
            return false;
        }
        $db = AlibabaCustomerRelationModel::getDbByClientId($clientId);
        $table = AlibabaCustomerRelationModel::model()->tableName();
        $sql  ="select alibaba_customer_id,customer_id, store_id,alibaba_company_id,buyer_account_id,buyer_account_encrypt,buyer_email from {$table} where client_id=:client_id and buyer_account_id=:buyer_account_id";
        return  $db->createCommand($sql)->queryRow(true, [':client_id'=> $clientId, ':buyer_account_id'=> $aliAccountId])?:[];
    }

    public static function getCustomerSyncScheduled($clientId,  $storeId, $count=0, $archive_time_start = null, $archive_time_end = null)
    {
        $store = new AlibabaStore($clientId, $storeId);
        $sessionKey =  $store->getAccessToken();
        if( empty($sessionKey) )
        {
            throw new \RuntimeException('店铺未授权');
        }

        // 没有选择设置count，则一键同步
        if( !$count )
        {
            // 获取当前设置是否是建档时间的一键同步
            $customerSync = new AlibabaCustomerSyncSetting($clientId, $storeId);

            $listObj = new AlibabaCustomerList($sessionKey);

            if($customerSync->sync_customer_extent == Constant::CUSTOMER_SYNC_TYPE_BETWEEN_TIME)
            {
                $archiveTimeStart = date('Y-m-d', strtotime($archive_time_start?:$customerSync->archive_time_start));
                $archiveTimeEnd = date('Y-m-d 23:59:59', strtotime($archive_time_end?:$customerSync->archive_time_end));
                if($archiveTimeStart != '1970-01-01')
                {
                    $listObj->setGmtCreateStart($archiveTimeStart);
                    $listObj->setGmtCreateEnd($archiveTimeEnd);
                }
            }

            $count = $listObj->count();
        }

        //正常速度是60个一分钟
        $minute = ceil($count/60);

        return ['store_id'=> $storeId, 'customer_count'=> $count, 'minute'=> $minute, 'archive_time_start' => $archiveTimeStart ?? '', 'archive_time_end' => $archiveTimeEnd ?? ''];

    }

    public static function getBuyerCustomerEmail($buyerAccountId,  $aliCustomerData)
    {
        $customerEmail  = '';
        foreach ($aliCustomerData as $item){
            if(isset($item['reference_id']) && $item['reference_id'] == $buyerAccountId){
                if(isset($item['email_list']['string']) && isset($item['email_list']['string'][0])){
                    $customerEmail = $item['email_list']['string'][0];
                    return $customerEmail;
                }
            }
        }
        return $customerEmail;
    }

    public static function getCustomerSyncInitStatus($clientId,$user_id)
    {
        $aliPhaseMaps = \CustomerOptionService::aliCustomerPhaseMap();
//        $customerStatusListObj = new CustomerStatusList($clientId);
//        $customerStatusListObj->setUserId($user_id);
//        $customerStatusListObj->setItemName([
//            '询盘客户',
//            '样单客户',
//            '成交客户',
//            '复购客户'
//        ]);
//
//        $customerStatusListObj->getFormatter()->listSetting();
//        $customerStatusList = $customerStatusListObj->find();


//        $customerStatusMaps = array_column($customerStatusList,'item_id','item_name');

        $api = new StatusApi($clientId, \Constants::TYPE_COMPANY);
        $customerStatusMaps = $api->getIdMapByName([
            '询盘客户',
            '样单客户',
            '成交客户',
            '复购客户'
        ]);

        $result = [];
        foreach ($aliPhaseMaps as $key => $value)
        {
            if($key == self::ALI_CUSTOMER_PHASE_IM && isset($customerStatusMaps['询盘客户']))
            {
                $result[] = ['ali_status' => $key,'status' => $customerStatusMaps['询盘客户']];
            }else if ($key == self::ALI_CUSTOMER_PHASE_SAMPLE && isset($customerStatusMaps['样单客户']))
            {
                $result[] = ['ali_status' => $key,'status' => $customerStatusMaps['样单客户']];
            }else if($key == self::ALI_CUSTOMER_PHASE_DEAL && isset($customerStatusMaps['成交客户'])) {
                $result[] = ['ali_status' => $key, 'status' => $customerStatusMaps['成交客户']];
            }else if($key == self::ALI_CUSTOMER_PHASE_RE_PURCHASE && isset($customerStatusMaps['复购客户']))
            {
                $result[] = ['ali_status' => $key,'status' => $customerStatusMaps['复购客户']];
            }else {
                $result[] = ['ali_status' => $key,'status' => ''];
            }

        }

        return $result;
    }

    public static function getLastCustomerStatusSetting($clientId)
    {
        $sql = "select ct.customer_status From tbl_alibaba_customer_sync_setting  ct left join tbl_alibaba_store o on  ct.client_id =o.client_id where o.client_id = {$clientId} order by o.bind_time desc limit 1";
        $customerStatus =  \Yii::app()->db->createCommand($sql)->queryRow(true,[]);
        return $customerStatus?json_decode($customerStatus['customer_status'],true):[];
    }

    public static  function getCustomerSettingPoolMaps($clientId)
    {

        $sql = "select pool_id,store_id from tbl_alibaba_customer_sync_setting where client_id=:client_id order by create_time asc";
        $list =  \Yii::app()->db->createCommand($sql)->queryAll(true,[':client_id'=> $clientId]);
        $map = array_column($list, 'pool_id', 'store_id');
        return $map;
    }


    public static function getSyncAlibabaCompanyIdMap($clientId, $storeId, array $companyIds)
    {
        $listObj = new AlibabaCompanyRelationList($clientId);
        $listObj->setStoreId($storeId);
        $listObj->setCompanyId($companyIds);
        $listObj->setFields(['company_id','alibaba_company_id']);

        return array_column($listObj->find(),'alibaba_company_id','company_id');
    }

    public static function setAlibabaCompanySyncStatus($clientId,array $alibabaCompanyIds)
    {
        if( empty($alibabaCompanyIds))
            return;

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $table = \common\models\client\AlibabaCompanyRelation::model()->tableName();
        $sql = "update {$table} set sync_status=:sync_status where client_id=:client_id";
        $params = [':client_id' => $clientId, ':sync_status'=> Constant::SYNC_STATUS_RUNNING];
        SqlBuilder::buildIntWhere('','alibaba_company_id', $alibabaCompanyIds, $sql, $params);
        $db->createCommand($sql)->execute($params);
    }

    public static function getSyncPrivateCustomerCount($clientId, $storeId)
    {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $listObj = new CompanyList($adminUserId);
        $listObj->setSkipPrivilege(true);
        $listObj->setUserNum([1,2]);
        $listObj->setAliStoreId([$storeId]);
        return $listObj->count();
    }

    public static function getSyncPublicCustomerCount($clientId, $storeId)
    {
        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $listObj = new CompanyList($adminUserId);
        $listObj->setSkipPrivilege(true);
        $listObj->setUserNum([0]);
        $listObj->setAliStoreId([$storeId]);
        return $listObj->count();
    }

    /**
     * @param $clientId
     * @param $storeId
     * @return bool|\CDbDataReader|mixed|string
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function getLastPushUpdateTime($clientId, $storeId)
    {
        $key =  "alibaba:lastPushUpdateTime:{$clientId}-{$storeId}";
        $lastPushUpdateTime = \RedisService::getCacheData(\RedisService::cache(), $key, 600, function ()use($clientId, $storeId){

            // ali push event
            $syncEvents = [
                Constant::ALI_SYNC_CUST_ADD,
                Constant::ALI_SYNC_INFO_EDIT,
                Constant::ALI_SYNC_CONTACT_ADD,
                Constant::ALI_SYNC_CONTACT_UPDATE,
                Constant::ALI_SYNC_CUST_TRANSFER,
                Constant::ALI_SYNC_CUST_FRONZE
            ];
            $sql = "select max(sync_time) as last_puah_update_time from tbl_alibaba_sync_history where client_id=:client_id and store_id=:store_id and event in(".implode(',', $syncEvents).")";
            $lastPushUpdateTime = \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->queryScalar([
                ':client_id' => $clientId,
                ':store_id' => $storeId
            ]);
            return $lastPushUpdateTime;
        });

        return $lastPushUpdateTime;
    }

	/**
	 * 迁移阿里客户关联关系
	 * @param $clientId
	 * @param $fromCompanyId
	 * @param $toCompanyId
	 * @return bool|int
	 */
	public static function batchCopyCompanyId($clientId, $fromCompanyId, $toCompanyId, $dryRun = 0) {

		try {

			$db = \PgActiveRecord::getDbByClientId($clientId);

			$sql = "SELECT *
					FROM tbl_alibaba_company_relation
					WHERE client_id = $clientId
					  AND company_id = $fromCompanyId";


			if ($dryRun) {

				echo ('$sql: ' . $sql .'$params: '. '');

				return;
			}


			$list = $db->createCommand($sql)->queryAll();

			foreach ($list as $item) {

				$item['company_id'] = $toCompanyId;

				$item['relation_id'] = intval(\ProjectActiveRecord::produceAutoIncrementId());

				$relation = (new AlibabaCompanyRelation($clientId));

				$relation->setAttributes($item);

				$relation->save();
			}

			\LogUtil::info("[batchCopyCompanyId] from: {$fromCompanyId} to: {$toCompanyId}");

			return true;

		} catch (\Exception $e) {
			return false;
		}
	}

    /**
     * 迁移阿里客户关联关系
     * @param $clientId
     * @param $fromCompanyId
     * @param $toCompanyId
     * @return bool|int
     */
    public static function batchChangeCompanyId($clientId, $fromCompanyId, $toCompanyId)
    {
        try {
            $date = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "UPDATE tbl_alibaba_company_relation SET company_id=:toCompanyId, update_time=:updateTime "
                . "WHERE client_id=:client_id AND company_id=:fromCompanyId";
            $params = [
                ':client_id' => $clientId,
                ':fromCompanyId' => $fromCompanyId,
                ':toCompanyId' => $toCompanyId,
                ':updateTime' => $date,
            ];
            $res = $db->createCommand($sql)->execute($params);

            \LogUtil::info("[batchChangeCompanyId] from: {$fromCompanyId} to: {$toCompanyId}");
            return $res;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 解除阿里客户关联关系
     * @param $clientId
     * @param array $fromCompanyIds
     * @param int $syncSwitchFlag
     * @return bool|int
     */
    public static function batchUnbindCompanyId($clientId, array $fromCompanyIds, int $syncSwitchFlag=1)
    {
        if( empty($fromCompanyIds))
            return false;

        try {
            $date = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "UPDATE tbl_alibaba_company_relation SET company_id=0, update_time=:updateTime,sync_switch_flag=:syncSwitchFlag "
                . "WHERE client_id=:client_id AND company_id in  (".implode(',',$fromCompanyIds).')';
            $params = [
                ':client_id' => $clientId,
                ':updateTime' => $date,
                ':syncSwitchFlag' => $syncSwitchFlag,
            ];
            $res = $db->createCommand($sql)->execute($params);

            // 更新用户关联社交账号表
            $sql = "UPDATE tbl_user_customer_contact SET company_id=0, customer_id=0, update_time=:updateTime
                     WHERE  client_id=:client_id AND company_id in  (".implode(',',$fromCompanyIds).")
                     AND company_id <> 0";
            $params = [
                ':client_id' => $clientId,
                ':updateTime' => $date,
            ];
            $db->createCommand($sql)->execute($params);


            \LogUtil::info("[batchUnbindCompanyId] from: ".implode(',', $fromCompanyIds));
            return $res;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     *  迁移阿里客户联系人关联关系
     * @param $clientId
     * @param $fromCustomerId
     * @param $toCustomerId
     * @return bool|int
     */
    public static function batchChangeCustomerId($clientId, $fromCustomerId, $toCustomerId)
    {
        try {
            $date = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "UPDATE tbl_alibaba_customer_relation SET customer_id=:toCustomerId, update_time=:updateTime "
                . "WHERE client_id=:client_id  and customer_id=:fromCustomerId";
            $params = [
                ':client_id' => $clientId,
                ':fromCustomerId' => $fromCustomerId,
                ':toCustomerId' => $toCustomerId,
                ':updateTime' => $date,
            ];
            $res = $db->createCommand($sql)->execute($params);

            \LogUtil::info("[batchChangeCustomerId] from: {$fromCustomerId} to: {$toCustomerId}");
            return $res;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     *  解除阿里客户联系人关联关系
     * @param $clientId
     * @param array $fromCustomerIds
     * @return bool|int
     */
    public static function batchUnbindCustomerId($clientId,array $fromCustomerIds)
    {
        if( empty($fromCustomerIds))
            return false;

        try {
            $date = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($clientId);
            $sql = "UPDATE tbl_alibaba_customer_relation SET customer_id=0, update_time=:updateTime "
                . "WHERE client_id=:client_id  and customer_id in (".implode(',', $fromCustomerIds).')';
            $params = [
                ':client_id' => $clientId,
                ':updateTime' => $date,
            ];
            $res = $db->createCommand($sql)->execute($params);

            // TODO: 要加索引
            // 更新用户关联社交账号表
            $sql = "UPDATE tbl_user_customer_contact SET customer_id=0, update_time=:updateTime
                     WHERE client_id=:client_id  and customer_id in (".implode(',', $fromCustomerIds).')';
            $db->createCommand($sql)->execute($params);

            \LogUtil::info("[batchUnbindCustomerId] from:".implode(',', $fromCustomerIds));
            return $res;
        } catch (\Exception $e) {
            return false;
        }
    }


    /**
     * 重新绑定客户联系人关联关系(TM, WhatsApp)
     * @param $clientId
     * @param $userId
     * @param $companyId
     * @param $customer_id
     * @param $contact
     * @return false
     */
    public static function rebindCustomerIdRelation($clientId, $userId, $companyId, $customerId, $contact)
    {
        if (empty($companyId) || empty($customerId)) {
            return false;
        }

        try {
            $date = date('Y-m-d H:i:s');
            $db = \PgActiveRecord::getDbByClientId($clientId);

            $relation = new AlibabaTradeRelation($clientId);
            $relation = $relation->loadByCompanyId($companyId);
            if (!$relation->isNew() && !empty($relation->buyer_account_id)) {
                $buyerAccountId = $relation->buyer_account_id;
                $sql = " UPDATE tbl_alibaba_customer_relation SET customer_id = :customer_id, update_time = :update_time
                                WHERE client_id = :client_id  AND buyer_account_id = :buyer_account_id ";
                $params = [
                    ':client_id' => $clientId,
                    ':update_time' => $date,
                    ':customer_id' => $customerId,
                    ':buyer_account_id' => $buyerAccountId
                ];
                $res = $db->createCommand($sql)->execute($params);
                \LogUtil::info("[rebindCustomerIdRelation_TM] customer_id: {$customerId} buyer_account_id: {$buyerAccountId} res: $res");
            }

            $contact = array_filter($contact, function ($item) {
                return ($item['type'] ?? '') == SnsConstants::SNS_CLIENT_WHATSAPP && !empty($item['value']) ;
            });
            if (!empty($contact)) {
                foreach ($contact as $item) {
                    $snsId = $item['value'];
                    $sql = " UPDATE tbl_user_customer_contact SET customer_id = :customer_id, update_time = :update_time
                                  WHERE client_id = :client_id AND user_id = :user_id AND sns_type = :sns_type AND sns_id = :sns_id ";
                    $params = [
                        ':client_id' => $clientId,
                        ':update_time' => $date,
                        ':sns_id' => $snsId,
                        ':user_id' => $userId,
                        ':sns_type' => SnsConstants::SNS_CLIENT_WHATSAPP,
                        ':customer_id' => $customerId
                    ];
                    $res = $db->createCommand($sql)->execute($params);
                    \LogUtil::info("[rebindCustomerIdRelation_whatsapp] customer_id: {$customerId} sns_id: {$snsId} res: $res");
                }
            }
        } catch (\Exception $e) {
            return false;
        }
        return false;
    }

    public static function updateCompanyDetailUrl($clientId, $alibabaCompanyId,$detailUrl)
    {
        if( empty($detailUrl) )
            return;

        $db = \PgActiveRecord::getDbByClientId($clientId);
        $sql = "UPDATE tbl_alibaba_company_relation SET detail_url=:detail_url"
            ." WHERE client_id=:client_id  and alibaba_company_id=:alibaba_company_id";
        $params = [
            ':client_id' => $clientId,
            ':alibaba_company_id' => $alibabaCompanyId,
            ':detail_url' => $detailUrl,
        ];

        $db->createCommand($sql)->execute($params);
    }

    /**
     * @param $seller_account_id
     * @return \CDbDataReader|mixed
     * @throws \CException
     */
    public static function getClientStoreBySellAccountId($seller_account_id)
    {
        $aouthFlag = [Constant::OAUTH_FLAG_AUTH, Constant::OAUTH_FLAG_BIND_AND_AUTH];
        $sql = "select a.client_id,a.store_id,a.user_id,a.access_token from tbl_alibaba_account a
                join tbl_alibaba_store s on a.store_id=s.store_id and a.client_id=s.client_id
                where a.taobao_user_id=:taobao_account_id and a.oauth_flag in(".implode(',', $aouthFlag).") and s.enable_flag=:store_enable_flag limit 1";
        $params = [
            ':taobao_account_id' => $seller_account_id,
            ':store_enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
        ];
        return \Yii::app()->db->createCommand($sql)->queryRow(true, $params);
    }
    
    /**
     * @param $seller_account_id
     * @return \CDbDataReader|mixed
     * @throws \CException
     */
    public static function getAllClientStore() {
        
        $aouthFlag = [Constant::OAUTH_FLAG_AUTH, Constant::OAUTH_FLAG_BIND_AND_AUTH];
        $sql = "select a.client_id,a.store_id,a.user_id,a.access_token, a.taobao_user_id from tbl_alibaba_account a
                join tbl_alibaba_store s on a.store_id=s.store_id and a.client_id=s.client_id
                where a.oauth_flag in(" . implode(',', $aouthFlag) . ") and s.enable_flag=:store_enable_flag";
        $params = [
            ':store_enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
        ];
        return \Yii::app()->db->createCommand($sql)->queryAll(true, $params);
    }

    /**
     * 是否有邮件动态
     * @param $clientId
     * @param $companyId
     * @param $customerId
     * @param int $module
     * @return bool
     */
    public static function hasCompanyTrail($clientId, $companyId, $customerId, $module = \common\library\trail\TrailConstants::MODULE_MAIL)
    {
        return \common\library\trail\Helper::hasCompanyTrail($clientId, $companyId, $customerId, $module);
    }


    /**
     * 初次同步标识
     * @param $clientId
     * @param $storeId
     * @return int
     */
    public static function getFirstTimeFlag($clientId, $storeId)
    {
        //初次同步标识
        $firstTimeFlag = Constant::CUSTOMER_SYNC_IS_FIRST;

        $listObj = new AlibabaCompanyRelationList($clientId);
        $listObj->setStoreId($storeId);
        $listObj->setIsSync(true);
        $listObj->setLimit(1);
        if(!empty($listObj->find())){
            $firstTimeFlag = Constant::CUSTOMER_SYNC_NONE_FIRST;
        }

        return $firstTimeFlag;
    }

    /**
     * 初始化多店铺字段必填设置
     * @param $clientId
     * @param $userId
     * @return void
     */
    public static function initCustomerMultiStoreFieldSetting($clientId, $userId)
    {
        $api = new \common\library\setting\library\config\ConfigApi($clientId, Constants::TYPE_CUSTOMER);
        $api->setOpUser($userId);
        $api->saveConfig(0, 0,ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_TACTIC, ConfigConstant::CUSTOMER_MULTI_STORE_CUS_FIELD);
        $requireField = [];
        $fieldInfoList = \common\library\custom_field\Helper::getFieldListInfo($clientId, 0, \Constants::TYPE_CUSTOMER, 1, ['email','tel_list']);
        $fieldRequireMap = array_column($fieldInfoList, 'require', 'id');

        $requireField['company.name'] = 1;
        $requireField['customer.email'] = (isset($fieldRequireMap['email']) && $fieldRequireMap['email']) ? 1 : 0;
        $requireField['customer.tel_list'] = (isset($fieldRequireMap['tel_list']) && $fieldRequireMap['tel_list']) ? 1 : 0;
        $api->saveConfig(0, 0,ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_SETTING, '',[ConfigConstant::FIELD_LIST_KEY => $requireField]);

        LogUtil::info(sprintf("InitCustomerSyncMultiStoreField client_id[%s] user_id[%s] tactic[%s] setting[%s]",
            $clientId,$userId,ConfigConstant::CUSTOMER_MULTI_STORE_CUS_FIELD,json_encode($requireField)));
    }

    /**
     * 获取客户通同步 CRM系统所需必填字段
     * @param $clientId
     * @return array
     */
    public static function getCustomerSyncCrmRequiredField($clientId)
    {
        $companyFieldInfoList = \common\library\custom_field\Helper::getFieldListInfo($clientId, 0, \Constants::TYPE_COMPANY, 1);
        $companyFieldMap = array_column($companyFieldInfoList, null, 'id');
        $customerFieldInfoList = \common\library\custom_field\Helper::getFieldListInfo($clientId, 0, \Constants::TYPE_CUSTOMER, 1);
        $customerFieldMap = array_column($customerFieldInfoList, null, 'id');

        $aliSyncFieldList = \common\library\setting\library\config\ConfigConstant::CUSTOMER_MULTI_STORE_CUS_FIELD_LIST;
        $result = [];
        foreach ($aliSyncFieldList as $field)
        {
            $fieldInfo = $field['type'] == \Constants::TYPE_COMPANY ? ($companyFieldMap[$field['id']] ?? []) : ($customerFieldMap[$field['id']] ?? []);
            $mapId = $field['type'] == \Constants::TYPE_COMPANY ? 'company.'.$field['id'] : 'customer.'.$field['id'];
            $result[] = [
                'id' => $field['id'],
                'type' => $field['type'],
                'ali_name' => $field['ali_name'],
                'require' => $fieldInfo['require'] ?? 0,
                'map_id' => $mapId,
                'name' => $fieldInfo['name'] ?? '',
                'base' =>  $fieldInfo['base'] ?? 0,
            ];
        }
        return $result;
    }

    /**
     * 获取多店铺 客户通同步 必填字段（两种策略crm ,custom）
     * @param $clientId
     * @param $tactic
     * @return array
     */
    public static function getMultiStoreRequiredField($clientId, $tactic)
    {
        $customerSyncCrmRequireField = \common\library\alibaba\customer\CustomerSyncHelper::getCustomerSyncCrmRequiredField($clientId);
        $crmRequiredField = array_column($customerSyncCrmRequireField,null,'map_id');
        switch ($tactic) {
            case ConfigConstant::CUSTOMER_MULTI_STORE_CRM_FIELD:
                $requiredField = $crmRequiredField;
                break;
            case ConfigConstant::CUSTOMER_MULTI_STORE_CUS_FIELD:
                $multiStoreFieldSetting = (new ConfigApi($clientId, Constants::TYPE_CUSTOMER))->getConfigByKey(0,0,ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_SETTING,[ConfigConstant::FIELD_LIST_KEY])[ConfigConstant::FIELD_LIST_KEY] ?? [];
                $multiStoreFieldSetting = is_string($multiStoreFieldSetting) ? (json_decode($multiStoreFieldSetting, true) ?? []) : $multiStoreFieldSetting;
                foreach ($crmRequiredField as $map_id => $requireField) {
                    $crmRequiredField[$map_id]['require'] = (isset($multiStoreFieldSetting[$map_id]) && $multiStoreFieldSetting[$map_id]) ? 1 : 0;
                }
                $requiredField = $crmRequiredField;
                break;
            default:
                //默认配置
                $requiredField = ['customer.email' => $crmRequiredField['customer.email'],'customer.tel_list' => $crmRequiredField['customer.tel_list']];
                break;
        }
        return $requiredField;
    }
}
