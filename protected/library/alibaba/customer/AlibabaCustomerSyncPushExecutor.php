<?php
/**
 * Created by PhpStorm.
 * User: bing
 * Date: 2021/06/18
 * Time: 10:03 AM
 */

namespace common\library\alibaba\customer;

use common\library\alibaba\Constant;
use common\library\alibaba\services\message\DelayMessageProcess;
use common\library\alibaba\store\AlibabaStore;
use common\library\privilege_v3\PrivilegeService;
use common\library\report\sensors\events\EventCreateCompany;
use User;

class AlibabaCustomerSyncPushExecutor
{
    /**
     * @var
     */
    protected $clientId;

    /**
     * @var
     */
    protected $userId;

    /**
     * @var array
     */
    protected $pushData;

    /**
     * @var string
     */
    protected $accessToken;

    /**
     * @var AlibabaStore
     */
    protected $store;

    /**
     * @var AlibabaCustomerSyncProcessor
     */
    protected $processor;

    /**
     * @var string
     */
    protected $event;

    /**
     * @var array
     */
    protected $eventMapAli;
    /**
     * @var int|mixed|string
     */
    protected $scene = \common\library\alibaba\customer\AlibabaCustomerSyncProcessor::SCENE_NORMAL;
    
    /**
     * AlibabaCustomerSyncPushExecutor constructor.
     * @param array $pushData
     * @throws \CException
     */
    public function __construct(
        $clientId,
        $storeId,
        $userId,
        $accessToken,
        array $pushData,
        $scene = \common\library\alibaba\customer\AlibabaCustomerSyncProcessor::SCENE_NORMAL
    )
    {
        $this->pushData = $pushData;
        // ali 推送的是字符串event_code,okki保存为int类型
        $this->event = $this->parseEvent($pushData['sync_type']);
        $this->clientId = $clientId;
        $this->accessToken = $accessToken;

        $this->userId = $userId;
    
        $this->scene = $scene ?: \common\library\alibaba\customer\AlibabaCustomerSyncProcessor::SCENE_NORMAL;
        
        $this->store = new AlibabaStore($this->clientId, $storeId);

        $this->processor = new AlibabaCustomerSyncProcessor($this->clientId, $this->userId ?? $this->store->create_user, $this->store->store_id);
    
        $this->processor->setScene($this->scene);
    }

    protected function parseEvent($syncType)
    {
        if(is_null($this->eventMapAli))
        {
            foreach(Constant::ALI_SYNC_EVENT_MAP as $eventType => $item)
            {
                $this->eventMapAli[$item['event_code']] = $eventType;
            }
        }
        return $this->eventMapAli[$syncType] ?? $syncType;
    }

    /**
     * @return string
     */
    protected function getAccessToken()
    {
        if(!$this->accessToken)
        {
            if(!$this->store->isAuth())
            {
                throw new \RuntimeException('店铺已被取消授权');
            }

            $this->accessToken = $this->store->getAccessToken();

            if(empty($this->accessToken))
            {
                throw new \RuntimeException(\Yii::t('alibaba', 'Store authorization has expired'));
            }

        }

        return $this->accessToken;
    }


    protected function begin()
    {
        $this->processor->setSessionKey($this->getAccessToken());
        $this->processor->setStore($this->store);
        $this->processor->setPushData($this->pushData);
        $this->processor->initDefaultSetting($this->store->getCustomerSyncObj()->pool_id,json_decode($this->store->getCustomerSyncObj()->customer_status, true)?:[], true);
        $this->processor->setSyncType(Constant::SYNC_TYPE_ALI_PUSH);
        // ALi 推送增量更新
        $this->processor->setUpdateType($this->store->getCustomerSyncObj()->update_type);
        $this->processor->setUpdateStatusFlag($this->store->getCustomerSyncObj()->update_status_flag);
        // 当前任务开始时间
        $this->processor->setSyncTime(date('Y-m-d H:i:s'));
        $this->processor->setEvent($this->event);
    }

    /**
     * 执行
     */
    public function run()
    {
    
        if ($this->scene != \common\library\alibaba\customer\AlibabaCustomerSyncProcessor::SCENE_OFFLINE_DATA_CHANGED) {
    
            // 查询是否存在延迟消息并消费
            $delayMessageProcess = new DelayMessageProcess($this->clientId,$this->userId,$this->store->store_id);
            $delayMessageProcess->setBuyerAccountId($this->pushData['buyer_member_seq'] ?? 0);
            $delayMessageProcess->setCustomerId($this->pushData['customer_id']);
            $delayMessageProcess->setType(Constant::DELAY_MESSAGE_TYPE_SYNC_CUSTOMER_FOR_LEAD);
            $delayMessageProcess->process();
        }
        

        // 没有开启接收数据同步，则禁止往下同步
        if(!$this->store->sync_customer_flag)
        {
            \LogUtil::error("【Ali Push】sync_customer_flag c_id={$this->clientId} s_id={$this->store->store_id},flag={$this->store->sync_customer_flag},alibabaCompanyId={$this->pushData['customer_id']} store_name={$this->store->store_name} 没有开启接收更新推送");
//            throw new \RuntimeException("【Ali Push】client_id={$this->clientId},store_id={$this->store->store_id},store_name={$this->store->store_name} 没有开启接收更新推送");
            return ;
        }

        try{
            $this->begin();
            $aliCompanyId = $this->pushData['customer_id'];
            $this->process($aliCompanyId);
        }catch (\Exception $e) {
            //错误上报
            if (!$e instanceof \RuntimeException) {
                $userId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), $userId);
            }
            
            \LogUtil::error("【Ali Push】 alibabaCompanyId={$this->pushData['customer_id']}  process error " . $e->getMessage() . $e->getTraceAsString());
            if ($this->processor->getSyncHistoryObject()->sync_id) {
                $this->processor->getSyncHistoryObject()->sync_status = Constant::SYNC_STATUS_FAIL;
                $this->processor->getSyncHistoryObject()->fail_content = $e->getMessage();
            }
    
            $error = $this->processor->getError();
    
            if (isset($error['alibaba_company_id']) && ($error['msg'] ?? '') == '客户不存在') {
        
                throw $e;
            }
        }
    }

    /**
     * @param $aliCompanyId
     * @return bool
     */
    public function process($aliCompanyId)
    {
        $ret = $this->processor->processWithLock($aliCompanyId, [], true);
        if(!$ret)
        {
            $error = $this->processor->getError();
            if( isset($error['relation_company_id']) &&$error['relation_company_id'])
            {
                //记录同步失败的company_id
                \LogUtil::error("【Ali Push】Fail companyIds,clientId={$this->clientId}, relation_company_id={$error['relation_company_id']}");
            }
    
            if (isset($error['alibaba_company_id']) && $error['msg'] == '客户不存在') {
        
                throw new \RuntimeException(json_encode($error));
            }

            return false;
        }

        $result = $this->processor->getResult();
        $pairInfo = $this->processor->getPairInfo();
        \LogUtil::info('sync company finish '. ($result['company_id']??'').'  aliCompany:'.($result['alibaba_company_id']??''). 'OwnerEmail:'.($result['alibaba_owner_email']??'').'customerIds:'.json_encode($result['save_customer_map']??[])
            .'isAdd:'. ($result['is_add']??'') .' isPublic:'.($result['is_public']??'') .'sync_note_count: '.($result['sync_note_count']??'') .'pairSource:'.($pairInfo['source']??'') .'pairItem: '.json_encode($pairInfo['item']??[]));
        // 新增客户事件
        if($this->event == Constant::ALI_SYNC_CUST_ADD && isset($result['is_add']) && (bool)$result['is_add'] === true)
        {
            // web端|ali同步
            $sensorsEvent = new EventCreateCompany($this->clientId, PrivilegeService::getInstance($this->clientId)->getAdminUserId());
            $sensorsEvent->setParams([
                'platform_type' => EventCreateCompany::PLATFORM_WEB,
                'company_number' => 1,
                'action_type' => EventCreateCompany::ACTION_TYPE_ALI,
            ]);
            $sensorsEvent->report();
        }
    }

    public function getProcessor()
    {
        return $this->processor;
    }

}
