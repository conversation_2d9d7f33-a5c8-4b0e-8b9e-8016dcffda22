<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/12
 * Time: 11:21 AM
 */

namespace common\library\alibaba\customer;


use common\library\util\SqlBuilder;
use common\models\client\AlibabaCustomerRelation as AlibabaCustomerRelationModel;

class AlibabaCustomerRelationList extends \MysqlList
{
    protected $clientId;
    protected $storeId;
    protected $alibabaCompanyId;
    protected $alibabaCustomerId;
    protected $customerId;
    protected $buyerAccountId;
    protected $existsBuyerAccountId;

    protected $fields;

    /**
     * AlibabaCompanyRelationList constructor.
     * @param $clientId
     */
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    public function getQueryTable()
    {
        return AlibabaCustomerRelationModel::model()->tableName();
    }

    /**
     * @param mixed $fields
     */
    public function setFields(array $fields)
    {
        $this->fields = $fields;
    }


    /**
     * @param mixed $storeId
     */
    public function setStoreId($storeId)
    {
        $this->storeId = $storeId;
    }

    /**
     * @param mixed $alibabaCompanyId
     */
    public function setAlibabaCompanyId($alibabaCompanyId)
    {
        $this->alibabaCompanyId = $alibabaCompanyId;
    }

    /**
     * @param mixed $alibabaCustomerId
     */
    public function setAlibabaCustomerId($alibabaCustomerId)
    {
        $this->alibabaCustomerId = $alibabaCustomerId;
    }


    /**
     * @param mixed $buyerAccountId
     */
    public function setBuyerAccountId($buyerAccountId)
    {
        $this->buyerAccountId = $buyerAccountId;
    }

    public function existsBuyerAccountId($flag)
    {
        $this->existsBuyerAccountId = $flag;
    }

    /**
     * @param mixed $customerId
     */
    public function setCustomerId($customerId)
    {
        $this->customerId = $customerId;
    }


    public function buildParams()
    {
        $sql = 'client_id=:client_id';
        $params =[':client_id' => $this->clientId];

        if( $this->alibabaCustomerId )
        {
            SqlBuilder::buildIntWhere('', 'alibaba_customer_id',$this->alibabaCustomerId,$sql, $params);
        }

        if( $this->buyerAccountId )
        {
            SqlBuilder::buildIntWhere('', 'buyer_account_id',$this->buyerAccountId,$sql, $params);
        }


        if( $this->customerId )
        {
            SqlBuilder::buildIntWhere('', 'customer_id',$this->customerId,$sql, $params);
        }

        if( $this->alibabaCompanyId )
        {
            SqlBuilder::buildIntWhere('', 'alibaba_company_id',$this->alibabaCompanyId,$sql, $params);
        }

        if ($this->existsBuyerAccountId) {
            $sql .= ' and buyer_account_id>0';
        }

        if( $this->storeId )
        {
            SqlBuilder::buildIntWhere('', 'store_id',$this->storeId,$sql, $params);
        }

        return [$sql, $params];
    }


    public function find()
    {
        list($where,  $params) = $this->buildParams();

        $db = AlibabaCustomerRelationModel::getDbByClientId($this->clientId);
        $limit  = $this->buildLimit();
        $orderBy = $this->buildOrderBy();
        $table = $this->getQueryTable();

        if (empty($this->fields)) {
            $fields = '*';
        } else {
            $fields = implode(',', $this->fields);
        }
        $sql = "SELECT $fields FROM {$table} WHERE {$where} {$orderBy} {$limit}";

        $result = $db->createCommand($sql)->queryAll(true, $params)?:[];
        return $result;
    }

    public function count()
    {
        list($where,  $params) = $this->buildParams();

        $db = AlibabaCustomerRelationModel::getDbByClientId($this->clientId);
        $table = $this->getQueryTable();

        $sql = "SELECT count(1) FROM {$table} WHERE {$where}";

        $result = $db->createCommand($sql)->queryScalar( $params);
        return intval($result);
    }

}
