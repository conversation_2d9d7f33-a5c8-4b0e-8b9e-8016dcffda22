<?php
/**
 * Created by PhpStorm.
 * User: june
 * Date: 2021/11/2
 * Time: 3:30 PM
 */

namespace common\library\alibaba\customer;


use common\library\alibaba\services\AlibabaCustomerList;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\store\AlibabaStoreMembersList;
use common\library\customer_v3\company\CompanyService;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\object\field\service\ScopeUserFieldService;
use LogUtil;

class SyncOwnerExecutor
{

    protected $clientId;
    protected $storeId;
    protected $settingMap;
    protected $totalCount = 0;
    /**
     * @var AlibabaStore
     */
    protected $store;


    public function __construct($clientId,$storeId)
    {
        $this->clientId = $clientId;
        $this->storeId = $storeId;

        $this->store = new AlibabaStore($this->clientId, $this->storeId);
        // 获取配置
        $this->settingMap = $this->getSettingMap();
    }

    protected function getAccessToken()
    {
        $alibabaStore = $this->store;
        if( !$alibabaStore->isEnable() )
        {
            throw new \RuntimeException('店铺已被取消授权');
        }

        $token = $alibabaStore->getAccessToken();
        if( empty($token) )
            throw new \RuntimeException(\Yii::t('alibaba', 'Store authorization has expired'));

        return $token;
    }



    public function run()
    {
        $adminUserId = \common\library\privilege_v3\PrivilegeService::getInstance($this->clientId)->getAdminUserId();
        \User::setLoginUserById($adminUserId);

        $accessToken = $this->getAccessToken();

        $begin = 0;
        $lastTime = '1998-01-01'; //全量同步
        $listObj = new AlibabaCustomerList($accessToken);
        $listObj->setPageSize(50);
        do
        {
            $listObj->setCustomerIdBegin($begin);
            $listObj->setLastSyncEndTime($lastTime);

            $rsp = $listObj->find();
            $rsp = is_object($rsp)? AlibabaTopClient::object2array($rsp) :$rsp;

            if( isset($rsp['total']) && !$begin )
            {
                $this->totalCount = $rsp['total'];
            }

            $dataList= $rsp['data_list']['customer_open_dto']??[];

            \LogUtil::info("[SyncOwnerExecutor] client_id:{$this->clientId},store_id:{$this->storeId},totalCount:{$this->totalCount},begin:{$begin}");

            foreach ( $dataList as $data)
            {
                $begin = $data['customer_id'];
                $this->process($data);
            }

        }while(!empty($dataList));

    }


    protected function process($data)
    {
        $pgDb = \PgActiveRecord::getDbByClientId($this->clientId);
        $companyRelation = (new AlibabaCompanyRelation($this->clientId))->loadByAlibabaCompanyId($data['customer_id']);

        if($companyRelation->isNew() || !$companyRelation->company_id)
        {
            return ;
        }

        $companyInfo = CustomerSyncHelper::parserCompanyInfo($data);

        $ownerAccountId = $companyInfo['owner_account_id'] ?? 0;

        $companyRelation->alibaba_company_name = $companyInfo['company_name'] ?: $companyRelation->alibaba_company_name;
        $companyRelation->owner_email = $companyInfo['owner_email'] ?? '';
        $companyRelation->owner_account_id = $ownerAccountId;
        $companyRelation->basic_info_allowable = $companyInfo['basic_info_allowable'];
        $companyRelation->contact_info_allowable = $companyInfo['contact_info_allowable'];
        $companyRelation->sync_count++;
        $companyRelation->update_time = date('Y-m-d H:i:s');
        $companyRelation->sync_time = date('Y-m-d H:i:s');
        $companyRelation->detail_url = $companyInfo['detail_url'];
        $companyRelation->is_protecting = $companyInfo['is_protecting'];
        $companyRelation->save();

        if( !isset($this->settingMap[$ownerAccountId]) )
        {
            return ;
        }
        $userId = $this->settingMap[$ownerAccountId];

        $oldCompany = $pgDb->createCommand("select user_id from tbl_company where client_id={$this->clientId} and company_id={$companyRelation->company_id} and is_archive=1")->queryRow();

        $companySql = "update tbl_company set user_id='{ $userId }' WHERE client_id={$this->clientId} and company_id={$companyRelation->company_id} and is_archive=1";
        $customerSql = "update tbl_customer set user_id='{ $userId }' where client_id={$this->clientId} and company_id={$companyRelation->company_id} and is_archive=1";

        $pgDb->createCommand($companySql)->execute();
        $pgDb->createCommand($customerSql)->execute();
        
        // 刷新scope_user_ids
//        $scopeUserService = new ScopeUserFieldService($this->clientId, new CompanyMetadata($this->clientId));
//        $scopeUserService->refreshScopeUserIdsByPids([$companyRelation->company_id]);

        \LogUtil::info("[SyncOwnerExecutor] client_id:{$this->clientId},store_id:{$this->storeId},company_id:{$companyRelation->company_id},before:{$oldCompany['user_id']},after:{$userId}");
    }

    protected function getSettingMap()
    {
        $storeMemberListObj = new AlibabaStoreMembersList($this->clientId);
        $storeMemberListObj->setStoreId($this->storeId);
        $storeMemberListObj->setFields(['user_id','seller_account_id']);
        $storeMemberSettings = $storeMemberListObj->find();
        return array_column($storeMemberSettings, 'user_id', 'seller_account_id');
    }




}
