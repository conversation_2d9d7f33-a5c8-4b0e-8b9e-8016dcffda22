<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/6
 * Time: 3:03 PM
 */

namespace common\library\alibaba\customer;


use common\library\alibaba\Constant;
use common\library\alibaba\exception\TaskAlreadyRunException;
use common\library\alibaba\services\AlibabaCustomerList;
use common\library\alibaba\services\AlibabaTopClient;
use common\library\alibaba\store\AlibabaStore;
use common\library\notification\Notification;
use common\library\notification\PushHelper;
use common\library\report\error\ErrorReport;
use common\library\report\sensors\events\EventCreateCompany;
use LogUtil;

class AlibabaCustomerSyncTaskExecutor
{
    const OP_TYPE_ERROR = 1;
    const OP_TYPE_IGNORE = 2;
    const OP_TYPE_WARN  = 3;
    const OP_TYPE_REMARK  = 4;

    protected $clientId;
    /**
     * @var AlibabaCustomerSyncTask
     */
    protected $task;
    /**
     * @var AlibabaStore
     */
    protected $store;
    /**
     * @var AlibabaCustomerSyncProcessor
     */
    protected $processor;

    //ali本次需要同步的公海客户
    protected $aliPublicCount;
    //ali本次需要同步的私海客户
    protected $aliPrivateCount;

    // Ali所有的需要同步客户
    protected $totalCount;

    // 待添加客户数量
    protected $protectingCount = 0;


    // okki同步成功的客户
    protected $successCount;
    protected $failCount;
    protected $failMessages;
    protected $lastAliUpdateTime;
    protected $lastAliCompanyId;

    protected $syncData;

    protected $newCreateCompanyNum;

    // 测试-外部set进来的companyId
    protected $testCompanyId;

    /**
     * AlibabaCustomerSyncExecutor constructor.
     * @param AlibabaCustomerSyncTask $task
     * @throws \ProcessException
     */
    public function __construct( AlibabaCustomerSyncTask $task)
    {
        if( $task->isNew() )
        {
            throw new \ProcessException("task not exist! ");
        }

        $this->clientId = $task->client_id;
        $this->task = $task;
        $this->store = new AlibabaStore($this->clientId, $this->task->store_id);
        $this->processor = new AlibabaCustomerSyncProcessor($this->clientId, $this->task->user_id, $this->store->store_id);
    }

    protected function getAccessToken()
    {
        $alibabaStore = $this->store;
        if( !$alibabaStore->isAuth() )
        {
            throw new \RuntimeException('店铺已被取消授权');
        }

        $token = $alibabaStore->getAccessToken();
        if( empty($token) )
            throw new \RuntimeException(\Yii::t('alibaba', 'Store authorization has expired'));

        return $token;
    }


    /**
     * @throws TaskAlreadyRunException
     */
    protected function begin()
    {
        $this->task->begin();
        //初始化支持配置
        $this->lastAliCompanyId = 0;
        $checkSyncSwitch = true;
        switch ($this->task->task_type )
        {
            case Constant::SYNC_TASK_TYPE_ALL:
                $this->lastAliUpdateTime = '1998-01-01';
                break;
            case Constant::SYNC_TASK_TYPE_EVERY_DAY:
                //从任务创建前一天开始同步
                $this->lastAliUpdateTime = date('Y-m-d',strtotime($this->task->create_time)-86400);
                break;
                // 指定companyId同步
            case Constant::SYNC_TASK_TYPE_SPECIFY:
            case Constant::SYNC_TASK_TYPE_SPECIFY_SELECT:
                $checkSyncSwitch = false;
                break;
                // 按建档时间同步
            case Constant::SYNC_TASK_TYPE_BETWEEN_TIME:
                $this->lastAliUpdateTime = '1998-01-01';
                // 需要检查是否删除不再同步的开关
                $checkSyncSwitch = true;
                break;
        }

        $this->processor->setSessionKey($this->getAccessToken());
        $this->processor->setStore($this->store);
        $this->processor->initDefaultSetting($this->task->pool_id,json_decode($this->task->customer_status, true)?:[], $checkSyncSwitch);
        $this->processor->setUpdateType($this->task->update_type);
        $this->processor->setUpdateOwnerFlag($this->task->update_owner_flag);
        $this->processor->setUpdateStatusFlag($this->task->update_status_flag);
        $this->processor->setMovePublicFlag($this->task->move_public_flag);
        // 当前任务开始时间
        $this->processor->setSyncTime(date('Y-m-d H:i:s'));

        // 设置同步类型
        switch ($this->task->task_type)
        {
            // 同步所有
            case Constant::SYNC_TASK_TYPE_ALL:
                // 按建档时间同步
            case Constant::SYNC_TASK_TYPE_BETWEEN_TIME:
                //事件
                $this->processor->setEvent(Constant::OKKI_SYNC_ONE_CLICK);
                // 归属于一键同步
                $this->processor->setSyncType(Constant::SYNC_TYPE_ONE_CLICK);
                break;
            // 指定companyId同步(重新同步)
            case Constant::SYNC_TASK_TYPE_SPECIFY:
            case Constant::SYNC_TASK_TYPE_SPECIFY_SELECT:
                // 事件
                $this->processor->setEvent(Constant::OKKI_SYNC_RESYNC);
                // 归属于一键同步
                $this->processor->setSyncType(Constant::SYNC_TYPE_RESYNC);
                break;
        }

        $this->aliPublicCount = 0;
        $this->aliPrivateCount = 0;
        $this->totalCount=0;
        $this->successCount =0;
        $this->failCount=0;
        $this->failMessages =[];
        $this->syncData = [
            'alibaba_public_company_ids' => [],//ali公海
            'alibaba_private_company_ids' => [],//ali私海
            'public_company_ids' => [], //okki公海
            'private_company_ids' => [], //okki私海
            'fail_company_ids' => [], //失败
        ];        //每次全量同步,产品改需求
    }

    public function run()
    {
        $loginUser = \User::getLoginUser();
        if( empty($loginUser) || $loginUser->getClientId() != $this->clientId)
        {
            throw new \ProcessException('need set login user ');
        }

        try
        {
            $this->begin();
            $accessToken = $this->getAccessToken();

            $begin = 0;
            $number = 1;
            $retry = 0;
            $lastTime = $this->lastAliUpdateTime;
            $listObj = new AlibabaCustomerList($accessToken);
            $listObj->setPageSize(50);
            do
            {
                $doRetry =false;
                $startTime = microtime(true);
                $dataList =[];
                $dataCount =0;
                if (in_array($this->task->task_type, [Constant::SYNC_TASK_TYPE_SPECIFY, Constant::SYNC_TASK_TYPE_SPECIFY_SELECT])) {
                    $refreshInfo = true;
                    if( !$begin )
                    {
                        // 测试set
                        if($this->testCompanyId)
                        {
                            $dataList[] = [
                                'customer_id' => $this->testCompanyId,
                            ];
                        }else
                        {
                            $alibabaCompanyIds = CustomerSyncHelper::getSyncTaskAlibabaCompanyIds($this->task->task_id);
    
                            $this->totalCount = count($alibabaCompanyIds);
                            
                            foreach ($alibabaCompanyIds as $alibabaCompanyId )
                            {
                                $dataList[] = [
                                    'customer_id' => $alibabaCompanyId,
                                ];
                            }
                        }

                    }
                }else
                {
                    $refreshInfo = false;

                    $listObj->setCustomerIdBegin($begin);
                    $listObj->setLastSyncEndTime($lastTime);

                    //按建档时间范围同步
                    if($this->task->task_type == Constant::SYNC_TASK_TYPE_BETWEEN_TIME)
                    {
                        // 开始坚定时间从00:00:00开始，与ali侧一至
                        $archiveTimeStart = date('Y-m-d', strtotime($this->task->archive_time_start));//建档开始时间
                        //结束建档结束时间(包括当天23:59:59), 与ali侧一至
                        $archiveTimeEnd = date('Y-m-d 23:59:59', strtotime($this->task->archive_time_end));
                        if($archiveTimeStart != '1970-01-01')
                        {
                            $listObj->setGmtCreateStart($archiveTimeStart);
                            $listObj->setGmtCreateEnd($archiveTimeEnd);
                        }
                    }

                    $rsp = $listObj->find();
                    $rsp = is_object($rsp)? AlibabaTopClient::object2array($rsp) :$rsp;
                    if( !isset($rsp['data_list']) )
                    {
                        $error = $listObj->getLastError();
                        $errorSubCode = $error['sub_code']??'';
                        $errorCode = $error['code']??0;
                        if( !$begin)
                        {
                            throw new \RuntimeException('同步错误:'.json_encode($error));
                        }
                        //执行过程中, token失效
                        if( $errorCode == 7  && $errorSubCode == 'invalid-sessionkey')
                        {
                            $errorMsg = "[task {$this->task->task_id}] error begin: {$begin} totalCount:{$this->totalCount}}  get   data list retry: $retry error: ".json_encode($error);
                            \LogUtil::error($errorMsg);
                        }else
                        {
                            // 非首次获取列表失败后, 重试10次, 阿里接口有个别情况或超时或者异常
                            \LogUtil::error("[task {$this->task->task_id}] error begin: {$begin} totalCount:{$this->totalCount}}  get   data list retry: $retry error: {$errorCode}");
                            if (isset(AlibabaTopClient::IGNORE_CODES[$errorSubCode]) && $errorCode != AlibabaTopClient::IGNORE_CODES[$errorSubCode])
                            {
                                $exception = new \AlibabaApiException("alibaba.seller.customer.batch.get req fail {$errorCode}:{$errorSubCode} ");
                                $exception->setApiName('alibaba.seller.customer.batch.get');
                                ErrorReport::phpError(new \CExceptionEvent(null,$exception ), $exception->getTrace(), $loginUser->getUserId());
                            }

                            if( !empty($error) && $number < ($this->totalCount-50) && $retry <10 )
                            {
                                $retry++;
                                $doRetry = true;
                                sleep(rand(2,5));
                                continue;
                            }

                        }
                    }

                    if( isset($rsp['total']) && !$begin )
                    {
                        $this->totalCount = $rsp['total'];
                    }

                    $dataList= $rsp['data_list']['customer_open_dto']??[];
                    //阿里接口偶尔会出问题, 返回空结果 ,但是又没报错, 这里做下重试
                    if( empty($dataList) && $number < ($this->totalCount-100) && $retry <10 )
                    {
                        \LogUtil::error("[task {$this->task->task_id}] error begin: {$begin}number:{$number}, lastSyncTime:{$lastTime} totalCount:{$this->totalCount}}  return empty data_list retry: $retry  rsp:".json_encode($rsp));
                        $retry++;
                        $doRetry = true;
                        sleep(rand(2,5));
                        continue;
                    }

                    // 正常返回结果时，重置重试次数
                    if(!empty($dataList) )
                    {
                        $retry = 0;
                    }
                }

                $dataCount = count($dataList);

                foreach ( $dataList as $data)
                {
                    $begin = $data['customer_id'];
                    $this->process($data, $number,$refreshInfo);
                    $number++;
                }

                $endTime = microtime(true);
                \LogUtil::info("[task {$this->task->task_id}] store: {$this->store->store_id} sync begin:{$begin},number:{$number}, lastSyncTime:{$lastTime} dataCount:{$dataCount} totalCount:{$this->totalCount},  successCount:{$this->successCount}, failCount:{$this->failCount} time: ".($endTime-$startTime));

            }while($doRetry || !empty($dataList));

            $this->finish();

        }catch (TaskAlreadyRunException $e){
            LogUtil::warning("[task {$this->task->task_id}] error ".$e->getMessage().$e->getTraceAsString());
        } catch (\Throwable $e)
        {
            if(\Yii::app()->params['env'] == 'test')
            {
                var_dump($e->getTraceAsString());
            }

            //错误上报
            if(! $e instanceof \RuntimeException)
            {
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), $loginUser->getUserId());
            }

            \LogUtil::error("[task {$this->task->task_id}] error ".$e->getMessage().$e->getTraceAsString());
            $this->failed($e->getMessage());

        }

    }

    protected function process($data, $number, $refreshInfo)
    {
        $alibabaCompanyId = $data['customer_id'];
        $processor = $this->processor;
        $ret = $processor->processWithLock($alibabaCompanyId, $data, $refreshInfo);

        $data = $processor->getLastData();
        // 受保护中间态
        if(isset($data['in_protecting']) && (int)$data['in_protecting'] == 1)
        {
            ++$this->protectingCount;
            return false;
        }else
        {
            // 判断是否是Ali私海|公海
            if(isset($data['owner_id_member_seq']) && !empty($data['owner_id_member_seq']))
            {
                ++$this->aliPrivateCount;
                $this->syncData['alibaba_private_company_ids'][] = $data['customer_id'] ?? 0;
            }else
            {
                ++$this->aliPublicCount;
                $this->syncData['alibaba_public_company_ids'][] = $data['customer_id'] ?? 0;

            }
        }

        if( !$ret )
        {
            $error = $processor->getError();
            if( isset($error['relation_company_id']) &&$error['relation_company_id'])
            {
                //记录同步失败的company_id
                $this->syncData['fail_company_ids'][] = $error['relation_company_id'];
            }

            $this->pushFailMessages($number, $error['alibaba_company_id'], $error['company_name'], $error['owner_email'], $error['level'], $error['msg']);
            return false;
        }

        $result = $processor->getResult();
        $pairInfo = $processor->getPairInfo();
        $this->successCount++;
        $this->lastAliUpdateTime = $result['process_time'];
        $this->lastAliCompanyId = $alibabaCompanyId;
        if( $result['is_public'])
        {
            $this->syncData['public_company_ids'][] = $result['company_id'];
        }else
        {
            $this->syncData['private_company_ids'][] = $result['company_id'];;
        }

        if(isset($result['is_add']) && (bool)$result['is_add'] === true)
        {
            $this->newCreateCompanyNum++;
        }

        \LogUtil::info("[task {$this->task->task_id}] sync company {$result['company_id']}  number: {$number} aliCompany:{$alibabaCompanyId}  OwnerEmail:{$result['alibaba_owner_email']} customerIds:".json_encode($result['save_customer_map'])
            ."isAdd: {$result['is_add']}  isPublic:{$result['is_public']} sync_note_count: {$result['sync_note_count']} pairSource:{$pairInfo['source']} pairItem: ".json_encode($pairInfo['item']));

        return $ret;

    }

    protected function failed($error)
    {
        $this->task->failed($error);
        $this->saveTaskData();
        $customerSyncSetting = $this->store->getCustomerSyncObj();
        $customerSyncSetting->sync_status = AlibabaCustomerSyncSetting::SYNC_STATUS_DONE;
        $customerSyncSetting->update(['sync_status']);
    }

    protected function finish()
    {
        //报错错误文件信息
        $filename = 'alibaba_customer_sync_' . $this->clientId .'_'.$this->store->store_id. '_' .$this->task->task_id.'_'.date('YmdHis').rand(100,10000) . '.csv';
        $csvPath = '/tmp/' . $filename;
        $fp = fopen($csvPath,'w');
        fputs($fp, (chr(0xEF) . chr(0xBB) . chr(0xBF)));//加上bom头告诉excel软件用utf8格式打开
        fputcsv($fp, ['序号','公司名称','业务员', '失败原因']);

        $no = 1;
        $failHeadRows = [];
//        $operationNameMap = [
//            self::OP_TYPE_ERROR => '错误',
//            self::OP_TYPE_IGNORE => '忽略',
//            self::OP_TYPE_WARN => '警告',
//            self::OP_TYPE_REMARK => '说明',
//        ];

        foreach ($this->failMessages as $item)
        {
            if( $item['operation'] != self::OP_TYPE_ERROR)
                continue;

            $csvRow = [$no, $item['company_name'], $item['owner_email'], $item['msg']];
            fputcsv($fp, $csvRow );
            if($no <= 20)
            {
                $failHeadRows[] = $csvRow;
            }

            $no++;
        }

        fclose($fp);

        // 上传结果
        $upload = \UploadService::uploadRealFile($csvPath, $filename, \UploadService::getFileKey($csvPath));
        unlink($csvPath);

        //去重处理
        $this->syncData['public_company_ids'] = array_values(array_unique($this->syncData['public_company_ids']));
        $this->syncData['private_company_ids'] = array_values(array_unique($this->syncData['private_company_ids']));
        $this->syncData['fail_company_ids'] = array_values(array_unique($this->syncData['fail_company_ids']));

        $this->task->finish(
            (int)$upload->getFileId(),
            ($this->totalCount - $this->protectingCount),
            $this->aliPublicCount,
            $this->aliPrivateCount,
            $this->successCount,
            count($this->syncData['public_company_ids']),
            count($this->syncData['private_company_ids']),
            $this->failCount, $failHeadRows
        );
        $this->task->updateLastInfo($this->lastAliCompanyId, $this->lastAliUpdateTime);
        $customerSyncSetting = $this->store->getCustomerSyncObj();
        $updateFields = ['sync_status'];
        //只取系统定时同步的时间
        if( $this->task->task_type == Constant::SYNC_TASK_TYPE_EVERY_DAY )
        {
            $customerSyncSetting->last_sync_time = $this->task->finish_time;
            $updateFields[] = ['last_sync_time'];
        }
        $customerSyncSetting->sync_status = AlibabaCustomerSyncSetting::SYNC_STATUS_DONE;
        $customerSyncSetting->update($updateFields);
        $this->saveTaskData();
        $notificationId =$this->sendNotification();


        // 上报神策
        if($this->newCreateCompanyNum > 0)
        {
            // web端|ali同步
            $sensorsEvent = new EventCreateCompany($this->task->client_id, $this->task->user_id);
            $sensorsEvent->setParams([
                'platform_type' => EventCreateCompany::PLATFORM_WEB,
                'company_number' => $this->newCreateCompanyNum,
                'action_type' => EventCreateCompany::ACTION_TYPE_ALI,
            ]);
            $sensorsEvent->report();
        }

        \LogUtil::info("[task {$this->task->task_id}] sync finish  lastAliCompanyId:{$this->lastAliCompanyId}, lastAliUpdateTime:{$this->lastAliUpdateTime}, totalCount:{$this->totalCount},  successCount:{$this->successCount}, failCount:{$this->failCount}  notification: {$notificationId}");

    }


    protected function saveTaskData()
    {
        if( empty($this->syncData) )
            return;

        CustomerSyncHelper::saveSyncTaskData($this->clientId, $this->task->task_id, $this->syncData);
    }

    public function sendNotification()
    {
        $notificationId=0;
        try
        {
            //发送消息通知
            $notification = new Notification($this->clientId, \common\library\notification\Constant::NOTIFICATION_TYPE_CUSTOMER_ALIBABA_SYNC);
            $notification->user_id = $this->task->user_id;
            $notification->create_user_id = $this->task->user_id;
            $notification->refer_id = $this->task->task_id;
            $fileName ='';
            $resultUrl ='';
            if( $this->task->result_file_id )
            {
                $upload = new \AliyunUpload();
                $upload->loadByFileId($this->task->result_file_id, $this->task->user_id);
                $fileName = $upload->getFileName();
                $resultUrl = $upload->getFileUrl();
            }

            $this->store->getFormatter()->setSpecifyFields(['store_id','store_name']);
            $this->store->getFormatter()->setShowSellerAccountInfo(true);
            $storeData = $this->store->getAttributes();

            $notification->setSourceData(array_merge([
                'task_id' => $this->task->task_id,
                'finish_time' => $this->task->finish_time,
                'create_time' => $this->task->create_time,
                'sync_time' => $this->task->finish_time,
                'total_count' => $this->task->total_count,
                'ali_public_count' => $this->task->ali_public_count,
                'ali_private_count' => $this->task->ali_private_count,
                'success_count' => $this->task->success_count,
                'public_count' => $this->task->public_count,
                'private_count' => $this->task->private_count,
                'fail_count' => $this->task->fail_count,
                'file_name' => $fileName,
                'result_url' => $resultUrl
            ], $storeData));


            PushHelper::pushNotification($this->clientId, $this->task->user_id, $notification);

            $notificationId =  $notification->notification_id;
        }catch (\Throwable $e)
        {
            \LogUtil::error("[task {$this->task->task_id}] send Message error :".$e->getMessage().$e->getTraceAsString());
        }

        return $notificationId;
    }

    protected function pushFailMessages($number, $alibabaCompanyId, $companyName, $ownerEmail, $operation, $msg)
    {
        if( $operation == self::OP_TYPE_ERROR)
        {
            $this->failCount ++;
        }

        $this->failMessages[] = [
            'number' => $number,
            'alibaba_company_id' => $alibabaCompanyId,
            'company_name' => $companyName,
            'owner_email' => $ownerEmail,
            'operation' => $operation,
            'msg' => $msg
        ];

        \LogUtil::error("[task {$this->task->task_id}] sync error msg  number:{$number} {$alibabaCompanyId}  {$companyName} {$operation} {$msg}");

//        var_dump([
//            'alibaba_company_id' => $alibabaCompanyId,
//            'company_name' => $companyName,
//            'operation' => $operation,
//            'msg' => $msg
//        ]);
    }

    public function setTestAliCompanyId($testCompanyId)
    {
        $this->testCompanyId = $testCompanyId;
    }


}
