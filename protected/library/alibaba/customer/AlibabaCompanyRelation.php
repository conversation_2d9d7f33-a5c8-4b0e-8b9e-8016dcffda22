<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/11/7
 * Time: 10:31 AM
 */

namespace common\library\alibaba\customer;


use common\components\BaseObject;
use common\library\alibaba\Constant;
use common\library\customer\Helper;
use common\library\sns\Constants;
use common\library\sns\customer\CustomerContactService;
use common\models\client\AlibabaCompanyRelation as AlibabaCompanyRelationModel;

/**
 * Class AlibabaCompanyRelation
 * @package common\library\alibaba\customer
 *
 * @property string $relation_id
 * @property string $alibaba_company_id
 * @property string $company_id
 * @property string $client_id
 * @property string $store_id
 * @property string $create_time
 * @property string $sync_time
 * @property string $update_time
 * @property string $buyer_account_id
 * @property string $buyer_account_encrypt
 * @property string $owner_email
 * @property string $owner_account_id
 * @property string $is_add
 * @property string $sync_count
 * @property string $alibaba_company_name
 * @property string $sync_note_flag
 * @property string $basic_info_allowable
 * @property string $contact_info_allowable
 * @property string $sync_status
 * @property string $fail_reason
 * @property string $fail_content
 * @property int $sync_switch_flag
 * @property string $detail_url
 * @property string $sync_max_note_id
 * @property integer $is_protecting
 * @property string $last_owner_account_id
 */
class AlibabaCompanyRelation extends BaseObject
{
    protected $clientId;

    /**
     * AlibabaCompanyRelation constructor.
     * @param $clientId
     * @param int $id
     */
    public function __construct($clientId, $id=0)
    {
        $this->clientId = $clientId;
        if( $id )
            $this->loadById($id);
    }


    /**
     * @return string | AlibabaCompanyRelationModel
     */
    public function getModelClass()
    {
        return AlibabaCompanyRelationModel::class;
    }

    public function  loadById($id)
    {
        $model = $this->getModelClass()::model()->find('relation_id=:relation_id',[':relation_id' => $id]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function  loadByAlibabaCompanyId($companyId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and alibaba_company_id=:alibaba_company_id',[':client_id' => $this->clientId, ':alibaba_company_id' => $companyId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function  loadByCompanyId($companyId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and company_id=:company_id',[':client_id' => $this->clientId, ':company_id' => $companyId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }


    public function  loadByBuyerAccountId($accountId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and buyer_account_id=:buyer_account_id',[':client_id' => $this->clientId, ':buyer_account_id' => $accountId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    /**
     * 根据店铺ID和买家ID获取关联的company_id
     * @param $storeId
     * @param $buyerAccountId
     * @return int
     */
    public function getCompanyIdByStoreBuyerAccountId($storeId, $buyerAccountId): int
    {
        $model = $this->getModelClass()::model()
            ->find('client_id=:client_id and buyer_account_id=:buyer_account_id and store_id=:store_id and company_id > 0',
                [':client_id' => $this->clientId, ':buyer_account_id' => $buyerAccountId, ':store_id' => $storeId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this->isNew() ? 0 : (int) $this->company_id;
    }


    public function  loadByOwnerAccountId($accountId)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and owner_account_id=:owner_account_id',[':client_id' => $this->clientId, ':owner_account_id' => $accountId]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }



    public function  loadByOwnerEmail($email)
    {
        $model = $this->getModelClass()::model()->find('client_id=:client_id and owner_email=:owner_email',[':client_id' => $this->clientId, ':owner_email' => $email]);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    protected function beforeSave()
    {
        if(isset($this->_attributes['fail_content']) && is_array($this->_attributes['fail_content']))
        {
            $this->_attributes['fail_content'] = empty($this->_attributes['fail_content'])?'{}':json_encode($this->_attributes['fail_content']);
        }

        //新数据,默认是需要同步的
        if( $this->isNew() )
        {
            $this->sync_switch_flag = Constant::TYPE_ENABLE;
        }

        return parent::beforeSave();
    }

	protected function afterSave() {

		if ($this->isNew() || (($this->_attributes['company_id'] ?? 0) != ($this->_oldAttributes['company_id'] ?? 0))) {

            // 更新阿里巴巴客户画像数据
            Helper::refreshCompanyPortrait($this->clientId,$this->company_id,$this->store_id, $this->alibaba_company_id);

            Helper::refreshCompanyTmTime($this->clientId, $this->company_id, $this->store_id);

            // 更新tbl_user_customer_contact
            $service = (new CustomerContactService($this->clientId));
            $service->updateCompanyRelation(
                Constants::SNS_CLIENT_TM,
                $this->store_id,
                $this->buyer_account_id,
                $this->company_id
            );
		}

		parent::afterSave(); // TODO: Change the autogenerated stub
	}


}
