<?php
namespace common\library\alibaba\customer;
/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 2020/10/28
 * Time: 11:35
 */
use common\components\BaseObject;
use common\library\alibaba\Constant;
use common\library\alibaba\exception\TaskAlreadyRunException;
use common\library\alibaba\services\queue\QueueHelper;
use common\library\alibaba\store\AlibabaStore;
use common\models\client\AlibabaCustomerSyncTask as AlibabaCustomerSyncTaskModel;

/**
 * This is the model class for table "tbl_alibaba_customer_sync_task".
 *
 * The followings are the available columns in table 'tbl_alibaba_customer_sync_task':
 * @property string $task_id
 * @property string $user_id
 * @property string $client_id
 * @property integer $store_id
 * @property string $sync_time
 * @property integer $status
 * @property integer $task_type
 * @property string $result_file_id
 * @property string $finish_time
 * @property string $last_ali_update_time
 * @property string $last_ali_company_id
 * @property string $total_count
 * @property integer $ali_public_count
 * @property integer $ali_private_count
 * @property string $success_count
 * @property integer $public_count
 * @property integer $private_count
 * @property string $fail_count
 * @property string $fail_reason
 * @property string $result_line
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @property string $customer_status
 * @property string $pool_id
 * @property string $archive_time_start
 * @property string $archive_time_end
 * @property integer $update_type
 * @property integer $update_owner_flag
 * @property integer $update_status_flag
 * @property integer $move_public_flag
 */

class AlibabaCustomerSyncTask extends BaseObject{

    protected $clientId;
    protected $userId;
    protected $taskId;

    protected $data;

    public function __construct($clientId,$taskId = 0)
    {
        $this->clientId = $clientId;
        if($taskId){
            $this->taskId = $taskId;
            $this->loadByTaskId();
        }
    }

    /**
     * @return string| AlibabaCustomerSyncTaskModel
     */
    public function getModelClass()
    {
        return AlibabaCustomerSyncTaskModel::class;
    }

    public static function getModelTableName()
    {
        return AlibabaCustomerSyncTaskModel::model()->tableName();
    }

    public function loadByTaskId()
    {
        $model = $this->getModelClass()::findByTaskId($this->clientId,$this->taskId);
        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    public function loadByUntreated($storeId, $taskType=0)
    {
        $sql = 'client_id=:client_id and store_id=:store_id  and status in ('
            .implode(',',[Constant::SYNC_FLAG_START, Constant::SYNC_FLAG_RUNNING]).')';
        $params = [':client_id' => $this->clientId,':store_id' => $storeId];

        if( $taskType )
        {
            $sql .= ' and task_type=:task_type';
            $params[':task_type'] = $taskType;
        }

        $model = $this->getModelClass()::model()->find($sql, $params);

        if ($model) {
            $this->setModel($model);
        }
        return $this;
    }

    /**
     * @param mixed $data
     */
    public function setData(array $data)
    {
        $this->data = $data;
    }


    /**
     * @return void
     * @throws TaskAlreadyRunException
     */
    public function begin(): void
    {
        if ($this->isNew())
            throw new \RuntimeException(\Yii::t('common', 'The task does not exist and cannot start'));

        if ($this->status != Constant::SYNC_FLAG_START){
            throw new TaskAlreadyRunException("任务已经运行过了 task={$this->task_id}（状态：{$this->status}）");
        }

        $this->status = Constant::SYNC_FLAG_RUNNING;
        $this->sync_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');
        $this->update(['status','sync_time','update_time']);

    }


    public function failed($error)
    {
        if ($this->isNew())
            throw new \RuntimeException(\Yii::t('common', 'The task does not exist and cannot start'));

        if ($this->status == Constant::SYNC_FLAG_FINISH)
            throw new \RuntimeException(\Yii::t('task', 'The task has been run (status: {status})', ['{status}' => $this->status]));

        $this->status = Constant::SYNC_FLAG_ERROR;
        $this->fail_reason = $error;
        $this->update(['status', 'fail_reason']);
    }

    public function finish(
        int $fileId,
        int $totalCount,
        int $aliPublicCount,
        int $aliPrivateCount,
        int $successCount,
        int $publicCount,
        int $privateCount,
        int $failCount,
        array $failMessages)
    {
        if ($this->isNew())
            throw new \RuntimeException(\Yii::t('common', 'The task does not exist and cannot start'));

        if ($this->status != Constant::SYNC_FLAG_RUNNING)
            throw new \RuntimeException(\Yii::t('task', 'The task is not running (status: {status})', ['{status}' => $this->status]));

        $this->result_file_id = $fileId;
        $this->total_count = $totalCount;
        $this->ali_public_count = $aliPublicCount;
        $this->ali_private_count = $aliPrivateCount;
        $this->success_count = $successCount;
        $this->private_count = $privateCount;
        $this->public_count = $publicCount;
        $this->fail_count = $failCount;
        $this->result_line = json_encode($failMessages);
        $this->finish_time = date('Y-m-d H:i:s');
        $this->update_time = date('Y-m-d H:i:s');
        $this->status = Constant::SYNC_FLAG_FINISH;
        $this->save();

    }

    public function updateLastInfo($lastCompanyId, $latUpdateTime)
    {
        $this->last_ali_update_time = $latUpdateTime;
        $this->last_ali_company_id = $lastCompanyId;
        $this->update(['last_ali_update_time','last_ali_company_id']);
    }

    protected function beforeSave()
    {
        if( $this->isNew() )
        {
            if( in_array($this->task_type, [Constant::SYNC_TASK_TYPE_SPECIFY, Constant::SYNC_TASK_TYPE_SPECIFY_SELECT])
                && empty($this->data['alibaba_company_ids']))
            {
                throw new \RuntimeException('需要设置指定同步的客户');
            }
        }

        return parent::beforeSave();
    }


    protected function afterSave()
    {
        if( $this->isNew() )
        {

            if( !empty($this->data) && in_array($this->task_type, [Constant::SYNC_TASK_TYPE_SPECIFY, Constant::SYNC_TASK_TYPE_SPECIFY_SELECT]))
            {
                CustomerSyncHelper::saveSyncTaskData($this->clientId, $this->task_id, $this->data);
                CustomerSyncHelper::setAlibabaCompanySyncStatus($this->clientId, $this->data['alibaba_company_ids']);
            }
        }

        parent::afterSave();
    }


    public function run($retry=false)
    {
        if( $retry )
        {
            $this->status = Constant::SYNC_FLAG_START;
            $this->update(['status']);
        }

        $params = [
            'client_id' => $this->client_id,
            'user_id' => $this->user_id,
            'store_id' => $this->store_id,
            'task_id' => $this->task_id,
            'retry' => $retry?1:0
        ];

        $log = '/tmp/alibaba_syncCustomer.log';
        \common\library\CommandRunner::run(
            'alibaba',
            'syncCustomer',
            $params,
            $log
        );
    }

    public function pushQueue()
    {
        if( $this->isNew() )
            return;

        QueueHelper::pushCustomerSyncHandle($this->client_id, $this->user_id, $this->store_id, $this->task_id);
    }

}
