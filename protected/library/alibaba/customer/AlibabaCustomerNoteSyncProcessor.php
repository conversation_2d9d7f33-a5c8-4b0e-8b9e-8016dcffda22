<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/11
 * Time: 6:05 PM
 */

namespace common\library\alibaba\customer;


use AlibabaStoreAccount;
use CDbException;
use CException;
use common\library\alibaba\services\AlibabaCustomerNote;
use common\library\trail\events\RemarkEvents;
use common\library\trail\TrailConstants;
use common\library\util\SqlBuilder;

class AlibabaCustomerNoteSyncProcessor
{
    protected $clientId;
    protected $storeId;

    protected $sessionKey;
    protected $_companyId;
    protected $_customerIds;
    protected $_alibabaCompanyId;
    protected $_forceFlag;

    protected $_setting;
    protected $typeMap;

    protected $syncCount = 0;

    /**
     * AlibabaCustomerNoteSyncProcessor constructor.
     * @param $clientId
     * @param $storeId
     */
    public function __construct($clientId, $storeId)
    {
        $this->clientId = $clientId;
        $this->storeId = $storeId;

        $this->init();
    }

    /**
     * @param mixed $sessionKey
     */
    public function setSessionKey($sessionKey)
    {
        $this->sessionKey = $sessionKey;
    }

    /**
     * @param mixed $setting
     */
    public function setSetting($setting)
    {
        $this->_setting = $setting;
    }

    /**
     * @return mixed
     */
    public function getSetting()
    {
        return $this->_setting;
    }

    /**
     * @return int
     */
    public function getSyncCount(): int
    {
        return $this->syncCount;
    }

    protected function init()
    {
        $this->typeMap = [
            //洽谈中
            'INQUIRY_WITH_NO_REPLY' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'FIRST_QUOTED_PRICE_WITH_NO_REPLY' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'MULTIPLE_FOLLOW_UP_WITH_NO_REPLY' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'INQUIRY_WITH_NO_QUOTED_PRICE' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'PRODUCT_CATEGORY_HAS_SEND' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'PRICE_NEGOTIATION' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'PRODUCT_NOT_RECOMMENDED_YET' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'DRAWINGS_NOT_CONFIRMED' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'SAMPLES_NOT_SENT' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'SEND_SAMPLE_WITH_NO_PLACE_ORDER' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'FACTORY_NOT_INSPECTED' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'PENDING_ORDER' => CustomerSyncHelper::NOTE_TYPE_TASK,
            'NEGOTIATING_NO_CODE' => CustomerSyncHelper::NOTE_TYPE_TASK,
            //未成交
            'MOQ_NOT_REACHED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'DISCONTINUED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'NOT_MEET_EXPECTATIONS' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'HIGH_QUOTATION' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'PAYMENT_METHOD_NOT_NEGOTIATED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'DELIVERY_DATE_NOT_NEGOTIATED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'INSPECTION_FAILED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'CUSTOMER_PRODUCTS_DISCONTINUED' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'EXPRESS_EXPENSIVE' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'EXPRESS_NOT_AVAILABLE' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'NO_CUSTOMS_CLEARANCE' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'HIGH_TARIFFS' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'CUSTOMER_LOST_CONTACT' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'UNSOLD_NO_CODE' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            'MOVE_TO_RECYCLE_BIN' => CustomerSyncHelper::NOTE_TYPE_UNSETTLED,
            //跟单中
            'PENDING_PAYMENT' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'RECEIVE_ADVANCE_CHARGE' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'TO_CHECK_GOODS' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'INSPECTION_REPORT_NOT_SENT' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'TO_RECEIVE_FINAL_PAYMENT' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'UNBOOKED' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'NOT_SHIPPED' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'RECEIPT_NOT_CONFIRMED' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'TO_BE_REPURCHASED' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'FOLLOWING_UP_NO_CODE' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'SYSTEM_OPPORTUNITY_FORCE_OPEN_TO_PUBLIC_SEA' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'CUSTOMER_LOST_WARN' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'HIGH_DIVE_PURCHASE' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'SYSTEM_OPPORTUNITY_PICKUP_TO_PUBLIC_SEA' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'MULTI_SHOP_CUSTOMER_JUDGE_REPEAT' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            'MULTI_SHOP_CUSTOMER_REPEAT_REJUDGE' => CustomerSyncHelper::NOTE_TYPE_DOCUMENTARY,
            //售后
            'PENDING_SALES_AND_COMPLAINTS' => CustomerSyncHelper::NOTE_TYPE_AFTER_SALES,
            'AFTER_SALE_PROCESSING' => CustomerSyncHelper::NOTE_TYPE_AFTER_SALES,
            'SOLVE_AFTER_SALE_PROBLEM' => CustomerSyncHelper::NOTE_TYPE_AFTER_SALES,
            'SOLVE_AFER_SALE_PROBLEM' => CustomerSyncHelper::NOTE_TYPE_AFTER_SALES, //阿里有拼写错误, 这里有坑
            'AFTER_SALE_NO_CODE' => CustomerSyncHelper::NOTE_TYPE_AFTER_SALES,
        ];
    }


    public function getLabelInfo($noteCode, $nodeLabel)
    {
        if (empty($noteCode)) {
            $typeName = str_replace('未标记', '', $nodeLabel);
            $nodeLabel = '';
            $type = array_flip(CustomerSyncHelper::NOTE_NAME_MAP)[$typeName] ?? 0;
        } else {
            $noteCode = strtoupper($noteCode);
            $type = $this->typeMap[$noteCode] ?? 0;
            $typeName = CustomerSyncHelper::NOTE_NAME_MAP[$type] ?? '';
        }

        $prefix = '';
        if ($typeName) {
            $prefix .= ($nodeLabel ? "{$typeName}-" : $typeName);
        }

        $prefix .= "{$nodeLabel}";

        if (!empty($prefix)) {
            $prefix = '[' . $prefix . ']';
        }

        return ['type' => $type, 'name' => $typeName, 'code' => $noteCode, 'label' => $nodeLabel, 'prefix' => $prefix];
    }

    protected function getMaxSynchronizedNoteId()
    {
        $listObj = new AlibabaCustomerNoteRelationList($this->clientId);
        $listObj->setStoreId($this->storeId);
        $listObj->setAlibabaCompanyId($this->_alibabaCompanyId);
        $listObj->setCompanyId($this->_companyId);
        $noteId = $listObj->maxNoteId();

        return $noteId;
    }

    protected function getNoteRelationMap(array $alibabaNoteIds)
    {
        $listObj = new AlibabaCustomerNoteRelationList($this->clientId);
        $listObj->setStoreId($this->storeId);
        $listObj->setAlibabaCompanyId($this->_alibabaCompanyId);
        $listObj->setAlibabaNoteId($alibabaNoteIds);
        $listObj->setFields(['trail_id,alibaba_note_id, alibaba_company_id, company_id']);

        return  array_column($listObj->find(),null,'alibaba_note_id' );
    }


    public function process($companyId, $alibabaCompanyId, array $customerIds = [], $forceFlag = false, $maxSYncNoteId=0)
    {
        $setting = $this->getSetting();
        if (empty($setting)) {
            throw new \ProcessException('需要配置 setting');
        }

        $this->_alibabaCompanyId = $alibabaCompanyId;
        $this->_companyId = $companyId;
        $this->_customerIds = $customerIds;
        $this->_forceFlag = $forceFlag;
        $this->syncCount = 0;

        $maxSynchronizedNoteId = $maxSYncNoteId?: $this->getMaxSynchronizedNoteId();
        $pageNum = 1;
        $currentMaxId = 0;
        $listObj = new AlibabaCustomerNote($this->sessionKey, $alibabaCompanyId);
        $runSyncFlag = true;
        do {
            $rsp = $listObj->getNoteList($pageNum, 100);
            $noteList = $rsp['data_list']['note_co'] ?? [];
            if( !empty($noteList) && $pageNum == 1) {
                $currentMaxId = $noteList[0]['note_id'] ?? 0;
                if( !$forceFlag  && $maxSynchronizedNoteId >= $currentMaxId) {
                    $runSyncFlag = false;
                    break;
                }
            }

            $syncRes = [];
            //需要检查关联关系
            $existNoteMap = $this->getNoteRelationMap(array_column($noteList, 'note_id'));

            foreach ($noteList as $item) {
                //增量同步到最新
                if (!$forceFlag && $item['note_id'] <= $maxSynchronizedNoteId) {
                    $runSyncFlag = false;
                    break;
                }

                //已经同步过的, 就不再同步
                if (isset($existNoteMap[$item['note_id']]) && $companyId == $existNoteMap[$item['note_id']]['company_id']) {
                    continue;
                }

                try {
                    list($trailId, $labelInfo) = $this->saveRemarkEvent($item);
                    $item['trail_id'] = $trailId;
                    $item['note_type'] = $labelInfo['type'];
                    $syncRes[] = $item;
                    $this->syncCount++;
                } catch (\Exception $e) {
                    if (!empty($syncRes)) {
                        $this->saveRelation($syncRes);
                    }

                    throw  $e;
                }
            }

            if (!empty($syncRes)) {
                $this->saveRelation($syncRes);
            }

            if (!$runSyncFlag) {
                break;
            }
            $pageNum++;
        } while (!empty($noteList));

        return $currentMaxId;
    }


    public function processSimple($companyId, $alibabaCompanyId, $noteId)
    {
        $setting = $this->getSetting();
        if (empty($setting)) {
            throw new \ProcessException('需要配置 setting');
        }

        $this->_alibabaCompanyId = $alibabaCompanyId;
        $this->_companyId = $companyId;
        $this->_customerIds = [];
        $this->_forceFlag = false;
        $existNoteMap = $this->getNoteRelationMap([$noteId]);
        if( isset($existNoteMap[$noteId ]) && $companyId == $existNoteMap[$noteId]['company_id'])
            return false;

        $alibabaCustomerNote = new AlibabaCustomerNote($this->sessionKey, $alibabaCompanyId);
        $data = $alibabaCustomerNote->getInfo($noteId);
        if( !isset($data['note_id']) ) {
            throw new \RuntimeException("客户小记不存在:".$noteId);
        }

        list($trailId, $labelInfo) = $this->saveRemarkEvent($data);
        $data['trail_id'] = $trailId;
        $data['note_type'] = $labelInfo['type'];
        $this->saveRelation([$data]);

        return $data;
    }

    /***
     * 保存客户小记到动态
     *
     * @param array $item
     * @return array
     * @throws CDbException
     * @throws CException
     */
    public function saveRemarkEvent(array $item)
    {
        $setting = $this->getSetting();
        $sellerAccountId = $item['sale_member_seq'] ?? 0;
        $userId = $sellerAccountId ? ($setting['owner_relation_map'][$sellerAccountId] ?? $setting['default_owner_user_id']) : $setting['default_owner_user_id'];
        $labelInfo = $this->getLabelInfo($item['note_code'] ?? '', $item['note_label'] ?? '');

        $content = $labelInfo['prefix'];
        if (!empty($item['content'])) {
            if( empty($content) ) {
                $content = $item['content'];
            }else {
                $content .= " \n {$item['content']}";
            }
        }

        $data = [
            'content' => $content,
            'note_code' => $item['note_code'] ?? '',
            'note_type' => $labelInfo['type'],
            'node_id' => $item['note_id'] ?? 0
        ];

        $aliStoreAccountModel = AlibabaStoreAccount::model();
        $aliAccount = $aliStoreAccountModel->find('client_id = :cid and seller_account_id = :sid', [
            ':cid' => $this->clientId,
            ':sid' => $sellerAccountId,
        ]);
        if ($aliAccount) {
            $data['source'] = [
                'type' => TrailConstants::SOURCE_TYPE_TM,
                'seller_account_id' => $sellerAccountId,
                'seller_email' => $aliAccount->seller_email,
                'seller_name' => "{$aliAccount->first_name} {$aliAccount->last_name}",
            ];
        }

        $event = new RemarkEvents();
        $event->setType(TrailConstants::TYPE_REMARK_ADD);
        $event->setCompanyId($this->_companyId);
        $event->setCustomerId($this->_customerIds);
        $event->setClientId($this->clientId);
        $event->setCreateUser($userId);
        $event->setCreateTime($item['note_time']);
        $event->setUserId($userId);
        $event->setData($data);
        $event->run();

        return[$event->getTrailId(),$labelInfo];
    }


    public function saveRelation(array $list)
    {
        $db = \AlibabaCustomerNoteRelationModel::getDbByClientId($this->clientId);
        $table = \AlibabaCustomerNoteRelationModel::model()->tableName();
        $dateTime = date('Y-m-d H:i:s');
        $sqlArr =[];
        $count = count($list);
        $maxId = \ProjectActiveRecord::produceAutoIncrementId($count);
        $id = $maxId - $count;

        foreach ($list as $item) {
            $id++;
            $sqlArr[] = [
                'relation_id' => $id,
                'client_id' => $this->clientId,
                'store_id' => $this->storeId,
                'company_id' => $this->_companyId,
                'alibaba_company_id' => $this->_alibabaCompanyId,
                'trail_id' => $item['trail_id'],
                'alibaba_note_id' => $item['note_id'],
                'node_type' => intval($item['note_type']??0),
                'note_code' => $item['note_code']??'',
                'note_label' => $item['note_label']??'',
                'content' => $item['content']??'',
                'note_time' => $item['note_time'],
                'seller_account_id' => intval($item['sale_member_seq']??0),
                'seller_name' => $item['sale_name']??'',
                'create_time' => $dateTime,
                'update_time' => $dateTime,
                'delete_flag' => $item['delete_flag']??0,
            ];
        }

        list($sql, $params) = SqlBuilder::buildMultiInsert($table,$sqlArr);
        $sql .=' on duplicate key update update_time=values(update_time),trail_id=values(trail_id),company_id=values(company_id)';
        $db->createCommand($sql)->execute($params);

    }

    public function saveDeleteFlag($alibabaCompanyId, array $noteIds,int $deleteFlag=0)
    {
        if(empty($noteIds ))
            throw new \ProcessException('noteIds 不能为空');

        $db = \AlibabaCustomerNoteRelationModel::getDbByClientId($this->clientId);
        $table = \AlibabaCustomerNoteRelationModel::model()->tableName();
        $sql = "update {$table} set delete_flag=:delete_flag where client_id=:client_id and store_id=:store_id and alibaba_company_id=:alibaba_company_id";
        $params = [ ':delete_flag'=>$deleteFlag, ':client_id' => $this->clientId, ':store_id'=> $this->storeId, ':alibaba_company_id'=> $alibabaCompanyId];
        SqlBuilder::buildIntWhere('', 'alibaba_note_id', $noteIds, $sql, $params);
        $db->createCommand($sql)->execute($params);

    }
}
