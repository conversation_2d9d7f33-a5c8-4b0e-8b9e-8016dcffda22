<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/13
 * Time: 11:20 PM
 */

namespace common\library\alibaba\customer;


use common\components\BaseObject;
use common\library\alibaba\Constant;
use common\library\alibaba\services\AlibabaCompany;
use common\library\alibaba\store\AlibabaStore;
use common\library\alibaba\store\AlibabaStoreAccountSyncHelper;
use common\library\alibaba\store\AlibabaStoreList;
use common\library\alibaba\store\AlibabaStoreMemberSettingService;
use common\library\alibaba\store\AlibabaStoreMembersList;
use common\library\alibaba\store\AlibabaStoreService;
use common\library\customer\Helper;
use xiaoman\orm\database\PgsqlUtil;

class AlibabaCompanyRelationFormatter extends \ListItemFormatter
{
    protected $clientId;

    protected $showOwnerAccountInfo = false;
	protected $showLastOwnerAccountInfo = false;
    protected $showLatelyAlibabaInfo = false;
    protected $showCompanyInfo = false;
    protected $showStoreInfo = false;
    protected $showStoreException = false;
    protected $showCompanyUserInfo = false;

    protected $specifyFields;

    protected $_sessionKeyMap=[];

    /**
     * AlibabaCompanyRelationFormatter constructor.
     * @param $clientId
     */
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @param mixed $specifyFields
     */
    public function setSpecifyFields($specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }

    /**
     * 显示最新公司名称, 性能极差,慎用
     * @param bool $showLatelyAlibabaInfo
     */
    public function setShowLatelyAlibabaInfo(bool $showLatelyAlibabaInfo)
    {
        $this->showLatelyAlibabaInfo = $showLatelyAlibabaInfo;
    }

    /**
     * @param bool $showOwnerAccountInfo
     */
    public function setShowOwnerAccountInfo(bool $showOwnerAccountInfo)
    {
        $this->showOwnerAccountInfo = $showOwnerAccountInfo;
    }

	public function setShowLastOwnerAccountInfo(bool $showLastOwnerAccountInfo)
	{
		$this->showLastOwnerAccountInfo = $showLastOwnerAccountInfo;
	}

    /**
     * @param bool $showCompanyInfo
     * @param bool $showCompanyUserInfo
     */
    public function setShowCompanyInfo(bool $showCompanyInfo, bool $showCompanyUserInfo = false)
    {
        $this->showCompanyInfo = $showCompanyInfo;
        $this->showCompanyUserInfo = $showCompanyUserInfo;
    }

    /**
     * @param bool $showStoreInfo
     */
    public function setShowStoreInfo(bool $showStoreInfo): void
    {
        $this->showStoreInfo = $showStoreInfo;
    }

    /**
     * @param bool $showStoreException
     */
    public function setShowStoreException(bool $showStoreException): void
    {
        $this->showStoreException = $showStoreException;
    }


    public function listInfoSetting()
    {
        $this->setSpecifyFields([
            'alibaba_company_id',
            'company_id',
            'store_id',
            'sync_time',
            'sync_status',
            'owner_account_id',
            'alibaba_company_name',
            'fail_reason',
            'fail_content',
            'detail_url',
        ]);

        $this->setShowOwnerAccountInfo(true);
        $this->setShowCompanyInfo(true);
    }

    public function companyAssocStoreList()
    {
        $this->setSpecifyFields([
            'store_id', 'alibaba_company_name', 'owner_account_id', 'detail_url', 'company_id', 'alibaba_company_id',
        ]);

        $this->setShowOwnerAccountInfo(true);
        $this->setShowStoreInfo(true);
        $this->setShowStoreException(true);
        $this->setShowCompanyInfo(true, true);
    }

    public function aliCompaniesOfLeadSetting()
    {
        $this->setSpecifyFields([
            'alibaba_company_id', 'company_id', 'owner_account_id'
        ]);
        $this->setShowCompanyInfo(true, true);
    }

    /**
     * 构建指定字段
     * @param $data
     * @return array
     */
    protected function buildFieldsInfo($data)
    {
        $specifyFields = $this->specifyFields;
        if ($this->specifyFields === null) {
            $specifyFields = array_keys($data);
        }
        $result = \ArrayUtil::columns($specifyFields, $data, '');
        return $result;
    }


    public function buildMapData()
    {
        $list = $this->batchFlag?$this->listData:[$this->data];

        $ownerAccountMap =[];
		$ownerAccountIds = [];
        if( $this->showOwnerAccountInfo )
        {
            $ownerAccountIds = array_filter(array_column($list,'owner_account_id'));
            //$ownerAccountMap = AlibabaStoreAccountSyncHelper::getStoreAccountInfoMap($this->clientId,$ownerAccountIds);
        }

		if ($this->showLastOwnerAccountInfo) {
			$ownerAccountIds = array_filter(array_unique(array_merge($ownerAccountIds, array_column($list, 'last_owner_account_id'))));
		}

		if (!empty($ownerAccountIds)) {
			$ownerAccountMap = AlibabaStoreAccountSyncHelper::getStoreAccountInfoMap($this->clientId, $ownerAccountIds);
		}

        $companyInfoMap = [];
        if( $this->showCompanyInfo )
        {
            $companyIds = array_filter(array_column($list, 'company_id'));
            $companyInfoMap = Helper::getCompanyInfoMap($this->clientId, $companyIds);

            if ($this->showCompanyUserInfo || $this->showStoreException) {
                $userIds = array_reduce($companyInfoMap, function ($res, $item) {
                    return $item['user_id'] ? array_merge($res, $item['user_id']) : $res;
                }, []);

                $userInfo = \common\library\account\Helper::getBatchUserInfo($this->clientId, $userIds);
                $userInfoMap = \ArrayUtil::keyBy($userInfo, function ($item) {
                    /** @var $item \common\library\account\UserInfo */
                    return ['key' => $item->user_id, 'value' => ['user_id' => $item->user_id, 'nickname' => $item->nickname]];
                });

                foreach ($companyInfoMap as $k => $value) {
                    foreach ($value['user_id'] as $userId) {
                        isset($userInfoMap[$userId]) && $companyInfoMap[$k]['user_info'][] = $userInfoMap[$userId];
                    }
                }

            }
        }

        $storeInfoMap = [];
        if ($this->showStoreInfo || $this->showStoreException) {
            $storeIds = array_filter(array_column($list, 'store_id'));

            if ($storeIds) {
                $storeInfoMap = AlibabaStoreService::getStoreInfoMaps($this->clientId, $storeIds);
            }
        }

        $authExceptionInfo = $userMapExceptionInfo = $switchExceptionInfo = [];
        if ($this->showStoreException) {

            // 检查授权
            $now = time();
            $storeIds = [];
            foreach ($storeInfoMap as $storeId => $storeInfo) {
                // 检查授权
                if (
                    $storeInfo['enable_flag'] == BaseObject::ENABLE_FLAG_FALSE ||
                    $storeInfo['oauth_flag'] != Constant::STORE_OAUTH_FLAG_AUTH ||
                    $storeInfo['expire_time'] < $now
                ) {
                    $authExceptionInfo[$storeId] = Constant::STORE_E_AUTH;
                    continue;  //优先级问题  没有授权就不检查开关了
                }
                $storeIds[] = $storeId;

                // 检查开关
                if (!$storeInfo['sync_customer_flag']) $switchExceptionInfo[$storeId] = Constant::STORE_E_SYNC_CLOSED;

            }

            // 检查用户映射关系
            if (!empty($storeIds) && !empty($ownerAccountIds)) {

                $storeMemberListObj = new AlibabaStoreMembersList($this->clientId);
                $storeMemberListObj->setStoreId($storeIds);
                $storeMemberListObj->setSellerAccountIds($ownerAccountIds);
                $storeMemberListObj->setFields(['store_id','user_id','seller_account_id']);
                $storeMemberList = $storeMemberListObj->find();

                foreach($storeMemberList as $item){
                    $userInfo = new \common\library\account\UserInfo($item['user_id'], $this->clientId);

                    if(!$item['user_id'] || !$userInfo->checkIsAvailable()){
                        $userMapExceptionInfo[$item['store_id']][$item['seller_account_id']] = Constant::STORE_E_USER_MAP;
                    }
                }

            }

        }

        $this->setMapData([
            'owner_account_info' => $ownerAccountMap,
            'company_info' => $companyInfoMap,
            'store_info' => $storeInfoMap,
            'auth_exception' => $authExceptionInfo,
            'user_map_exception' => $userMapExceptionInfo,
            'switch_exception' => $switchExceptionInfo,
        ]);
    }


    protected function format($data)
    {
        $result = $this->buildFieldsInfo($data);

        if( array_key_exists('fail_content',$result))
        {
            $result['fail_content'] = is_string($result['fail_content'])?json_decode($result['fail_content'], true):$result['fail_content'];
            if( isset($result['fail_content']['msg']))
            {
                $result['fail_content']['msg'] = \Yii::t('alibaba', $result['fail_content']['msg']);
            }
        }

        if( array_key_exists('sync_time', $result ))
        {
            $result['sync_time'] = substr($result['sync_time'], 0, 4) == '1970' ?'': $result['sync_time'];
        }

        if( $this->showOwnerAccountInfo )
        {
            $ownerAccountInfo = $this->getMapData('owner_account_info', $data['owner_account_id'])?:[
                'seller_account_id' => $data['owner_account_id'],
                'seller_email' => $data['owner_email'],
                'enable_flag' => 0,
                'first_name' => '',
                'last_name' => '',
                'login_id' => '',
            ];

            $result['owner_info'] = $ownerAccountInfo;
        }

		if( $this->showLastOwnerAccountInfo )
		{
			$lastOwnerAccountInfo = $this->getMapData('owner_account_info', $data['last_owner_account_id'])?:[
				'seller_account_id' => $data['last_owner_account_id'],
				'seller_email' => '',
				'enable_flag' => 0,
				'first_name' => '',
				'last_name' => '',
				'login_id' => '',
			];

			$result['last_owner_info'] = $lastOwnerAccountInfo;
		}

        if( $this->showLatelyAlibabaInfo )
        {
            //由于阿里接口限制, 只能单个调用,性能极差, 只能在访问量较小的场景使用
            $alibabaInfo = $this->getLatelyAlibabaInfo($data['store_id'], $data['alibaba_company_id']);
            $result['alibaba_company_name'] = $alibabaInfo['company_name']??$data['alibaba_company_name'];
            if( empty($data['detail_url']) && !empty($alibabaInfo['detail_url']) )
            {
                $result['detail_url'] = $alibabaInfo['detail_url'];
                CustomerSyncHelper::updateCompanyDetailUrl($this->clientId, $data['alibaba_company_id'], $alibabaInfo['detail_url']);
            }
        }

        if( $this->showCompanyInfo )
        {
            $companyInfo = $this->getMapData('company_info', $data['company_id'])??[];
            $result['company_info'] = [
                'company_id' => $companyInfo['company_id']??0,
                'name' => $companyInfo['name']??'',
                'serial_id' => $companyInfo['serial_id']??'',
                'pool_id' => $companyInfo['pool_id'] ?? 0,
                'is_archive' => $companyInfo['is_archive'] ?? 0,
                'create_time' => $companyInfo['create_time'] ?? '',
                'trail_status' => $companyInfo['trail_status'] ?? 0,
                'user_id' => $companyInfo['user_id'] ?? [],
                'scope_user_ids' => $companyInfo['scope_user_ids'] ?? [],
                'user_info' => $companyInfo['user_info'] ?? [],
            ];
        }

        if ($this->showStoreInfo) {
            $storeInfo = $this->getMapData('store_info', $data['store_id']) ?? [];
            $result['store_info'] = [
                'store_id' => $storeInfo['store_id'] ?? 0,
                'store_name' => $storeInfo['store_name'] ?? '',
            ];
            if (array_key_exists('delete_flag', $storeInfo)) {
                $result['store_info']['delete_flag'] = $storeInfo['delete_flag'];
            }
        }

        if ($this->showStoreException) {
            //优先级 ： 授权失效 > 用户映射关系异常 > 实时同步开关关闭
            $exception = $this->getMapData('auth_exception', $data['store_id']);

            if(!$exception){
                $userMapException = $this->getMapData('user_map_exception', $data['store_id']);
                $exception = $userMapException[$data['owner_account_id']]??0;
            }

            if(!$exception){
                $exception = $this->getMapData('switch_exception', $data['store_id']);
            }
            $result['store_info']['exception'] = $exception ?: 0;

        }


        return $result;
    }

    /**
     * 获取最新的公司名称,性能极差,慎用
     * @param $storeId
     * @param $alibabaCompanyId
     * @return array
     */
    protected function getLatelyAlibabaInfo($storeId, $alibabaCompanyId)
    {
        $info =[];
        if( !isset($this->_sessionKeyMap[$storeId]))
        {
            $alibabaStore = new AlibabaStore($this->clientId, $storeId);
            $this->_sessionKeyMap[$storeId] = $alibabaStore->getAccessToken();
        }

        $sessionKey = $this->_sessionKeyMap[$storeId];

        if( empty($sessionKey))
            return $info;

        try
        {
            $alibabaCompany = new AlibabaCompany($sessionKey, $alibabaCompanyId);
            $data = $alibabaCompany->getInfo();
            $info = CustomerSyncHelper::parserCompanyInfo($data);
        }catch (\Exception $e)
        {
            \LogUtil::error('getLatelyAlibabaCompanyName fail '.$e->getMessage());
        }

        return $info;

    }
}
