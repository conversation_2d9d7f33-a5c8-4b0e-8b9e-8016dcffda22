<?php
/**
 * Created by PhpStorm.
 * User: yoyo
 * Date: 2021-01-06
 * Time: 10:27
 */

namespace common\library\alibaba\customer;


use common\library\setting\library\config\ConfigApi;
use common\library\setting\library\config\ConfigConstant;
use Constants;

class AlibabaCustomerSyncSettingFormatter extends \ListItemFormatter
{


    protected $clientId;
    protected $specifyFields;
    protected $showSyncPrivateCustomerCount = false;
    protected $showSyncPublicCustomerCount = false;
    protected $showLastPushUpdateTime = false;
    protected $showFirstTimeFlag = false;
    protected $withMultiStoreHandler = false;
    protected $withMultiStoreField = false;

    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }


    public function setSpecifyFields($specifyFields)
    {
        $this->specifyFields = $specifyFields;
    }

    /**
     * @param bool $showSyncPrivateCustomerCount
     */
    public function setShowSyncPrivateCustomerCount(bool $showSyncPrivateCustomerCount)
    {
        $this->showSyncPrivateCustomerCount = $showSyncPrivateCustomerCount;
    }

    /**
     * @param bool $showSyncPublicCustomerCount
     */
    public function setShowSyncPublicCustomerCount(bool $showSyncPublicCustomerCount)
    {
        $this->showSyncPublicCustomerCount = $showSyncPublicCustomerCount;
    }


    public function setShowLastPushUpdateTime(bool $showLastPushUpdateTime)
    {
        $this->showLastPushUpdateTime = $showLastPushUpdateTime;
    }

    public function setShowFirstTimeFlag(bool $showFirstTimeFlag)
    {
        $this->showFirstTimeFlag = $showFirstTimeFlag;
    }

    public function withMultiStoreHandler($withMultiStoreHandler)
    {
        $this->withMultiStoreHandler = $withMultiStoreHandler;
    }

    public function withMultiStoreField($withMultiStoreField)
    {
        $this->withMultiStoreField = $withMultiStoreField;
    }


    public function syncSettingInfoSetting()
    {
        $this->setSpecifyFields(
            [
                'setting_id',
                'enable_flag',
                'pool_id',
                'customer_status',
                'sync_status',
                'last_open_time',
                'last_sync_time',
                'sync_customer_extent',
                'sync_owner_change',
                'update_type',
                'update_status_flag',
                'archive_time_start',
                'archive_time_end',
                'move_public_flag',
                'company_rename_flag'
            ]
        );
        $this->setShowLastPushUpdateTime(true);
        $this->setShowSyncPrivateCustomerCount(true);
        $this->setShowSyncPublicCustomerCount(true);
        $this->withMultiStoreHandler(true);
        $this->setShowFirstTimeFlag(true);
        $this->withMultiStoreField(true);
    }

    protected function format($data)
    {

        $result = $this->buildFieldsInfo($data);
        if(isset($data['customer_status']) && $data['customer_status']){
            $result['customer_status'] = json_decode($data['customer_status'],true)??[];
        }

        if( $this->showSyncPrivateCustomerCount )
        {
            $result['sync_private_customer_count'] = CustomerSyncHelper::getSyncPrivateCustomerCount($this->clientId, $data['store_id']);
        }

        if( $this->showSyncPublicCustomerCount )
        {
            $result['sync_public_customer_count'] = CustomerSyncHelper::getSyncPublicCustomerCount($this->clientId, $data['store_id']);
        }

        if($this->showLastPushUpdateTime)
        {
            $result['last_push_update_time'] = CustomerSyncHelper::getLastPushUpdateTime($this->clientId, $data['store_id']);
        }

        if (empty($result['archive_time_start']??'')) {
            $result['archive_time_start'] = '';
        } else {
            if (in_array($result['archive_time_start'], ['1970-01-01 00:00:01', '0000-00-00 00:00:00'])) {
                $result['archive_time_start'] = '';
            }else{
                $result['archive_time_start'] = $data['archive_time_start'];
            }
        }

        if (empty($result['archive_time_end']??'')) {
            $result['archive_time_end'] = '';
        } else {
            if (in_array($result['archive_time_end'], ['1970-01-01 00:00:01', '0000-00-00 00:00:00'])) {
                $result['archive_time_end'] = '';
            }else{
                $result['archive_time_end'] = $data['archive_time_end'];
            }
        }

        if ($this->withMultiStoreHandler) {
            $api = new ConfigApi($this->clientId, Constants::TYPE_CUSTOMER);
            $result['customer_multi_store_handle'] = $api->getCustomerSyncSetting();
        }

        if($this->showFirstTimeFlag)
        {
            $result['first_time_flag'] = CustomerSyncHelper::getFirstTimeFlag($this->clientId, $data['store_id']);
        }

        if ($this->withMultiStoreField) {
            $api = new ConfigApi($this->clientId, Constants::TYPE_CUSTOMER);
            $fieldTactic = $api->getCustomerSyncSettingByKey(ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_TACTIC);
            $result['customer_multi_store_field_tactic'] = empty($fieldTactic) ? 'custom' : $fieldTactic;
            $fieldSetting = $api->getConfigByKey(0,0,ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_SETTING,[ConfigConstant::FIELD_LIST_KEY])[ConfigConstant::FIELD_LIST_KEY] ?? [];
            $result['customer_multi_store_field_setting'] = empty($fieldSetting) ? json_encode(['company.name' => 1]) : json_encode($fieldSetting);
        }

        return $result;
    }

    /**
     * @param $data
     * @return array
     * 返回数据格式的构建
     */
    protected function buildFieldsInfo($data)
    {

        if ($this->specifyFields == []) {
            $this->specifyFields = array_keys($data);
        }

        $result = \ArrayUtil::columns($this->specifyFields, $data, '');

        return $result;
    }

}
