<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/12
 * Time: 11:21 AM
 */

namespace common\library\alibaba\customer;


use common\library\util\SqlBuilder;

class AlibabaCustomerNoteRelationList extends \MysqlList
{
    protected $clientId;
    protected $storeId;
    protected $alibabaCompanyId;
    protected $sellerAccountId;
    protected $companyId;
    protected $trailId;
    protected $alibabaNoteId;

    protected $fields;

    /**
     * AlibabaCompanyRelationList constructor.
     * @param $clientId
     */
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    public function getQueryTable()
    {
        return \AlibabaCustomerNoteRelationModel::model()->tableName();
    }

    /**
     * @param mixed $fields
     */
    public function setFields(array $fields)
    {
        $this->fields = $fields;
    }


    /**
     * @param mixed $storeId
     */
    public function setStoreId($storeId)
    {
        $this->storeId = $storeId;
    }

    /**
     * @param mixed $alibabaCompanyId
     */
    public function setAlibabaCompanyId($alibabaCompanyId)
    {
        $this->alibabaCompanyId = $alibabaCompanyId;
    }

    /**
     * @param mixed $sellerAccountId
     */
    public function setSellerAccountId($sellerAccountId)
    {
        $this->sellerAccountId = $sellerAccountId;
    }

    /**
     * @param mixed $companyId
     */
    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;
    }

    /**
     * @param mixed $trailId
     */
    public function setTrailId($trailId)
    {
        $this->trailId = $trailId;
    }

    /**
     * @param mixed $alibabaNoteId
     */
    public function setAlibabaNoteId($alibabaNoteId)
    {
        $this->alibabaNoteId = $alibabaNoteId;
    }



    public function buildParams()
    {
        $sql = 'client_id=:client_id';
        $params =[':client_id' => $this->clientId];

        if( $this->storeId )
        {
            SqlBuilder::buildIntWhere('', 'store_id',$this->storeId,$sql, $params);
        }

        if( $this->alibabaCompanyId )
        {
            SqlBuilder::buildIntWhere('', 'alibaba_company_id',$this->alibabaCompanyId,$sql, $params);
        }

        if( $this->sellerAccountId )
        {
            SqlBuilder::buildIntWhere('', 'seller_account_id',$this->sellerAccountId,$sql, $params);
        }


        if( $this->companyId )
        {
            SqlBuilder::buildIntWhere('', 'company_id',$this->companyId,$sql, $params);
        }

        if( $this->alibabaNoteId )
        {
            SqlBuilder::buildIntWhere('', 'alibaba_note_id',$this->alibabaNoteId,$sql, $params);
        }

        if( $this->trailId )
        {
            SqlBuilder::buildIntWhere('', 'trail_id',$this->trailId,$sql, $params);
        }


        return [$sql, $params];
    }


    public function find()
    {
        list($where,  $params) = $this->buildParams();

        $db = \AlibabaCustomerNoteRelationModel::getDbByClientId($this->clientId);
        $limit  = $this->buildLimit();
        $orderBy = $this->buildOrderBy();
        $table = $this->getQueryTable();

        if (empty($this->fields)) {
            $fields = '*';
        } else {
            $fields = implode(',', $this->fields);
        }
        $sql = "SELECT $fields FROM {$table} WHERE {$where} {$orderBy} {$limit}";

        $result = $db->createCommand($sql)->queryAll(true, $params)?:[];
        return $result;
    }

    public function count()
    {
        list($where,  $params) = $this->buildParams();

        $db = \AlibabaCustomerNoteRelationModel::getDbByClientId($this->clientId);
        $table = $this->getQueryTable();

        $sql = "SELECT count(1) FROM {$table} WHERE {$where}";

        $result = $db->createCommand($sql)->queryScalar( $params);
        return intval($result);
    }

    public function maxNoteId()
    {
        list($where,  $params) = $this->buildParams();

        $db = \AlibabaCustomerNoteRelationModel::getDbByClientId($this->clientId);
        $table = $this->getQueryTable();

        $sql = "SELECT max(alibaba_note_id) FROM {$table} WHERE {$where}";

        $result = $db->createCommand($sql)->queryScalar( $params);
        return intval($result);
    }

}
