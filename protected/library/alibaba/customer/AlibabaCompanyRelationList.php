<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2021/1/12
 * Time: 11:21 AM
 */

namespace common\library\alibaba\customer;


use common\library\util\SqlBuilder;
use common\models\client\AlibabaCompanyRelation as AlibabaCompanyRelationModel;

class AlibabaCompanyRelationList extends \MysqlList
{

    protected $clientId;
    protected $storeId;
    protected $taskId;
    protected $alibabaCompanyId;
    protected $companyId;
    protected $ownerAccountId;
    protected $buyerAccountId;
    protected $syncStatus;
    protected $failReason;
    protected $startSyncDate;
    protected $endSyncDate;
    protected $isAdd;
    protected $isSync;
    protected $alibabaCompanyIdNotEmpty = false;

    protected $keyword;

    protected $fields;

    protected $isProtecting = 0; // 默认展示非待添加状态的

    protected $isPublic;

    /**
     * AlibabaCompanyRelationList constructor.
     * @param $clientId
     */
    public function __construct($clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @return \ListItemFormatter|AlibabaCompanyRelationFormatter
     */
    public function getFormatter()
    {
        if( $this->formatter === null)
        {
            $this->formatter = new AlibabaCompanyRelationFormatter($this->clientId);
        }

        return parent::getFormatter();
    }


    public function getQueryTable()
    {
        return AlibabaCompanyRelationModel::model()->tableName();
    }

    /**
     * @param mixed $fields
     */
    public function setFields(array $fields)
    {
        $this->fields = $fields;
    }


    /**
     * @param mixed $storeId
     */
    public function setStoreId($storeId)
    {
        $this->storeId = $storeId;
    }

    /**
     * @param int $taskId
     */
    public function setTaskId(int $taskId)
    {
        $this->taskId = $taskId;
    }

    /**
     * @param int $isPublic
     */
    public function setIsPublic($isPublic)
    {
        $this->isPublic = $isPublic;
    }

    /**
     * @param mixed $alibabaCompanyId
     */
    public function setAlibabaCompanyId($alibabaCompanyId)
    {
        $this->alibabaCompanyId = $alibabaCompanyId;
    }

    /**
     * @param mixed $companyId
     */
    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;
    }

    /**
     * @param mixed $ownerAccountId
     */
    public function setOwnerAccountId($ownerAccountId)
    {
        $this->ownerAccountId = $ownerAccountId;
    }

    /**
     * @param mixed $buyerAccountId
     */
    public function setBuyerAccountId($buyerAccountId)
    {
        $this->buyerAccountId = $buyerAccountId;
    }

    /**
     * @param mixed $syncStatus
     */
    public function setSyncStatus($syncStatus)
    {
        $this->syncStatus = $syncStatus;
    }

    /**
     * @param mixed $startSyncDate
     */
    public function setStartSyncDate($startSyncDate)
    {
        $this->startSyncDate = $startSyncDate;
    }

    /**
     * @param mixed $endSyncDate
     */
    public function setEndSyncDate($endSyncDate)
    {
        $this->endSyncDate = $endSyncDate;
    }

    /**
     * @param mixed $keyword
     */
    public function setKeyword($keyword)
    {
        $this->keyword = $keyword;
    }

    /**
     * @param mixed $failReason
     */
    public function setFailReason($failReason)
    {
        $this->failReason = $failReason;
    }

    /**
     * @param mixed $isAdd
     */
    public function setIsAdd(int $isAdd)
    {
        $this->isAdd = $isAdd;
    }

    public function setAlibabaCompanyIdNotEmpty()
    {
        $this->alibabaCompanyIdNotEmpty = true;
    }

    /**
     * @param mixed $isSync
     */
    public function setIsSync($isSync)
    {
        $this->isSync = $isSync;
    }

    public function setIsProtecting($isProtecting)
    {
        $this->isProtecting = $isProtecting;
    }

    public function buildParams()
    {
        $sql = 'client_id=:client_id';
        $params =[':client_id' => $this->clientId];

        if( $this->storeId )
        {
            SqlBuilder::buildIntWhere('', 'store_id',$this->storeId,$sql, $params);
        }

        if( $this->ownerAccountId )
        {
            SqlBuilder::buildIntWhere('', 'owner_account_id',$this->ownerAccountId,$sql, $params);
        }

        if( $this->buyerAccountId )
        {
            SqlBuilder::buildIntWhere('', 'buyer_account_id',$this->buyerAccountId,$sql, $params);
        }

        if($this->taskId)
        {
            $attributes = \common\models\client\AlibabaCustomerSyncData::model()->findByPk($this->taskId);
            if(is_null($this->isPublic) || $this->isPublic === '')
            {
                if($attributes['alibaba_company_ids'])
                {
                    $aliCompanyIds = json_decode(snappy_uncompress($attributes['alibaba_company_ids']), true) ?? [];
                }
            }else if($this->isPublic == 1)
            {
                if($attributes['alibaba_public_company_ids'])
                {
                    $aliCompanyIds = json_decode(snappy_uncompress($attributes['alibaba_public_company_ids']), true) ?? [];
                }

            }else {
                if($attributes['alibaba_private_company_ids'])
                {
                    $aliCompanyIds = json_decode(snappy_uncompress($attributes['alibaba_private_company_ids']), true) ?? [];
                }
            }
            if(isset($aliCompanyIds) && is_array($aliCompanyIds))
            {
                $this->alibabaCompanyId = array_unique($aliCompanyIds);
            }

        }

        if( $this->companyId )
        {
            SqlBuilder::buildIntWhere('', 'company_id',$this->companyId,$sql, $params);
        }

        if( $this->alibabaCompanyId )
        {
            SqlBuilder::buildIntWhere('', 'alibaba_company_id',$this->alibabaCompanyId,$sql, $params);
        }

        if( $this->syncStatus )
        {
            SqlBuilder::buildIntWhere('', 'sync_status',$this->syncStatus,$sql, $params);
        }

        if( $this->startSyncDate || $this->endSyncDate )
        {
            SqlBuilder::buildDateRange('','sync_time', $this->startSyncDate, $this->endSyncDate, $sql, $params);
        }

        if( $this->keyword )
        {
            $keyword = \Util::escapeDoubleQuoteSql($this->keyword);
            $sql .= " and alibaba_company_name ilike '%{$keyword}%'";
        }

        if( $this->failReason )
        {
            SqlBuilder::buildIntWhere('', 'fail_reason',$this->failReason,$sql, $params);
        }

        if( $this->isAdd )
        {
            SqlBuilder::buildIntWhere('', 'is_add',$this->isAdd,$sql, $params);
        }

        if( $this->isSync )
        {
            $sql .= ' and company_id >0';
        }

        if( $this->alibabaCompanyIdNotEmpty )
        {
            $sql .= ' and alibaba_company_id >0';
        }

        if(!is_null($this->isProtecting))
        {
            SqlBuilder::buildIntWhere('', 'is_protecting', $this->isProtecting,$sql, $params);
        }

        return [$sql, $params];
    }


    public function find()
    {
        list($where,  $params) = $this->buildParams();

        $db = AlibabaCompanyRelationModel::getDbByClientId($this->clientId);
        $limit  = $this->buildLimit();
        $orderBy = $this->buildOrderBy();
        $table = $this->getQueryTable();

        if (empty($this->fields)) {
            $fields = '*';
        } else {
            $fields = implode(',', $this->fields);
        }
        $sql = "SELECT $fields FROM {$table} WHERE {$where} {$orderBy} {$limit}";

        $result = $db->createCommand($sql)->queryAll(true, $params)?:[];

        if($this->formatter)
        {
            $this->formatter->setListData($result);
            $result = $this->formatter->result();
        }

        return $result;
    }

    public function count()
    {
        list($where,  $params) = $this->buildParams();

        $db = AlibabaCompanyRelationModel::getDbByClientId($this->clientId);
        $table = $this->getQueryTable();

        $sql = "SELECT count(1) FROM {$table} WHERE {$where}";

        $result = $db->createCommand($sql)->queryScalar( $params);
        return intval($result);
    }
}
