<?php

namespace common\library\common_file;

class Constants
{

    const MAX_FOLDER_LAYER_LIMIT = 10;
    const MAX_FOLDER_NAME_LIMIT = 255;
    const MAX_FILE_NAME_LIMIT = 255;
    const BATCH_MAX_FILE_LIMIT = 10000;
    const BATCH_RECOVER_LIMIT = 2000;

    const FOLDER_LAYER_PREFIX_SEPARATOR = '-';

    const FOLDER_WRITE_READ = 0;
    const FOLDER_READ_ONLY = 1;

    const ROOT_FOLDER_ALL_ID = 0;
    const ROOT_FOLDER_CLIENT_ID = -1;

    const FOLDER_ALL_NAME = "全部文档";
    const FOLDER_CLIENT_NAME = "共享文档";

    // 商机附件来源
    const FILE_LIST_REFER_TYPE_AI = 2; // 原“ai”
    const FILE_LIST_REFER_TYPE_DEFAULT = 1; // 原“default”

    const ROOT_FOLDER_ID_MAP = [
        self::ROOT_FOLDER_ALL_ID => self::FOLDER_ALL_NAME,
        self::ROOT_FOLDER_CLIENT_ID => self::FOLDER_CLIENT_NAME,
    ];

    // 云盘-关联知识库类型
    const DISK_EXTERNAL_RELATE_TYPE_KNOWLEDGE = 1;


    /**
     * 客户文件-关联业务类型
     */
    const EXTERNAL_RELATE_TYPE_COMPANY_EMAIL = 1;
    const EXTERNAL_RELATE_TYPE_COMPANY_UPLOAD = 2;
    const EXTERNAL_RELATE_TYPE_COMPANY_ORDER_EXPORT = 3;
    const EXTERNAL_RELATE_TYPE_COMPANY_QUOTATION_EXPORT = 4;
    const EXTERNAL_RELATE_TYPE_COMPANY_PURCHASE_ORDER_EXPORT = 5;
    const EXTERNAL_RELATE_TYPE_COMPANY_SHIPPING_EXPORT = 6;
    const EXTERNAL_RELATE_TYPE_COMPANY_INQUIRY_COLLABORATION_EXPORT = 7;


}