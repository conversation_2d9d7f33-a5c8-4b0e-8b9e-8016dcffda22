<?php

namespace common\library\waba\jobs;

use common\library\queue_v2\job\BaseJob;
use common\library\queue_v2\QueueConstant;
use common\library\sns\batch_message\BatchMessageTask;
use Exception;

class BatchSendMessageJob extends BaseJob
{
    public $maxExceptions = 5;

    public $channel = QueueConstant::CONNECTION_NAME_DEFAULT;

    public $tag = QueueConstant::CONSUMER_PRIORITY_HIGH;

    /**
     * @var int 客户ID
     */
    public $clientId;

    /**
     * @var int 用户ID
     */
    public $userId;

    /***
     * @var array 过滤重复接收人
     */
    public $filter_repeat;

    /***
     * @var array 过滤同事客户接收人
     */
    public $filter_colleagues;

    /**
     * @var int 任务ID
     */
    public $task_id;

    /***
     * @var array 数据
     */
    public $data;

    /**
     * @var string 消息内容
     */
    public function __construct(array $data)
    {
        $this->data = $data;
        $this->clientId = $data['client_id'];
        $this->userId = $data['user_id'];

        $this->filter_repeat = $data['filter_repeat'] ?? [];
        $this->filter_colleagues = $data['filter_colleagues'] ?? [];
        $this->task_id = $data['task_id'] ?? 0;
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        \LogUtil::info("process BatchSendMessageJob: ".print_r($this->data, true));

        if(empty($this->clientId) || empty($this->userId) || empty($this->task_id))
            return;

        \User::setLoginUserById($this->userId);
        $task = new BatchMessageTask($this->clientId, $this->task_id);
        $task->setOpUserId($this->userId);
        $task->setFilterRepeatList($this->filter_repeat);
        $task->setFilterColleagueList($this->filter_colleagues);
        $task->decompose();
        $task->send();
    }
}
