<?php

namespace common\library\ai_agent\utils;

class PartialJsonParser
{
    /**
     * 解析方法：
     * 1. 从左到右，逐个字符读取
     * 2. 根据当前状态，判断当前字符的含义，例如
     *   { 后面通常会出现双引号和key、
     *   : 后面通常是value
     *   " 可能是key/value的开始/结束
     * 3. 读取到字符后更新当前的状态，然后读取下一个字符
     */

    public const STATUS_NONE = 'none';
    public const STATUS_WAITING_KEY = 'waiting_key';    // 等待读取key，通常是遇到{
    public const STATUS_PARSING_KEY = 'parsing_key';    // 正在读取key，通常是遇到"
    public const STATUS_WAITING_VALUE = 'waiting_value';    // 等待读取value，通常是遇到:
    public const STATUS_PARSING_VALUE = 'parsing_value';    // 正在读取value，通常是遇到"

    public const STATUS_PARSING_VALUE_FINISH = 'parse_value_finish';    // 遇到双引号，读取值结束

    public string $status = self::STATUS_NONE;

    public const CONTEXT_NONE = 'none';
    public const CONTEXT_ARRAY = 'array';   // 当前正在读取一个数组
    public const CONTEXT_OBJECT = 'object'; // 当前正在读取一个对象
    protected bool $isParsingNumber = false;    // 正在解析数字


    public string $context = self::CONTEXT_NONE;

    public int $level = 0;
    public array $parsed = [];
    public array $subArray = [];

    public function parse(string $json): array
    {
        $len = mb_strlen($json);
        $key = null;
        $value = null;

        for ($i = 0; $i < $len; $i++) {
            $char = mb_substr($json, $i, 1);

            switch ($char) {
                case '{':
                    $this->status = self::STATUS_WAITING_KEY;
                    if ($this->context === self::CONTEXT_ARRAY) {
                        $this->level++;
                    }
                    $this->context = self::CONTEXT_OBJECT;
                    break;

                case '}':
                    if ($key) {
                        $this->addKeyValue($key, $value);
                        $key = null;
                        $value = null;
                    }
                    if ($this->level === 0) {
                        return $this->parsed;
                    }
                    $this->level--;
                    $this->context = self::CONTEXT_ARRAY;
                    $this->addKeyValue(null, $this->subArray);
                    $this->subArray = [];

                    $this->status = self::STATUS_NONE;
                    break;
                case '[':
                    if ($this->status === self::STATUS_PARSING_VALUE) {
                        $this->appendToKeyOrValue($char, $key, $value);
                    } else {
                        $this->status = self::STATUS_WAITING_VALUE;
                        $this->context = self::CONTEXT_ARRAY;
                    }
                    break;
                case ']':
                    if ($this->status === self::STATUS_PARSING_VALUE && !$this->isParsingNumber) {
                        // 作为字符串的一部分
                        $this->appendToKeyOrValue($char, $key, $value);
                    } else {
                        if ($value !== null) {
                            $this->addKeyValue('', $value);
                        }
                        $this->status = self::STATUS_NONE;
                        $this->context = self::CONTEXT_NONE;
                    }
                    break;

                case '"': // 开始或结束一个键或值
                    $nextStatusMap = [
                        self::STATUS_WAITING_KEY => self::STATUS_PARSING_KEY,
                        self::STATUS_PARSING_KEY => self::STATUS_NONE,
                        self::STATUS_PARSING_VALUE_FINISH => self::STATUS_NONE,
                        self::STATUS_WAITING_VALUE => self::STATUS_PARSING_VALUE,
                        self::STATUS_PARSING_VALUE => self::STATUS_PARSING_VALUE_FINISH,
                        self::STATUS_NONE => $this->context === self::CONTEXT_OBJECT
                            ? self::STATUS_PARSING_KEY
                            : self::STATUS_PARSING_VALUE,
                    ];
                    $before = $this->status;
                    if(isset($nextStatusMap[$this->status])) {
                        $this->status = $nextStatusMap[$this->status];
                    }
                    if ($before === self::STATUS_PARSING_VALUE) {   // 开始解析值，如果值是引号开头，说明值不是数字
                        $this->isParsingNumber = false;
                    }
                    if($before === self::STATUS_PARSING_VALUE && $this->status === self::STATUS_PARSING_VALUE_FINISH ) {
                        $this->addKeyValue($key, (string) $value);  // value是引号包围的，必须是字符串
                        $key = null;
                        $value = null;
                    }
                    break;

                case ':':
                    if ($this->status === self::STATUS_PARSING_KEY || $this->status === self::STATUS_NONE) {
                        // 解析key结束，准备解析value
                        $value = null;
                        $this->status = self::STATUS_WAITING_VALUE;
                    } else {
                        $this->appendToKeyOrValue($char, $key, $value);
                    }
                    break;

                case ',':
                    if($this->status === self::STATUS_PARSING_VALUE_FINISH
                    || ($this->status === self::STATUS_PARSING_VALUE && $this->isParsingNumber)
                    ) {
                        /**
                         * 遇到逗号的情况：
                         * - 字符串value结束：{"name: "Bob",
                         * - 数字结束: {"age": 17,
                         */
                        $this->addKeyValue($key, $value);
                        $key = null;
                        $value = null;
                        // 解析下一个
                        if ($this->context === self::CONTEXT_ARRAY) {
                            $this->status = self::STATUS_WAITING_VALUE;
                        } elseif($this->context === self::CONTEXT_OBJECT) {
                            $this->status = self::STATUS_WAITING_KEY;
                        }
                    } elseif ($this->status === self::STATUS_WAITING_VALUE) {
                        /**
                         * key结束waiting_value时，遇到了逗号，是gpt返回缺少了value的情况：
                         *  {"value":,
                         */
                        $this->addKeyValue($key, null);
                        $key = null;
                        $value = null;
                        // 解析下一个
                        if ($this->context === self::CONTEXT_ARRAY) {
                            $this->status = self::STATUS_WAITING_VALUE;
                        } elseif($this->context === self::CONTEXT_OBJECT) {
                            $this->status = self::STATUS_WAITING_KEY;
                        }
                    } else {
                        // 逗号是key、value内容的一部分
                        $this->appendToKeyOrValue($char, $key, $value);
                    }

                    break;

                case is_numeric($char) ? $char : null:
                case '-': // 负数
                    if($this->status === self::STATUS_WAITING_VALUE) {
                        $this->status = self::STATUS_PARSING_VALUE;
                        $this->isParsingNumber = true;
                    }
                    $this->appendToKeyOrValue($char, $key, $value);
                    break;

                default:
                    $this->appendToKeyOrValue($char, $key, $value);
                    break;
            }
        }

        return $this->parsed;
    }


    /**
     * 字符加到key/value
     * @param string $char
     * @param string|null $key
     * @param string|null $value
     * @return void
     */
    protected function appendToKeyOrValue(string $char, ?string &$key, ?string &$value): void
    {
        if ($this->status === self::STATUS_PARSING_VALUE) {
            $value .= $char;
        }
        if ($this->status === self::STATUS_PARSING_KEY) {
            $key .= $char;
        }
    }

    /**
     * 解析出一个key/value后，添加到parsed或subArray中
     * @param string|null $key
     * @param mixed $value
     * @return void
     */
    protected function addKeyValue(?string $key, mixed $value): void
    {
        if ($key === null && $value === null) {
            return;
        }
        if ($this->context === self::CONTEXT_OBJECT) {
            if ($this->level === 0) {
                $this->parsed[$key] = $value;
            }
            if ($this->level === 1) {
                $this->subArray[$key] = $value;
            }
        } elseif ($this->context === self::CONTEXT_ARRAY) {
            if ($this->level === 0) {
                $this->parsed[] = $value;
            } else {
                $this->subArray[] = $value;
            }
        }
    }
}