<?php

namespace common\library\ai_agent\agent;

use common\components\BaseObject;

/**
 *
 * @property integer $agent_id
 * @property string $agent_name
 * @property string $agent_type
 * @property string $create_time
 * @property string $update_time
 * @property integer $enable_flag
 * @property integer $publish_flag
 * @property integer $scene_type
 * @property string $default_version
 * @property string $desc
 * @property string $icon
 *
 * @method AiAgentFormatter getFormatter()
 *
 */
class AiAgent extends BaseObject
{
    public function __construct($id = null)
    {
        $this->_getFormatter = true;
        $this->_formatter = new AiAgentFormatter();
        $this->_formatter->setNeedStrip(false);

        if ($id) {
            $this->loadById($id);
        }
    }

    protected function loadById($id)
    {
        $model = $this->getModelClass()::model()->find("agent_id=:agent_id", [":agent_id" => $id]);
        if ($model) {
            $this->setModel($model);
        } else {
            throw new \RuntimeException("查询错误！");
        }
    }

    public function getModelClass()
    {
        return \AiAgent::class;
    }
}