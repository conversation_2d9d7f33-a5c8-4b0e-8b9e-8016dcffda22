<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Email.proto

namespace protobuf\Email;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 邮件保存大附件请求 mailWrite/createLargeAttach
 *
 * Generated from protobuf message <code>PBCreateLargeAttachReq</code>
 */
class PBCreateLargeAttachReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int64 file_id = 1;</code>
     */
    protected $file_id = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $file_id
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Email::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int64 file_id = 1;</code>
     * @return int|string
     */
    public function getFileId()
    {
        return $this->file_id;
    }

    /**
     * Generated from protobuf field <code>int64 file_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFileId($var)
    {
        GPBUtil::checkInt64($var);
        $this->file_id = $var;

        return $this;
    }

}

