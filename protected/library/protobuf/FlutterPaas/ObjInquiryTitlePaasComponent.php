<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: PaasComponent.proto

namespace protobuf\FlutterPaas;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *  询价 Title 组件
 *
 * Generated from protobuf message <code>ObjInquiryTitlePaasComponent</code>
 */
class ObjInquiryTitlePaasComponent extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string title = 1;</code>
     */
    protected $title = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $title
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\PaasComponent::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Generated from protobuf field <code>string title = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

}

