<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: SalesGuide.proto

namespace protobuf\SalesGuide;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBPreViewDocumentRsp</code>
 */
class PBPreViewDocumentRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 page_id = 1;</code>
     */
    protected $page_id = 0;
    /**
     * Generated from protobuf field <code>.PBDocumentKindType kind = 2;</code>
     */
    protected $kind = 0;
    /**
     * Generated from protobuf field <code>uint64 file_id = 3;</code>
     */
    protected $file_id = 0;
    /**
     * Generated from protobuf field <code>string file_name = 4;</code>
     */
    protected $file_name = '';
    /**
     * Generated from protobuf field <code>string file_url = 5;</code>
     */
    protected $file_url = '';
    /**
     * Generated from protobuf field <code>string file_preview_url = 6;</code>
     */
    protected $file_preview_url = '';
    /**
     * Generated from protobuf field <code>uint64 file_size = 7;</code>
     */
    protected $file_size = 0;
    /**
     * Generated from protobuf field <code>string pdf_file_size = 8;</code>
     */
    protected $pdf_file_size = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $page_id
     *     @type int $kind
     *     @type int|string $file_id
     *     @type string $file_name
     *     @type string $file_url
     *     @type string $file_preview_url
     *     @type int|string $file_size
     *     @type string $pdf_file_size
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\SalesGuide::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 page_id = 1;</code>
     * @return int|string
     */
    public function getPageId()
    {
        return $this->page_id;
    }

    /**
     * Generated from protobuf field <code>uint64 page_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setPageId($var)
    {
        GPBUtil::checkUint64($var);
        $this->page_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBDocumentKindType kind = 2;</code>
     * @return int
     */
    public function getKind()
    {
        return $this->kind;
    }

    /**
     * Generated from protobuf field <code>.PBDocumentKindType kind = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setKind($var)
    {
        GPBUtil::checkEnum($var, \protobuf\SalesGuide\PBDocumentKindType::class);
        $this->kind = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 file_id = 3;</code>
     * @return int|string
     */
    public function getFileId()
    {
        return $this->file_id;
    }

    /**
     * Generated from protobuf field <code>uint64 file_id = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFileId($var)
    {
        GPBUtil::checkUint64($var);
        $this->file_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string file_name = 4;</code>
     * @return string
     */
    public function getFileName()
    {
        return $this->file_name;
    }

    /**
     * Generated from protobuf field <code>string file_name = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setFileName($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string file_url = 5;</code>
     * @return string
     */
    public function getFileUrl()
    {
        return $this->file_url;
    }

    /**
     * Generated from protobuf field <code>string file_url = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setFileUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string file_preview_url = 6;</code>
     * @return string
     */
    public function getFilePreviewUrl()
    {
        return $this->file_preview_url;
    }

    /**
     * Generated from protobuf field <code>string file_preview_url = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setFilePreviewUrl($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_preview_url = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 file_size = 7;</code>
     * @return int|string
     */
    public function getFileSize()
    {
        return $this->file_size;
    }

    /**
     * Generated from protobuf field <code>uint64 file_size = 7;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFileSize($var)
    {
        GPBUtil::checkUint64($var);
        $this->file_size = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string pdf_file_size = 8;</code>
     * @return string
     */
    public function getPdfFileSize()
    {
        return $this->pdf_file_size;
    }

    /**
     * Generated from protobuf field <code>string pdf_file_size = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setPdfFileSize($var)
    {
        GPBUtil::checkString($var, True);
        $this->pdf_file_size = $var;

        return $this;
    }

}

