<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: SalesGuide.proto

namespace protobuf\SalesGuide;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *文档用途列表请求响应
 *
 * Generated from protobuf message <code>PBTradeDocCateListRsp</code>
 */
class PBTradeDocCateListRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .PBTradeDocCateItem list = 1;</code>
     */
    private $list;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\protobuf\SalesGuide\PBTradeDocCateItem>|\Google\Protobuf\Internal\RepeatedField $list
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\SalesGuide::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .PBTradeDocCateItem list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBTradeDocCateItem list = 1;</code>
     * @param array<\protobuf\SalesGuide\PBTradeDocCateItem>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\SalesGuide\PBTradeDocCateItem::class);
        $this->list = $arr;

        return $this;
    }

}

