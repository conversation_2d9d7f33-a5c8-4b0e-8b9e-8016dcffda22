<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: services/customer.proto

namespace protobuf\services\customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 批量将相应名片草稿态数据转正式态
 *
 * Generated from protobuf message <code>services.BatchSaveBusinessCardDraftReq</code>
 */
class BatchSaveBusinessCardDraftReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated uint64 business_card_ids = 1;</code>
     */
    private $business_card_ids;
    /**
     * Generated from protobuf field <code>.PBBusinessCardSaveType save_type = 2;</code>
     */
    protected $save_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $business_card_ids
     *     @type int $save_type
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\services\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated uint64 business_card_ids = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBusinessCardIds()
    {
        return $this->business_card_ids;
    }

    /**
     * Generated from protobuf field <code>repeated uint64 business_card_ids = 1;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBusinessCardIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->business_card_ids = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBBusinessCardSaveType save_type = 2;</code>
     * @return int
     */
    public function getSaveType()
    {
        return $this->save_type;
    }

    /**
     * Generated from protobuf field <code>.PBBusinessCardSaveType save_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setSaveType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Customer\PBBusinessCardSaveType::class);
        $this->save_type = $var;

        return $this;
    }

}

