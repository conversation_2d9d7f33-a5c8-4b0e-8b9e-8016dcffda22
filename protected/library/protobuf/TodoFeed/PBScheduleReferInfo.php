<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: TodoFeed.proto

namespace protobuf\TodoFeed;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 日程关联对象信息
 *
 * Generated from protobuf message <code>PBScheduleReferInfo</code>
 */
class PBScheduleReferInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * 关联对象id
     *
     * Generated from protobuf field <code>uint64 id = 1;</code>
     */
    protected $id = 0;
    /**
     *关联对象名称
     *
     * Generated from protobuf field <code>string name = 2;</code>
     */
    protected $name = '';
    /**
     *关联对象类型
     *
     * Generated from protobuf field <code>uint32 refer_type = 3;</code>
     */
    protected $refer_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $id
     *           关联对象id
     *     @type string $name
     *          关联对象名称
     *     @type int $refer_type
     *          关联对象类型
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\TodoFeed::initOnce();
        parent::__construct($data);
    }

    /**
     * 关联对象id
     *
     * Generated from protobuf field <code>uint64 id = 1;</code>
     * @return int|string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * 关联对象id
     *
     * Generated from protobuf field <code>uint64 id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkUint64($var);
        $this->id = $var;

        return $this;
    }

    /**
     *关联对象名称
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     *关联对象名称
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     *关联对象类型
     *
     * Generated from protobuf field <code>uint32 refer_type = 3;</code>
     * @return int
     */
    public function getReferType()
    {
        return $this->refer_type;
    }

    /**
     *关联对象类型
     *
     * Generated from protobuf field <code>uint32 refer_type = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setReferType($var)
    {
        GPBUtil::checkUint32($var);
        $this->refer_type = $var;

        return $this;
    }

}

