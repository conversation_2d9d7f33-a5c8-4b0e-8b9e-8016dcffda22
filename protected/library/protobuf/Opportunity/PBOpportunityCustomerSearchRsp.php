<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Opportunity.proto

namespace protobuf\Opportunity;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 响应
 *
 * Generated from protobuf message <code>PBOpportunityCustomerSearchRsp</code>
 */
class PBOpportunityCustomerSearchRsp extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .PBOpportunityCustomerSearchItem list = 1;</code>
     */
    private $list;
    /**
     * Generated from protobuf field <code>uint32 total = 2;</code>
     */
    protected $total = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\protobuf\Opportunity\PBOpportunityCustomerSearchItem>|\Google\Protobuf\Internal\RepeatedField $list
     *     @type int $total
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Opportunity::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .PBOpportunityCustomerSearchItem list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Generated from protobuf field <code>repeated .PBOpportunityCustomerSearchItem list = 1;</code>
     * @param array<\protobuf\Opportunity\PBOpportunityCustomerSearchItem>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\Opportunity\PBOpportunityCustomerSearchItem::class);
        $this->list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 total = 2;</code>
     * @return int
     */
    public function getTotal()
    {
        return $this->total;
    }

    /**
     * Generated from protobuf field <code>uint32 total = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setTotal($var)
    {
        GPBUtil::checkUint32($var);
        $this->total = $var;

        return $this;
    }

}

