<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 获取pin列表请求 /userRead/pinList
 *
 * Generated from protobuf message <code>PBPinListReq</code>
 */
class PBPinListReq extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.PBPinType type = 1;</code>
     */
    protected $type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.PBPinType type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>.PBPinType type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\CRMCommon\PBPinType::class);
        $this->type = $var;

        return $this;
    }

}

