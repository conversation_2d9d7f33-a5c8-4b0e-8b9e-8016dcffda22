<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBReferFileInfo</code>
 */
class PBReferFileInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.PBFileInfo file_info = 1;</code>
     */
    protected $file_info = null;
    /**
     * Generated from protobuf field <code>.PBType refer_type = 2;</code>
     */
    protected $refer_type = 0;
    /**
     * Generated from protobuf field <code>uint64 refer_file_id = 3;</code>
     */
    protected $refer_file_id = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \protobuf\CRMCommon\PBFileInfo $file_info
     *     @type int $refer_type
     *     @type int|string $refer_file_id
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.PBFileInfo file_info = 1;</code>
     * @return \protobuf\CRMCommon\PBFileInfo|null
     */
    public function getFileInfo()
    {
        return $this->file_info;
    }

    public function hasFileInfo()
    {
        return isset($this->file_info);
    }

    public function clearFileInfo()
    {
        unset($this->file_info);
    }

    /**
     * Generated from protobuf field <code>.PBFileInfo file_info = 1;</code>
     * @param \protobuf\CRMCommon\PBFileInfo $var
     * @return $this
     */
    public function setFileInfo($var)
    {
        GPBUtil::checkMessage($var, \protobuf\CRMCommon\PBFileInfo::class);
        $this->file_info = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.PBType refer_type = 2;</code>
     * @return int
     */
    public function getReferType()
    {
        return $this->refer_type;
    }

    /**
     * Generated from protobuf field <code>.PBType refer_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setReferType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\CRMCommon\PBType::class);
        $this->refer_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 refer_file_id = 3;</code>
     * @return int|string
     */
    public function getReferFileId()
    {
        return $this->refer_file_id;
    }

    /**
     * Generated from protobuf field <code>uint64 refer_file_id = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setReferFileId($var)
    {
        GPBUtil::checkUint64($var);
        $this->refer_file_id = $var;

        return $this;
    }

}

