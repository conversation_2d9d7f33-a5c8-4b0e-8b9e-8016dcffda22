<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: CRMCommon.proto

namespace protobuf\CRMCommon;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * 获取客户/线索/商机搜索自定义字段设置 /stormsFury/customerRead/searchSetting
 *
 * Generated from protobuf message <code>PBSearchSettingReq</code>
 */
class PBSearchSettingReq extends \Google\Protobuf\Internal\Message
{
    /**
     * 客户:4 线索:7 商机:9
     *
     * Generated from protobuf field <code>uint32 type = 1;</code>
     */
    protected $type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $type
     *           客户:4 线索:7 商机:9
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\CRMCommon::initOnce();
        parent::__construct($data);
    }

    /**
     * 客户:4 线索:7 商机:9
     *
     * Generated from protobuf field <code>uint32 type = 1;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * 客户:4 线索:7 商机:9
     *
     * Generated from protobuf field <code>uint32 type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkUint32($var);
        $this->type = $var;

        return $this;
    }

}

