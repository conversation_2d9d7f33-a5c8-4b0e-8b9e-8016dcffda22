<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBFormFieldCustomerInfo</code>
 */
class PBFormFieldCustomerInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     */
    protected $company_id = 0;
    /**
     * Generated from protobuf field <code>uint64 customer_id = 2;</code>
     */
    protected $customer_id = 0;
    /**
     * Generated from protobuf field <code>string email = 3;</code>
     */
    protected $email = '';
    /**
     * Generated from protobuf field <code>bool main_customer_flag = 4;</code>
     */
    protected $main_customer_flag = false;
    /**
     * Generated from protobuf field <code>string name = 5;</code>
     */
    protected $name = '';
    /**
     * Generated from protobuf field <code>repeated .PBFieldItem fields = 6;</code>
     */
    private $fields;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $company_id
     *     @type int|string $customer_id
     *     @type string $email
     *     @type bool $main_customer_flag
     *     @type string $name
     *     @type array<\protobuf\CRMCommon\PBFieldItem>|\Google\Protobuf\Internal\RepeatedField $fields
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @return int|string
     */
    public function getCompanyId()
    {
        return $this->company_id;
    }

    /**
     * Generated from protobuf field <code>uint64 company_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCompanyId($var)
    {
        GPBUtil::checkUint64($var);
        $this->company_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint64 customer_id = 2;</code>
     * @return int|string
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * Generated from protobuf field <code>uint64 customer_id = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCustomerId($var)
    {
        GPBUtil::checkUint64($var);
        $this->customer_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string email = 3;</code>
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Generated from protobuf field <code>string email = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEmail($var)
    {
        GPBUtil::checkString($var, True);
        $this->email = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>bool main_customer_flag = 4;</code>
     * @return bool
     */
    public function getMainCustomerFlag()
    {
        return $this->main_customer_flag;
    }

    /**
     * Generated from protobuf field <code>bool main_customer_flag = 4;</code>
     * @param bool $var
     * @return $this
     */
    public function setMainCustomerFlag($var)
    {
        GPBUtil::checkBool($var);
        $this->main_customer_flag = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 5;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFieldItem fields = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFields()
    {
        return $this->fields;
    }

    /**
     * Generated from protobuf field <code>repeated .PBFieldItem fields = 6;</code>
     * @param array<\protobuf\CRMCommon\PBFieldItem>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFields($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \protobuf\CRMCommon\PBFieldItem::class);
        $this->fields = $arr;

        return $this;
    }

}

