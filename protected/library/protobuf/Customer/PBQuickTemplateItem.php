<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Customer.proto

namespace protobuf\Customer;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PBQuickTemplateItem</code>
 */
class PBQuickTemplateItem extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.PBQuickTextItem text = 1;</code>
     */
    protected $text = null;
    /**
     * 模版名称
     *
     * Generated from protobuf field <code>string item_name = 2;</code>
     */
    protected $item_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \protobuf\Customer\PBQuickTextItem $text
     *     @type string $item_name
     *           模版名称
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Customer::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.PBQuickTextItem text = 1;</code>
     * @return \protobuf\Customer\PBQuickTextItem|null
     */
    public function getText()
    {
        return $this->text;
    }

    public function hasText()
    {
        return isset($this->text);
    }

    public function clearText()
    {
        unset($this->text);
    }

    /**
     * Generated from protobuf field <code>.PBQuickTextItem text = 1;</code>
     * @param \protobuf\Customer\PBQuickTextItem $var
     * @return $this
     */
    public function setText($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Customer\PBQuickTextItem::class);
        $this->text = $var;

        return $this;
    }

    /**
     * 模版名称
     *
     * Generated from protobuf field <code>string item_name = 2;</code>
     * @return string
     */
    public function getItemName()
    {
        return $this->item_name;
    }

    /**
     * 模版名称
     *
     * Generated from protobuf field <code>string item_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setItemName($var)
    {
        GPBUtil::checkString($var, True);
        $this->item_name = $var;

        return $this;
    }

}

