<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: report.proto

namespace protobuf\Report;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *(成交)订单构成 客户分析 国家地区&&来源 /stormsFury/reportRead/orderCompanyAnalysis
 *
 * Generated from protobuf message <code>PBKanBanOrderCompanyAnalysisReq</code>
 */
class PBKanBanOrderCompanyAnalysisReq extends \Google\Protobuf\Internal\Message
{
    /**
     * user_id,后端会鉴权过滤 现在可以支持多选user
     *
     * Generated from protobuf field <code>repeated uint64 user_ids = 1;</code>
     */
    private $user_ids;
    /**
     * 部门 支持多部门
     *
     * Generated from protobuf field <code>uint32 dep_id = 2;</code>
     */
    protected $dep_id = 0;
    /**
     * 开始时间
     *
     * Generated from protobuf field <code>string start_date = 3;</code>
     */
    protected $start_date = '';
    /**
     * 结束时间
     *
     * Generated from protobuf field <code>string end_date = 4;</code>
     */
    protected $end_date = '';
    /**
     * 区分是否成交客户 传2是成交客户 不传,0,1是订单客户
     *
     * Generated from protobuf field <code>.PBOrderDealType order_deal_type = 5;</code>
     */
    protected $order_deal_type = 0;
    /**
     * 查看视角 国家地区||来源 默认国家地区
     *
     * Generated from protobuf field <code>.PBKanBanOrderCompanyAnalysisViewEnum view = 6;</code>
     */
    protected $view = 0;
    /**
     * 查看视角为国家地区特有字段-区域粒度 1洲/2地区/3国家
     *
     * Generated from protobuf field <code>.PBKanBanRegionalScopeEnum regional_scope = 7;</code>
     */
    protected $regional_scope = 0;
    /**
     * 客户的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanCompanySearchParam company_search = 8;</code>
     */
    protected $company_search = null;
    /**
     * 订单的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanOrderSearchParam order_search = 9;</code>
     */
    protected $order_search = null;
    /**
     * 排序字段
     *
     * Generated from protobuf field <code>string sort_field = 10;</code>
     */
    protected $sort_field = '';
    /**
     * 排序方式
     *
     * Generated from protobuf field <code>.PBSortType sort_type = 11;</code>
     */
    protected $sort_type = 0;
    /**
     * Generated from protobuf field <code>uint32 cur_page = 12;</code>
     */
    protected $cur_page = 0;
    /**
     * Generated from protobuf field <code>uint32 page_size = 13;</code>
     */
    protected $page_size = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $user_ids
     *           user_id,后端会鉴权过滤 现在可以支持多选user
     *     @type int $dep_id
     *           部门 支持多部门
     *     @type string $start_date
     *           开始时间
     *     @type string $end_date
     *           结束时间
     *     @type int $order_deal_type
     *           区分是否成交客户 传2是成交客户 不传,0,1是订单客户
     *     @type int $view
     *           查看视角 国家地区||来源 默认国家地区
     *     @type int $regional_scope
     *           查看视角为国家地区特有字段-区域粒度 1洲/2地区/3国家
     *     @type \protobuf\Report\PBKanBanCompanySearchParam $company_search
     *           客户的筛选参数
     *     @type \protobuf\Report\PBKanBanOrderSearchParam $order_search
     *           订单的筛选参数
     *     @type string $sort_field
     *           排序字段
     *     @type int $sort_type
     *           排序方式
     *     @type int $cur_page
     *     @type int $page_size
     * }
     */
    public function __construct($data = NULL) {
        \protobuf\GPBMetadata\Report::initOnce();
        parent::__construct($data);
    }

    /**
     * user_id,后端会鉴权过滤 现在可以支持多选user
     *
     * Generated from protobuf field <code>repeated uint64 user_ids = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getUserIds()
    {
        return $this->user_ids;
    }

    /**
     * user_id,后端会鉴权过滤 现在可以支持多选user
     *
     * Generated from protobuf field <code>repeated uint64 user_ids = 1;</code>
     * @param array<int>|array<string>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setUserIds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::UINT64);
        $this->user_ids = $arr;

        return $this;
    }

    /**
     * 部门 支持多部门
     *
     * Generated from protobuf field <code>uint32 dep_id = 2;</code>
     * @return int
     */
    public function getDepId()
    {
        return $this->dep_id;
    }

    /**
     * 部门 支持多部门
     *
     * Generated from protobuf field <code>uint32 dep_id = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setDepId($var)
    {
        GPBUtil::checkUint32($var);
        $this->dep_id = $var;

        return $this;
    }

    /**
     * 开始时间
     *
     * Generated from protobuf field <code>string start_date = 3;</code>
     * @return string
     */
    public function getStartDate()
    {
        return $this->start_date;
    }

    /**
     * 开始时间
     *
     * Generated from protobuf field <code>string start_date = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setStartDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->start_date = $var;

        return $this;
    }

    /**
     * 结束时间
     *
     * Generated from protobuf field <code>string end_date = 4;</code>
     * @return string
     */
    public function getEndDate()
    {
        return $this->end_date;
    }

    /**
     * 结束时间
     *
     * Generated from protobuf field <code>string end_date = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setEndDate($var)
    {
        GPBUtil::checkString($var, True);
        $this->end_date = $var;

        return $this;
    }

    /**
     * 区分是否成交客户 传2是成交客户 不传,0,1是订单客户
     *
     * Generated from protobuf field <code>.PBOrderDealType order_deal_type = 5;</code>
     * @return int
     */
    public function getOrderDealType()
    {
        return $this->order_deal_type;
    }

    /**
     * 区分是否成交客户 传2是成交客户 不传,0,1是订单客户
     *
     * Generated from protobuf field <code>.PBOrderDealType order_deal_type = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setOrderDealType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Invoices\PBOrderDealType::class);
        $this->order_deal_type = $var;

        return $this;
    }

    /**
     * 查看视角 国家地区||来源 默认国家地区
     *
     * Generated from protobuf field <code>.PBKanBanOrderCompanyAnalysisViewEnum view = 6;</code>
     * @return int
     */
    public function getView()
    {
        return $this->view;
    }

    /**
     * 查看视角 国家地区||来源 默认国家地区
     *
     * Generated from protobuf field <code>.PBKanBanOrderCompanyAnalysisViewEnum view = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setView($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Report\PBKanBanOrderCompanyAnalysisViewEnum::class);
        $this->view = $var;

        return $this;
    }

    /**
     * 查看视角为国家地区特有字段-区域粒度 1洲/2地区/3国家
     *
     * Generated from protobuf field <code>.PBKanBanRegionalScopeEnum regional_scope = 7;</code>
     * @return int
     */
    public function getRegionalScope()
    {
        return $this->regional_scope;
    }

    /**
     * 查看视角为国家地区特有字段-区域粒度 1洲/2地区/3国家
     *
     * Generated from protobuf field <code>.PBKanBanRegionalScopeEnum regional_scope = 7;</code>
     * @param int $var
     * @return $this
     */
    public function setRegionalScope($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Report\PBKanBanRegionalScopeEnum::class);
        $this->regional_scope = $var;

        return $this;
    }

    /**
     * 客户的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanCompanySearchParam company_search = 8;</code>
     * @return \protobuf\Report\PBKanBanCompanySearchParam|null
     */
    public function getCompanySearch()
    {
        return $this->company_search;
    }

    public function hasCompanySearch()
    {
        return isset($this->company_search);
    }

    public function clearCompanySearch()
    {
        unset($this->company_search);
    }

    /**
     * 客户的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanCompanySearchParam company_search = 8;</code>
     * @param \protobuf\Report\PBKanBanCompanySearchParam $var
     * @return $this
     */
    public function setCompanySearch($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Report\PBKanBanCompanySearchParam::class);
        $this->company_search = $var;

        return $this;
    }

    /**
     * 订单的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanOrderSearchParam order_search = 9;</code>
     * @return \protobuf\Report\PBKanBanOrderSearchParam|null
     */
    public function getOrderSearch()
    {
        return $this->order_search;
    }

    public function hasOrderSearch()
    {
        return isset($this->order_search);
    }

    public function clearOrderSearch()
    {
        unset($this->order_search);
    }

    /**
     * 订单的筛选参数
     *
     * Generated from protobuf field <code>.PBKanBanOrderSearchParam order_search = 9;</code>
     * @param \protobuf\Report\PBKanBanOrderSearchParam $var
     * @return $this
     */
    public function setOrderSearch($var)
    {
        GPBUtil::checkMessage($var, \protobuf\Report\PBKanBanOrderSearchParam::class);
        $this->order_search = $var;

        return $this;
    }

    /**
     * 排序字段
     *
     * Generated from protobuf field <code>string sort_field = 10;</code>
     * @return string
     */
    public function getSortField()
    {
        return $this->sort_field;
    }

    /**
     * 排序字段
     *
     * Generated from protobuf field <code>string sort_field = 10;</code>
     * @param string $var
     * @return $this
     */
    public function setSortField($var)
    {
        GPBUtil::checkString($var, True);
        $this->sort_field = $var;

        return $this;
    }

    /**
     * 排序方式
     *
     * Generated from protobuf field <code>.PBSortType sort_type = 11;</code>
     * @return int
     */
    public function getSortType()
    {
        return $this->sort_type;
    }

    /**
     * 排序方式
     *
     * Generated from protobuf field <code>.PBSortType sort_type = 11;</code>
     * @param int $var
     * @return $this
     */
    public function setSortType($var)
    {
        GPBUtil::checkEnum($var, \protobuf\Report\PBSortType::class);
        $this->sort_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 cur_page = 12;</code>
     * @return int
     */
    public function getCurPage()
    {
        return $this->cur_page;
    }

    /**
     * Generated from protobuf field <code>uint32 cur_page = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setCurPage($var)
    {
        GPBUtil::checkUint32($var);
        $this->cur_page = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 page_size = 13;</code>
     * @return int
     */
    public function getPageSize()
    {
        return $this->page_size;
    }

    /**
     * Generated from protobuf field <code>uint32 page_size = 13;</code>
     * @param int $var
     * @return $this
     */
    public function setPageSize($var)
    {
        GPBUtil::checkUint32($var);
        $this->page_size = $var;

        return $this;
    }

}

