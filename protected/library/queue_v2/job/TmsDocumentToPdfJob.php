<?php

namespace common\library\queue_v2\job;

use common\library\annotation\ClaimUser;
use common\library\html_screenshot\HtmlScreenshotService;
use common\library\queue_v2\QueueConstant;
use common\library\trade_document\page\PageAPI;
use common\library\trade_document\page\user\Page;


#[ClaimUser('<EMAIL>')]
class TmsDocumentToPdfJob extends BaseJob
{
    public $channel = QueueConstant::CONNECTION_NAME_DEFAULT;
    public $tag = QueueConstant::CONSUMER_TAG_TMS_DOCUMENT;

    public $data;

    public function __construct(array $data)
    {
        $this->data = [
            'scene' => 'tms_document',
            'url' => $data['url'],
            'pageId' => $data['pageId'],
            'clientId' => $data['clientId'],
            'userId' => $data['userId'],
            'filename' => $data['filename']
        ];
    }


    public function handle(): void
    {
        $message = $this->data;
        \LogUtil::info("process tms document to pdf: ".print_r($message, true));
        $pageId = $message['pageId'];
        $page = new Page($message['clientId']);
        $page->loadByPageId($pageId);
        if ($page->isNew()) {
            \LogUtil::info("page not exists ".$pageId);
            return;
        }

        $screenshot = new HtmlScreenshotService();
        $screenshot->setUserId($message['userId']);
        $screenshot->setClientId($message['clientId']);
        $screenshot->setTraceData([
            'client_id' => $message['clientId'],
            'page_id' => $pageId
        ]);
        $url = $message['url'];
        $fallbackUrls = $screenshot->makeFallbackUrls($url);
        $result = $screenshot->html2pdf($url, $message['scene'], $message['filename'] ?? '', $fallbackUrls);

        // 更新文档
        $page->pdf_file_id = $result['file_id'];
        $page->pdf_file_url = $result['url'];
        \LogUtil::info("document to pdf finish page_id: $pageId, url: {$result['url']}");
        $page->update(['pdf_file_id', 'pdf_file_url']);

        // 清除缓存
        PageAPI::deleteRemotePageCache($page->page_id);
    }
}