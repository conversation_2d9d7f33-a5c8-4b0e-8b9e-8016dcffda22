<?php


namespace common\library\queue_v2\job;


use common\components\BaseObject;
use common\library\account\Client;
use common\library\annotation\ClaimUser;
use common\library\cash_collection\CashCollectionList;
use common\library\invoice\batch\OrderBatchOperator;
use common\library\performance_v2\Helper;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\rule\PerformanceV2RuleList;
use common\library\queue_v2\QueueConstant;
use common\library\report\error\ErrorReport;
use common\library\performance_v2\record\PerformanceV2RecordList;
use common\library\privilege_v3\PrivilegeService;
use common\library\performance_v2\filter\RuleConfig;
use common\library\workflow\filter\WorkflowFilterRunner;
use common\models\client\PerformanceV2Rule;
use PerformanceV2Record;
use PgActiveRecord;



#[ClaimUser('<EMAIL>')]
class PerformanceV2RecordJob extends BaseJob
{
    public $clientId;
    public $baseType;
    public $objectIds;
    public $channel = QueueConstant::CONNECTION_NAME_DEFAULT;
    public $maxExceptions = 2;
    public $tag = QueueConstant::CONSUMER_PRIORITY_LOW;
    public $oldAttributes = [];
    public $updateFields = [];
    public $scene = '';

    public $referTypeMap = [
        \Constants::TYPE_COMPANY => [
            'primary_key' => 'company_id',
            'table_name' => 'tbl_company'
        ],
        \Constants::TYPE_OPPORTUNITY => [
            'primary_key' => 'opportunity_id',
            'table_name' => 'tbl_opportunity'
        ],
        \Constants::TYPE_QUOTATION => [
            'primary_key' => 'quotation_id',
            'table_name' => 'tbl_quotation'
        ],
        \Constants::TYPE_ORDER => [
            'primary_key' => 'order_id',
            'table_name' => 'tbl_order'
        ],
        \Constants::TYPE_CASH_COLLECTION => [
            'primary_key' => 'cash_collection_id',
            'table_name' => 'tbl_cash_collection'
        ],
        \Constants::TYPE_FOLLOWUP => [
            'primary_key' => 'follow_up_id',
            'table_name' => 'tbl_follow_up'
        ],
        \Constants::TYPE_LEAD => [
            'primary_key' => 'lead_id',
            'table_name' => 'tbl_lead'
        ],
        \Constants::TYPE_SALE_OUTBOUND_INVOICE => [
            'primary_key' => 'outbound_invoice_id',
            'table_name' => 'tbl_outbound_invoice'
        ],
    ];

    public function __construct($clientId, $baseType, $objectIds, $oldAttributes = [], $updateFields = [],$scene = '')
    {
        $this->clientId = $clientId;
        $this->baseType = $baseType;

        if (!is_array($objectIds)) {
            $objectIds = [$objectIds];
        }
        $objectIds = array_values(array_unique($objectIds));
        $this->objectIds = $objectIds;
        $this->oldAttributes = $oldAttributes;
        $this->updateFields = $updateFields;
        $this->scene = $scene;
    }


    public function handle()
    {

        if (empty($this->clientId) || empty($this->baseType) || empty($this->objectIds)) {
            return;
        }

        // 临时处理方式delete锁住
        if ($this->clientId == 62263 && $this->objectIds == [778981736691])
        {
            return;
        }

        // 超级管理员
        $loginUser = \User::getLoginUser();
        if (!($loginUser && $loginUser->getClientId() == $this->clientId)) {
            $adminUserId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
            if (!$adminUserId) {
                return;
            }
            \User::setLoginUserById($adminUserId);
        }

        $db = \PgActiveRecord::getDbByClientId($this->clientId);
        if (empty($db)) {
            return;
        }

        //获取满足条件的baseType以及referType rule 以及相对应的objectIds
        list($baseRuleReferIdMap, $ruleReferIdMap) = $this->getRuleAndReferIds($db);


        if (empty($baseRuleReferIdMap) && empty($ruleReferIdMap)) {
            return;
        }

        if (!empty($baseRuleReferIdMap)) {

            array_multisort(array_column($baseRuleReferIdMap, 'type'), SORT_DESC, $baseRuleReferIdMap);

            //先执行baseType 有些绩效规则依赖于预设的绩效规则，比如客群的成交订单金额相关的字段依赖成交订单金额，
            //新建客户数的绩效规则则依赖于先执行成交订单金额执行触发客群字段才能执行
            foreach ($baseRuleReferIdMap as $baseRuleItem) {
                $this->recordPerformanceByRuleInfoAndReferIds($baseRuleItem['rule'], $baseRuleItem['referIds'], $db);
            }
        }


        if (!empty($ruleReferIdMap)) {
            //执行当前对象变更，配置了依赖当前对象变更的绩效规则，比如客户变更，影响到配置了订单-客户字段=xxx的成交订单金额字段
            //refer执行，优先执行预设规则的绩效规则 按照type来排序 普通规则type=0
            array_multisort(array_column($ruleReferIdMap, 'type'), SORT_DESC, $ruleReferIdMap);

            foreach ($ruleReferIdMap as $ruleItem) {
                $this->recordPerformanceByRuleInfoAndReferIds($ruleItem['rule'], $ruleItem['referIds'], $db);
            }
        }

    }

    private function getRules($clientId, $referTypes): array
    {
        if (empty($clientId) || empty($referTypes)) {
            return [];
        }

        $referTypes = array_unique($referTypes);
        $list = new PerformanceV2RuleList($clientId, null);
        $list->setReferType($referTypes);
        $list->setEnableFlag(PerformanceV2Constant::ENABLE_FLAG_TRUE);
        $list->setDeleteFlag(PerformanceV2Constant::DELETE_FLAG_FALSE);
        $list->setLimit(10000);
        return $list->find();
    }

    private function getRuleAndReferIds($db): array
    {
        $baseRuleReferIdMap = [];
        $ruleReferIdMap = [];
        $referPrimaryField = $this->referTypeMap[$this->baseType]['primary_key'] ?? '';

        $rules = $this->getRules($this->clientId, array_merge(array_keys($this->referTypeMap), [$this->baseType]));

        if (empty($rules)) {
            return [[],[]];
        }

        foreach ($rules as $rule) {
            $currentReferType = $rule['refer_type'];
            $filters = $rule['filters'];
            $hasReferFlag = false;
            $timeField = $rule['time_field'];

            // 邮件绩效在停用规则时仍然记录绩效，但是其他规则不允许
            if ($currentReferType != \Constants::TYPE_MAIL && $rule['enable_flag'] == PerformanceV2Constant::ENABLE_FLAG_FALSE) continue;


            if ($this->checkNeedSkipPerformanceRuleByUpdateField($currentReferType,$timeField)) continue;

            if ($this->checkNeedSkipPerformanceRuleByScene($currentReferType,$timeField)) continue;


            //baseType和objectIds
            if ($currentReferType == $this->baseType) {
                $baseRuleReferIdMap[] = [
                    'rule' => $rule,
                    'referIds' => $this->objectIds,
                    'type' => $rule['type'] ?? 0
                ];
                continue;
            }


            //寻找referType和referIds
            $referIds = [];
            foreach ($filters as $workflowFilter) {

                //规则配置了依赖字段
                if ($workflowFilter['refer_type'] == $this->baseType) {

                    $hasReferFlag = true;
                    $primaryField = $this->referTypeMap[$currentReferType]['primary_key'] ?? '';
                    $tableName = $this->referTypeMap[$currentReferType]['table_name'] ?? '';

                    if (empty($primaryField) || empty($referPrimaryField) || empty($tableName)) {
                        continue;
                    }

                    $referIdStr = implode(',', $this->objectIds);
                    $sql = "select $primaryField from $tableName where client_id=$this->clientId and $referPrimaryField  in ({$referIdStr}) and enable_flag = 1 ";

                    if ($currentReferType == \Constants::TYPE_COMPANY && ($workflowFilter['refer_type'] ?? 0) == \Constants::TYPE_OPPORTUNITY  && ($workflowFilter['field'] ?? '') == 'stage')
                    {
                        $referPrimaryField = "company_id";

                        $referIdStr = "select company_id from tbl_opportunity where opportunity_id in ($referIdStr)";
                        $sql = "select $primaryField from $tableName where client_id=$this->clientId and $referPrimaryField  in ({$referIdStr}) and is_archive = 1 ";
                    }

                    if ($currentReferType == \Constants::TYPE_SALE_OUTBOUND_INVOICE && $this->baseType == \Constants::TYPE_ORDER) {
                        $referIdStr = "select $primaryField from tbl_outbound_record where $referPrimaryField in ($referIdStr) and delete_flag = 0 ";
                        $sql = "select $primaryField from $tableName where client_id=$this->clientId and $primaryField  in ({$referIdStr}) and delete_flag = 0 ";
                    }

                    try {
                        $referIds = $db->createCommand($sql)->queryColumn();
                    } catch (\Exception $e) {
                        ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                        \LogUtil::error(sprintf("绩效计算查询关联对象报错 clientId[{$rule['client_id']}] ruleId[{$rule['rule_id']}] sql[{$sql}] referPrimaryField[{$referPrimaryField}] errorMsg[{$e->getMessage()}]"));
                        continue;
                    }
                    //找到有依赖于当前绩效对象的绩效规则则推出
                    break;
                }
            }


            //客户绩效配置了依赖成交订单绩效规则字段
            if ($this->baseType == \Constants::TYPE_ORDER && $currentReferType == \Constants::TYPE_COMPANY && in_array($timeField,[
                    'deal_time','latest_transaction_order_time',
                    'latest_success_opportunity_time','success_opportunity_first_time','every_transaction_order_time', 'transaction_order_first_time'
                ]))
            {
                $referIdStr = implode(',', $this->objectIds);

                if (!empty($referIdStr)) {
                    $sql = "select company_id from tbl_order where client_id=$this->clientId and order_id  in ({$referIdStr}) and enable_flag = 1 ";
                    $referIds = $db->createCommand($sql)->queryColumn();
                    $hasReferFlag = true;
                }
                //找到有依赖于当前绩效对象的绩效规则则推出
            }

            //销售出库单绩效配置了依赖销售订单规则字段
            if ($this->baseType == \Constants::TYPE_ORDER && $currentReferType == \Constants::TYPE_SALE_OUTBOUND_INVOICE) {
                $referIdStr = implode(',', $this->objectIds);
                if (!empty($referIdStr)) {
                    $sql = "SELECT distinct(outbound_invoice_id) FROM tbl_outbound_record WHERE client_id={$this->clientId} AND delete_flag=0 AND order_id IN ({$referIdStr})";
                    $referIds = $db->createCommand($sql)->queryColumn();
                    $hasReferFlag = true;
                }
            }

            if ($hasReferFlag && !empty($rule) && !empty($referIds)) {
                $ruleReferIdMap[] = [
                    'rule' => $rule,
                    'referIds' => $referIds,
                    'type' => $rule['type'] ?? 0
                ];
            }
        }

        // 假如是订单的话，需要找到回款单关联了 performance_order_count 的字段的回款单规则重跑一下绩效
        if ($this->baseType == \Constants::TYPE_ORDER)
        {
            // 找到存在 performance_order_count 字段的回款单规则ID 进行重新运算
            $performanceRulePdo = new PerformanceV2RuleList($this->clientId);
            $performanceRulePdo->setReferType(\Constants::TYPE_CASH_COLLECTION);
            $performanceRulePdo->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
            $cashRules = $performanceRulePdo->find();

            $cashIds = [];
            foreach ($cashRules as $cashRule)
            {
                $filters = $cashRule['filters'];
                $filterFields = array_column($filters, 'field');

                if (in_array('performance_order_count', $filterFields))
                {
                    // 找到 order 关联的回款单Id
                    if (empty($cashIds))
                    {
                        $cashPdo = new CashCollectionList($this->clientId);
                        $cashPdo->setFields(['cash_collection_id']);
                        $cashPdo->setOrderId($this->objectIds);
                        $cashPdo->setEnableFlag(BaseObject::ENABLE_FLAG_TRUE);
                        $cashPdo->setSkipPermission(true);
                        $cashList = $cashPdo->find();
                        $cashIds = array_column($cashList, 'cash_collection_id');
                    }

                    $ruleReferIdMap[] = [
                        'rule' => $cashRule,
                        'referIds' => $cashIds,
                        'type' => $cashRule['type'] ?? 0
                    ];
                }
            }
        }

        return [$baseRuleReferIdMap, $ruleReferIdMap];
    }

    private function recordPerformanceByRuleInfoAndReferIds(array $ruleInfo, array $referIds, $db): bool
    {
        if (empty($ruleInfo) || empty($referIds)) {
            return false;
        }

        $clientId = $ruleInfo['client_id'];
        $referType = $ruleInfo['refer_type'];
        $ruleId = $ruleInfo['rule_id'];
        $timeField = $ruleInfo['time_field'];


        if (empty($clientId) || empty($referType) || empty($ruleId)) {
            return false;
        }

        $referIds = $this->getReferIds($ruleInfo,$referIds,$db);

        if ($this->checkNeedDeleteRecordByRuleInfo($ruleInfo)) {
            if (in_array($this->clientId, [56574, 19744])) {
                \LogUtil::info(sprintf(__FUNCTION__ . "删除业绩数据[%s] - clientId[{$clientId}] ruleId[{$ruleId}] referId[%s]", microtime(true), json_encode($referIds)));
            }
            $recordList = new PerformanceV2RecordList($clientId);
            $recordList->setReferType($referType);
            $recordList->setRuleId($ruleId);
            $recordList->setReferId($referIds);
            $recordList->delete();
        }

        $ruleConfig = new RuleConfig($clientId, $referType, $referIds, $ruleInfo);
        $filterRunner = new WorkflowFilterRunner($ruleConfig);
        $filterRunner->setFilters($ruleInfo['filters'], $ruleInfo['criteria']);
        foreach ($ruleConfig->getExtraFilters() ?? [] as $filter) {
            $filterRunner->addExtraFilters($filter);
        }
        $targetFieldFilters = $ruleConfig->getExtraFiltersByTargetField();
        $filterRunner->addExtraWorkflowFilters($targetFieldFilters['filters'], $targetFieldFilters['criteria'], $targetFieldFilters['operator']);
        $filterRunner->setReferIds($referIds);
        $filterRunner->setDebug(0);
        $filterRunner->setFields('*');
        $filterRunner->setUpdateFields($this->updateFields);
        $specialKeys = PerformanceV2Constant::REFER_SPECIAL_KEYS_MAP[$referType] ?? [];

        $isMatchFlag = false;
        //需要更新的客群字段
        $customerGroupCompanyIds = [];
        //匹配的销售订单
        $matchOrderIds = [];
        // 主币种
        $mainCurrency = Client::getClient($clientId)->getMainCurrency();

        // 特殊字段在这里加
        $extraData = [
            'mainCurrency' => $mainCurrency,
            'specialKeys' => $specialKeys,
            // 公海用户原跟进人在表里只存了一个，但是实际上有多个 绩效应该记录真正的跟进人 因此在外面埋点了，这里特殊处理下.
            'lastOwnerMap' => $this->oldAttributes['lastOwnerMap'] ?? [],
            // 这个是为了解决工单,绩效delete后原本company数据没办法回溯的问题 具体可以找wuyiwai 或者 raylei 问问 如果都没有 那么就是在order.php afersave里面做的埋点
            // 【【工单#20230714000057】客户详情页成交订单数显示异常：订单关联错客户，重新关联了客户后，原来关联的这个客户还会显示成交订单数】https://www.tapd.cn/21404721/bugtrace/bugs/view?bug_id=1121404721001087668
            'changedCompanyIds' => $this->updateFields['changedCompanyIds'] ?? []
        ];

        foreach ($filterRunner->iterate(1000) as $items) {
            try {
                $items = Helper::processReferListByRuleInfo($items,$ruleInfo);
                // 获取业绩计算结果
                list($userPerformanceRecords, $departmentPerformanceRecords) = Helper::getBatchRecordPerformances($referType, $items, $ruleInfo, $extraData);

                $allPerformanceRecords = array_merge($userPerformanceRecords, $departmentPerformanceRecords);

                if (!empty($allPerformanceRecords)) {

                    $values = [];
                    $count = count($allPerformanceRecords);
                    // 获取record_id
                    $recordId = PgActiveRecord::produceAutoIncrementId($count);
                    foreach ($allPerformanceRecords as $item) {
                        if ($item['account_date'] == '1970-01-01 00:00:00' || $item['account_date'] == '1970-01-01') {
                            $timeField = Helper::transferTimeField($referType, $timeField);
                            if (in_array($timeField, ['latest_transaction_order_time', 'transaction_order_first_time',
                                'deal_time', 'latest_success_opportunity_time','first_collection_date']))
                            {
                                \LogUtil::info(sprintf("记录无效绩效 --clientId=[{$item['client_id']}] --referId=[{$item['refer_id']}] --ruleId=[{$item['rule_id']}] --companyId=[%d]", $item['company_id'] ?? 0));
                            }
                            continue;
                        }
                        $userRecord = [];
                        $userRecord['client_id'] = $item['client_id'];
                        $userRecord['refer_id'] = $item['refer_id'];
                        $userRecord['owner_id'] = $item['owner_id'] ?? 0;
                        $userRecord['rule_id'] = $item['rule_id'];
                        $userRecord['refer_type'] = $item['refer_type'] ?? 0;
                        $userRecord['record_type'] = $item['record_type'] ?? 0;
                        $userRecord['indicator_type'] = \Util::escapeDoubleQuoteSql($item['indicator_type']);
                        $userRecord['indicator_value'] = $item['indicator_value'] ?? 0;
                        $userRecord['account_date'] = \Util::escapeDoubleQuoteSql($item['account_date']);
                        $userRecord['create_time'] = \Util::escapeDoubleQuoteSql($item['create_time']);
                        $userRecord['update_time'] = \Util::escapeDoubleQuoteSql($item['update_time']);
                        $userRecord['company_id'] = $item['company_id'] ?? 0;
                        $values[] = "({$recordId}, {$userRecord['client_id']}, {$userRecord['refer_id']}, {$userRecord['owner_id']}, {$userRecord['rule_id']}, {$userRecord['refer_type']}, {$userRecord['record_type']}, '{$userRecord['indicator_type']}', {$userRecord['indicator_value']}, '{$userRecord['account_date']}', '{$userRecord['create_time']}', '{$userRecord['update_time']}', {$userRecord['company_id']})";
                        $recordId --;

                        //满足预设的成交订单金额绩效规则
                        //满足条件更新客群的数据

                        //成交订单金额规则的判断条件
                        $defaultOrderRuleFlag = $item['record_type'] == PerformanceV2Constant::RECORD_TYPE_USER && $ruleInfo['type'] == 2
                            && $ruleInfo['refer_type'] == \Constants::TYPE_ORDER && !empty($item['company_id']);

                        if ($defaultOrderRuleFlag) {
                            $matchOrderIds[] = $item['refer_id'];
                            $customerGroupCompanyIds[] = $item['company_id'];
                        }
                    }

                    if (!empty($values)) {
                        // 批量入库 若存在则更新value
                        $commonInsertPreSql = "insert into tbl_performance_v2_record (record_id,client_id, refer_id, owner_id, rule_id, refer_type, record_type, indicator_type, indicator_value, account_date, create_time, update_time, company_id) VALUES ";
                        $commonInsertEndSql = " ON CONFLICT (client_id, rule_id, owner_id, refer_id,account_date) DO UPDATE SET indicator_type = EXCLUDED.indicator_type, account_date = EXCLUDED.account_date, update_time = EXCLUDED.update_time, indicator_value = EXCLUDED.indicator_value, owner_id = EXCLUDED.owner_id, company_id = EXCLUDED.company_id";
                        $insertSql = $commonInsertPreSql . implode(",", $values) . $commonInsertEndSql;
                        $affectRows = $db->getPdoInstance()->prepare($insertSql)->execute();
                        $isMatchFlag = true;

                        if (in_array($this->clientId, [56574, 19744])) {
                            \LogUtil::info(sprintf(__FUNCTION__ . "记录业绩数据[%s] - clientId[{$clientId}] ruleId[{$ruleId}] referId[%s] insertSql[{$insertSql}] affectRows[{$affectRows}]", microtime(true), json_encode($referIds)));
                        }
                    }
                }

            } catch (\PDOException $pdoException) {
                if (preg_match("/Deadlock detected/i", $pdoException->getMessage())) {
                    if (!empty($values)) {
                        // 批量入库 若存在则更新value
                        $commonInsertPreSql = "insert into tbl_performance_v2_record (record_id,client_id, refer_id, owner_id, rule_id, refer_type, record_type, indicator_type, indicator_value, account_date, create_time, update_time, company_id) VALUES ";
                        $commonInsertEndSql = " ON CONFLICT (client_id, rule_id, owner_id, refer_id,account_date) DO UPDATE SET indicator_type = EXCLUDED.indicator_type, account_date = EXCLUDED.account_date, update_time = EXCLUDED.update_time, indicator_value = EXCLUDED.indicator_value, owner_id = EXCLUDED.owner_id, company_id = EXCLUDED.company_id";
                        $insertSql = $commonInsertPreSql . implode(",", $values) . $commonInsertEndSql;
                        $db->getPdoInstance()->prepare($insertSql)->execute();
                        $isMatchFlag = true;
                    }
                }
            } catch (\Exception $e) {
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
                \LogUtil::info("错误信息是: [{$e->getMessage()}]");
                \common\library\report\error\ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace());
            }
        }


        //预设成交订单金额 维护客户统计字段维护
        if ( $ruleInfo['type'] == 2 && $ruleInfo['refer_type'] == \Constants::TYPE_ORDER) {

            //匹配到预设规则 则更新重算客群
            if (!$isMatchFlag){
                $orderIdStr = implode(',',$referIds);
                $queryCompanyIdSql = "select distinct company_id from tbl_order where client_id={$this->clientId} and order_id in ($orderIdStr)";
                $customerGroupCompanyIds = $db->createCommand($queryCompanyIdSql)->queryColumn();
            }

            if (!empty($this->oldAttributes['company_id'])) {
                array_push($customerGroupCompanyIds, $this->oldAttributes['company_id']);
                $customerGroupCompanyIds = array_filter(array_unique($customerGroupCompanyIds));
            }
            $originCompanyIds = $extraData['changedCompanyIds'] ?? [];
            if (!empty($originCompanyIds)) {
                $customerGroupCompanyIds = array_unique(array_merge($customerGroupCompanyIds, $originCompanyIds));
            }
            if(empty($customerGroupCompanyIds)) {
                return true;
            }
            \LogUtil::info('refreshCompanyFieldWithTransactionOrder', [
                'client_id' => $clientId,
                'rule_id' => $ruleId,
                $referIds,
            ]);
            Helper::refreshCompanyFieldWithTransactionOrder($clientId, $ruleId, $customerGroupCompanyIds);

            // 更改order的account_flag
            $adminUserId = PrivilegeService::getInstance($this->clientId)->getAdminUserId();
            $orderBatchOperator = new OrderBatchOperator($adminUserId);
            $notMatchOrderIds = array_diff($referIds, $matchOrderIds);
            if ($isMatchFlag && !empty($matchOrderIds)) {
                $orderBatchOperator->setParams([
                    'order_ids' => $matchOrderIds,
                ]);
                $accountFlag = $ruleInfo['enable_flag'] ? true : false;
                $orderBatchOperator->changeAccountFlag($accountFlag);
            }
            if(!empty($notMatchOrderIds)){
                $orderBatchOperator->setParams([
                    'order_ids' => $notMatchOrderIds,
                ]);
                $orderBatchOperator->changeAccountFlag(false);
            }
            //计算referIds的首单、计算首次成交订单金额
            \common\library\performance_v2\Helper::refreshOrderFirstFlag($clientId, $ruleInfo['rule_id'], $customerGroupCompanyIds);

        }


        return true;
    }

    public function getBatchRecordPerformances($referTYpe, $referInfoList, $rule,$extraData = []): array
    {
        $userPerformanceRecords = [];
        $departmentPerformanceRecords = [];
        if (empty($referInfoList)) return [$userPerformanceRecords, $departmentPerformanceRecords];

        $specialKeys = $extraData['specialKeys'] ?? [];
        $extraData['mainCurrency'] = empty($extraData['mainCurrency']) ? $extraData['mainCurrency'] :  Client::getClient($rule['client_id'])->getMainCurrency();

        // 初始化公共参数
        $specialReferIds = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? array_column($referInfoList,$specialKeys['refer_id_key']) : 0;
        $targetField = $rule['target_field']; // 考核指标字段

        $performanceField = $rule['performance_field']; // 被考核角色字段
        // 转换performanceField => e.g 邮件中的sender和receiver同时取user_id
        $performanceField = Helper::transferPerformanceField($referTYpe,$performanceField);

        $timeField = $rule['time_field']; // 计入时间依据字段
        $now = date('Y-m-d H:i:s');
        $formula = json_decode($rule['formula'],true) ?? [];
        $clientId = $rule['client_id'];

        $relateInfo = [];

        $adminUserId = PrivilegeService::getInstance($clientId)->getAdminUserId();
        if (!$adminUserId) {
            \LogUtil::info("clientId:$clientId adminUser not exist!");
            return [$userPerformanceRecords, $departmentPerformanceRecords];
        }

        if (!empty($formula))
        {
            $relateInfo = Helper::getFormulaRelateInfoByReferId($clientId,$adminUserId,$formula,$specialReferIds,$referTYpe);
        }
        // 有些考核指标需要关联其他对象  e.g:  order => cash_collection
        $specialInfo = Helper::getRelateInfoMapByTargetField($clientId,$referTYpe,$targetField,$specialReferIds,$formula);

        $allReferUserIds = array_unique(Helper::getAllUserIdsByReferInfoListAndTargetField($referInfoList,$referTYpe,$performanceField));

        $userToDepartmentListMap = Helper::batchGetUserToDepartmentList($clientId, $allReferUserIds);

        foreach ($referInfoList as $referInfo) {

            if (!Helper::isNeedRecordPerformance($referTYpe, $referInfo, $rule['rule_id'])) continue;

            $externalFieldData = Helper::getExternalFieldDataByReferInfo($referInfo,$referTYpe);
            $specialReferId = (isset($specialKeys['refer_id_key']) && $specialKeys['refer_id_key'] != '') ? $referInfo[$specialKeys['refer_id_key']] : 0;
            $referInfo['specialInfo'] = $specialInfo[$specialReferId] ?? [];

            $exchangeRateSetting = [
                'amount_rmb' => 'exchange_rate',
                'amount_usd' => 'exchange_rate_usd',
            ];
            $exchangeRate = (int)(isset($exchangeRateSetting[$targetField]) ? $referInfo[$exchangeRateSetting[$targetField]] : 100);

            if (empty($specialReferId)) {
                continue;
            }

            $performanceRates = Helper::getReferPerformanceRates($referTYpe, $referInfo, $performanceField, $userToDepartmentListMap,$extraData);
            $userRates = $performanceRates['users'];
            $departmentRates = $performanceRates['departments'];
            $accountDate = '';
            if (in_array($referTYpe, [\Constants::TYPE_COMPANY,\Constants::TYPE_OPPORTUNITY, \Constants::TYPE_ORDER, \Constants::TYPE_QUOTATION, \Constants::TYPE_CASH_COLLECTION,\Constants::TYPE_LEAD])) {
                $accountDate = (isset($externalFieldData[$timeField]) && !empty($externalFieldData[$timeField])) ? $externalFieldData[$timeField] : '';
            }

            $timeField = Helper::transferTimeField($referTYpe,$timeField);



            if (empty($accountDate)) {
                // 自定义字段可能有没有值的时候
                $accountDate = $referInfo[$timeField] ?? '1970-01-01 00:00:00';
            }

            if (empty($accountDate)) {
                continue;
            }

            // 特殊字段在这里加
            $extraData['exchangeRate'] = $exchangeRate;

            // 公共数据部分
            $record = [
                'client_id' => $rule['client_id'],
                'refer_type' => $referTYpe,
                'refer_id' => $specialReferId,
                'indicator_type' => $rule['target_field'],
                'account_date' => $accountDate,
                'create_time' => $now,
                'update_time' => $now,
                'rule_id' => $rule['rule_id'],
                'indicator_value' => 0,
                'owner_id' => 0,
                'company_id' => $referInfo['company_id'] ?? 0,
            ];


            foreach ($userRates as $belongsTo => $rate) {
                if (empty($belongsTo)) continue;
                $userRecord = Helper::getPerformanceRecordMapByRecordType($record,PerformanceV2Constant::RECORD_TYPE_USER,$referInfo,$relateInfo,$rule,$belongsTo,$rate,$extraData);
                $userPerformanceRecords[] = $userRecord;
            }

            foreach ($departmentRates as $belongsTo => $rate) {
                $belongsTo = (int)$belongsTo;
                $departmentRecord = Helper::getPerformanceRecordMapByRecordType($record,PerformanceV2Constant::RECORD_TYPE_DEPARTMENT,$referInfo,$relateInfo,$rule,$belongsTo,$rate,$extraData);
                $departmentPerformanceRecords[] = $departmentRecord;
            }
            \LogUtil::info("getBatchRecordPerformances,refer_id=[{$specialReferId}],userRates=[" . json_encode($userRates) . '],departmentRates=[' . json_encode($departmentRates) . ']');
        }

        return [$userPerformanceRecords, $departmentPerformanceRecords];
    }


    private function getReferIds($rule, $referIds, $db)
    {
        //解决这个工单问题【【Bug转需求】【工单#20211117000020】已回款金额完成情况统计已回款金额统计的不对，用户在11月13号有符合条件的回款单，但是没有统计到】 https://www.tapd.cn/21404721/prong/stories/view/1121404721001025928
        if ($rule['refer_type'] == \Constants::TYPE_CASH_COLLECTION) {
            $filters = $rule['filters'] ?? [];

            if (empty($filters)) {
                return $referIds;
            }

            $flag = false;
            foreach ($filters as $workflowFilter) {
                if ($workflowFilter['field'] == 'cash_collection_status' && $workflowFilter['refer_type'] == \Constants::TYPE_ORDER) {
                    $flag = true;
                    break;
                }
            }

            if ($flag) {

                $cashCollectionIdStr = implode(',', $referIds);
                $selectCashCollectionSql =
                    "select  cash_collection_id from tbl_cash_collection where client_id={$this->clientId} and order_id in
        ((select order_id from tbl_cash_collection where client_id = {$this->clientId} and cash_collection_id in ({$cashCollectionIdStr})))";

                $cashCollectionIds = $db->createCommand($selectCashCollectionSql)->queryColumn();

                if (empty($cashCollectionIds)) {
                    return $referIds;
                }

                return $cashCollectionIds;
            }
        }

        return $referIds;
    }


    /**
     * 根据场景判断某些规则是否需要跳过执行
     * @param $referType
     * @param $timeField
     * @return bool
     */
    private function checkNeedSkipPerformanceRuleByScene($referType,$timeField): bool
    {
        $needToRecordMap = [
            \Constants::TYPE_COMPANY => [
                'private_time',
                'public_time',
            ]
        ];
        switch ($this->scene){
            case PerformanceV2Constant::PERFORMANCE_RECORD_JOB_SCENE_TRANSFER:
                if (\common\library\performance_v2\rule\PerformanceV2Rule::checkIsDynamicTimeByReferTypeAndTimeField($referType,$timeField))
                {
                    // 客户的移入公私海时间是在转移场景需要计算的
                    return !in_array($timeField,$needToRecordMap[$referType] ?? []);
                };
                break;

            default:
                return false;
        }
        return false;
    }


    /**
     * 绩效特殊逻辑 当需要根据updateField过滤某些绩效规则的时候请调用这个方法
     * @param $referType
     * @param $timeField
     * @return bool
     */
    private function checkNeedSkipPerformanceRuleByUpdateField($referType,$timeField): bool
    {
        // 商机阶段变更时只有在商机阶段真正变更的时候才需要计算绩效
        $map = [
            \Constants::TYPE_OPPORTUNITY => [
                'stage_edit_time'
            ],
            \Constants::TYPE_ORDER => [
                'last_order_status_update_time'
            ]
        ];

        if (empty($this->updateFields))
        {
            return false;
        }
        $skipFlag = false;
        if (in_array($timeField,$map[$referType] ?? []))
        {
            switch ($referType)
            {
                case \Constants::TYPE_OPPORTUNITY:
                    if (!in_array($timeField,$this->updateFields)) {
                        $skipFlag = true;
                    }
                    break;
                case \Constants::TYPE_ORDER:
                    if ($timeField == 'last_order_status_update_time' && !in_array('status',$this->updateFields)) {
                        $skipFlag = true;
                    }
                    break;
            }
        }
        return $skipFlag;

    }

    protected function withoutOverlapping()
    {
        if (in_array($this->clientId, [67066, 62263])) {
            return true;
        }
        return false;
    }

    protected function withoutOverlappingKey():bool
    {
        return 'performance_record_job_' . $this->clientId;
    }

    protected function checkNeedDeleteRecordByRuleInfo($ruleInfo)
    {
        $referType = $ruleInfo['refer_type'];
        $timeField = $ruleInfo['time_field'];

        if ($referType == \Constants::TYPE_EDM) return false;

        // 这三个时间额外判断 动态时间 但是可以删除绩效
        if ($referType == \Constants::TYPE_COMPANY && in_array($timeField, ['latest_success_opportunity_time', 'every_transaction_order_time', 'deal_time'])) {
            return true;
        }

        $isDynamicTime =  \common\library\performance_v2\rule\PerformanceV2Rule::checkIsDynamicTimeByReferTypeAndTimeField($referType, $timeField);

        if ($isDynamicTime) {
            return false;
        }

        // 非动态时间删除绩效
        return true;
    }

}