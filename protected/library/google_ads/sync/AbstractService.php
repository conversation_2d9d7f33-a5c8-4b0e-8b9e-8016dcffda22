<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/2/18
 * Time: 3:28 PM
 */

namespace common\library\google_ads\sync;


use common\library\google_ads\GoogleAccessAccountService;

abstract class AbstractService
{
    protected $clientId;
    protected $startDate;
    protected $endDate;

    protected $_adsList;
    protected $_accessList;
    protected $_siteList;

    abstract public function process();

    public function getServiceKey()
    {
        return 'ads:'.__CLASS__.':'.$this->clientId;
    }

    /**
     * @param $startDate
     */
    public function setStartDate($startDate)
    {
        $this->startDate = $startDate;
    }

    /**
     * @param $endDate
     */
    public function setEndDate($endDate)
    {
      $this->endDate = $endDate;
    }

    /**
     * @param mixed $adsList
     */
    public function setAdsList(array $adsList)
    {
        $this->_adsList = $adsList;
    }

    /**
     * @param mixed $accessList
     */
    public function setAccessList(array $accessList)
    {
        $this->_accessList = $accessList;
    }

    /**
     * @param mixed $siteList
     */
    public function setSiteList(array $siteList)
    {
        $this->_siteList = $siteList;
    }

    /**
     * @return mixed
     */
    public function getAdsList()
    {
        if( $this->_adsList ===null )
        {
            $this->_adsList =  GoogleAccessAccountService::getNormalAdsAccountAccessList($this->clientId);
        }
        return $this->_adsList;
    }

    /**
     * @return mixed
     */
    public function getAccessList()
    {
        if( $this->_accessList === null )
        {
            $this->_accessList = GoogleAccessAccountService::getNormalAccessList($this->clientId);
        }
        return $this->_accessList;
    }

    /**
     * @return mixed
     */
    public function getSiteList()
    {
        if( $this->_siteList === null )
        {
            $this->_siteList = GoogleAccessAccountService::getNormalSiteList($this->clientId);
        }

        return $this->_siteList;
    }


}
