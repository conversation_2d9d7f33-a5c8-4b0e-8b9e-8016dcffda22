<?php
/**
 *
 * Created by: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2021/12/29
 */

namespace common\library\exam;

class QuestionConstant
{
//题目类型
    const QUESTION_TYPE_SINGLE_CHOICE = 'single_choice';
    const QUESTION_TYPE_MULTIPLE_CHOICE = 'multiple_choice';

    const QUESTION_ID_1 = 1;
    const QUESTION_ID_2 = 2;
    const QUESTION_ID_3 = 3;
    const QUESTION_ID_4 = 4;
    const QUESTION_ID_5 = 5;
    const QUESTION_ID_6 = 6;

    const EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_1 = 'nine_day_plan_scheme_1'; //九天进阶计划方案1
    const EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_2 = 'nine_day_plan_scheme_2'; //九天进阶计划方案2
    const EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_3 = 'nine_day_plan_scheme_3'; //九天进阶计划方案3

    const QUESTION_LIST = [
        self::EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_1 => [
            self::QUESTION_ID_1 => [
                'question_id' => self::QUESTION_ID_1,
                'title' => '绑定阿里店铺后可以完成哪些操作？',
                'options' => [
                    'A' => '同步阿里客户通客户',
                    'B' => '同步阿里店铺产品',
                    'C' => '同步信保订单',
                    'D' => '以上都可以',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_2 => [
                'question_id' => self::QUESTION_ID_2,
                'title' => '营销邮件发送时需要填写多少个邮件主题？',
                'options' => [
                    'A' => '1',
                    'B' => '2',
                    'C' => '3',
                    'D' => '4',
                ],
                'correct_answer' => ['C'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_3 => [
                'question_id' => self::QUESTION_ID_3,
                'title' => '为了实现建立信任，挖掘商机的业务目标，我们需要对潜在客户做以下哪些业务动作？',
                'options' => [
                    'A' => '破冰',
                    'B' => '修复信任',
                    'C' => '需求分析',
                    'D' => '价值传递',
                ],
                'correct_answer' => ['A','C','D'],
                'type' => self::QUESTION_TYPE_MULTIPLE_CHOICE
            ],
            self::QUESTION_ID_4 => [
                'question_id' => self::QUESTION_ID_4,
                'title' => '以下哪项不是客户动态的跟进类型?',
                'options' => [
                    'A' => '快速记录',
                    'B' => '视频',
                    'C' => '电话',
                    'D' => '会面',
                ],
                'correct_answer' => ['B'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_5 => [
                'question_id' => self::QUESTION_ID_5,
                'title' => '可以对客户列表做哪些优化？',
                'options' => [
                    'A' => '字段显示优先级',
                    'B' => '字段显示内容',
                    'C' => '筛选条件',
                    'D' => '以上都可以',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
        ],
        self::EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_2 => [
            self::QUESTION_ID_1 => [
                'question_id' => self::QUESTION_ID_1,
                'title' => '绑定阿里店铺后可以完成哪些操作？',
                'options' => [
                    'A' => '同步阿里客户通客户',
                    'B' => '同步阿里店铺产品',
                    'C' => '同步信保订单',
                    'D' => '以上都可以',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_2 => [
                'question_id' => self::QUESTION_ID_2,
                'title' => '营销邮件发送时需要填写多少个邮件主题？',
                'options' => [
                    'A' => '1',
                    'B' => '2',
                    'C' => '3',
                    'D' => '4',
                ],
                'correct_answer' => ['C'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_3 => [
                'question_id' => self::QUESTION_ID_3,
                'title' => '为了实现建立信任，挖掘商机的业务目标，我们需要对潜在客户做以下哪些业务动作？',
                'options' => [
                    'A' => '破冰',
                    'B' => '修复信任',
                    'C' => '需求分析',
                    'D' => '价值传递',
                ],
                'correct_answer' => ['A','C','D'],
                'type' => self::QUESTION_TYPE_MULTIPLE_CHOICE
            ],
            self::QUESTION_ID_4 => [
                'question_id' => self::QUESTION_ID_4,
                'title' => '以下哪项不是客户动态的跟进类型?',
                'options' => [
                    'A' => '快速记录',
                    'B' => '视频',
                    'C' => '电话',
                    'D' => '会面',
                ],
                'correct_answer' => ['B'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_5 => [
                'question_id' => self::QUESTION_ID_5,
                'title' => '可以对客户列表做哪些优化？',
                'options' => [
                    'A' => '字段显示优先级',
                    'B' => '字段显示内容',
                    'C' => '筛选条件',
                    'D' => '以上都可以',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
        ],
        self::EXAM_TOPIC_NINE_DAY_PLAN_SCHEME_3 => [
            self::QUESTION_ID_6 => [
                'question_id' => self::QUESTION_ID_6,
                'title' => '海关数据不能用以下那个方式搜索？',
                'options' => [
                    'A' => '产品',
                    'B' => 'HSCODE',
                    'C' => '公司',
                    'D' => '交易总额',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_2 => [
                'question_id' => self::QUESTION_ID_2,
                'title' => '营销邮件发送时需要填写多少个邮件主题？',
                'options' => [
                    'A' => '1',
                    'B' => '2',
                    'C' => '3',
                    'D' => '4',
                ],
                'correct_answer' => ['C'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_3 => [
                'question_id' => self::QUESTION_ID_3,
                'title' => '为了实现建立信任，挖掘商机的业务目标，我们需要对潜在客户做以下哪些业务动作？',
                'options' => [
                    'A' => '破冰',
                    'B' => '修复信任',
                    'C' => '需求分析',
                    'D' => '价值传递',
                ],
                'correct_answer' => ['A','C','D'],
                'type' => self::QUESTION_TYPE_MULTIPLE_CHOICE
            ],
            self::QUESTION_ID_4 => [
                'question_id' => self::QUESTION_ID_4,
                'title' => '以下哪项不是客户动态的跟进类型?',
                'options' => [
                    'A' => '快速记录',
                    'B' => '视频',
                    'C' => '电话',
                    'D' => '会面',
                ],
                'correct_answer' => ['B'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
            self::QUESTION_ID_5 => [
                'question_id' => self::QUESTION_ID_5,
                'title' => '可以对客户列表做哪些优化？',
                'options' => [
                    'A' => '字段显示优先级',
                    'B' => '字段显示内容',
                    'C' => '筛选条件',
                    'D' => '以上都可以',
                ],
                'correct_answer' => ['D'],
                'type' => self::QUESTION_TYPE_SINGLE_CHOICE
            ],
        ],
    ];


    public static function getQuestionList($examKey){
        if (empty($examKey) || !isset(self::QUESTION_LIST[$examKey])){
            return [];
        }

        return array_column(self::QUESTION_LIST[$examKey],null,'question_id');
    }


    /**
     * @param $questionList
     * @return array
     * 数据格式化
     */
    public static function format($questionList){
        $data = [];
        foreach ($questionList as $questionItem){
            unset($questionItem['correct_answer']);
            $options = [];
            foreach ($questionItem['options'] as $k => $option){
                $options[] = [
                    'value' => $k,
                    'label' => $k.'.'.\Yii::t('promotion', $option)
                ];
            }
            $questionItem['options'] = $options;
            $questionItem['title'] = \Yii::t('promotion',$questionItem['title']);
            $data[] = $questionItem;
        }
        return $data;
    }


}