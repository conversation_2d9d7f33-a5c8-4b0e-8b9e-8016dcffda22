<?php

namespace common\library\ai_sdr;

use common\library\ai_agent\AgentWorkflowService;
use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_agent\SdrSellerIndustryAnalyze;
use common\library\ai_agent\SdrEdmWriteAgent;
use common\library\ai_sdr\interfaces\AiAgentFactoryInterface;
use common\library\AiBackgroundCheckService;

/**
 * AI Agent工厂实现
 * 
 * 负责创建各种AI Agent服务实例
 */
class AiAgentFactory implements AiAgentFactoryInterface
{
    protected int $clientId;
    protected int $userId;
    
    public function __construct(int $clientId, int $userId)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
    }
    
    /**
     * 创建潜客质量分析Agent
     * 
     * @return mixed
     */
    public function createQualityAnalysisAgent()
    {
        return new SdrLeadQualityAnalysisAgent($this->clientId, $this->userId);
    }
    
    /**
     * 创建卖家行业分析Agent
     * 
     * @return mixed
     */
    public function createSellerIndustryAnalyzer()
    {
        return new SdrSellerIndustryAnalyze($this->clientId, $this->userId);
    }
    
    /**
     * 创建EDM写作Agent
     * 
     * @return mixed
     */
    public function createEdmWriteAgent()
    {
        return new SdrEdmWriteAgent($this->clientId, $this->userId);
    }
    
    /**
     * 创建Agent工作流服务
     * 
     * @return mixed
     */
    public function createAgentWorkflowService()
    {
        return new AgentWorkflowService($this->clientId, $this->userId);
    }
    
    /**
     * 创建背景调研Agent
     * 
     * @return mixed
     */
    public function createBackgroundCheckAgent()
    {
        return new AiBackgroundCheckService($this->clientId, $this->userId);
    }
    
    /**
     * 创建产品分类Agent
     * 
     * @return mixed
     */
    public function createProductCategoryAgent()
    {
        // 这里可以根据需要实现具体的产品分类Agent
        // 目前返回一个通用的Agent工作流服务
        return new AgentWorkflowService($this->clientId, $this->userId);
    }
    
    /**
     * 创建卖家档案Agent
     * 
     * @return mixed
     */
    public function createSellerProfileAgent()
    {
        // 这里可以根据需要实现具体的卖家档案Agent
        // 目前返回一个通用的Agent工作流服务
        return new AgentWorkflowService($this->clientId, $this->userId);
    }
    
    /**
     * 创建主页分析Agent
     * 
     * @return mixed
     */
    public function createHomepageAgent()
    {
        // 这里可以根据需要实现具体的主页分析Agent
        // 目前返回一个通用的Agent工作流服务
        return new AgentWorkflowService($this->clientId, $this->userId);
    }
}
