<?php

namespace common\library\ai_sdr\interfaces;

/**
 * 队列服务接口
 * 
 * 定义队列任务分发的核心方法，支持依赖注入和Mock测试
 */
interface QueueServiceInterface
{
    /**
     * 异步分发任务到队列
     * 
     * @param mixed $job 任务对象
     * @return void
     */
    public function dispatch($job): void;
    
    /**
     * 同步执行任务
     * 
     * @param mixed $job 任务对象
     * @return void
     */
    public function dispatchSync($job): void;
    
    /**
     * 延迟分发任务
     * 
     * @param mixed $job 任务对象
     * @param int $delay 延迟时间（秒）
     * @return void
     */
    public function dispatchAfter($job, int $delay): void;
    
    /**
     * 批量分发任务
     * 
     * @param array $jobs 任务列表
     * @return void
     */
    public function dispatchBatch(array $jobs): void;
}
