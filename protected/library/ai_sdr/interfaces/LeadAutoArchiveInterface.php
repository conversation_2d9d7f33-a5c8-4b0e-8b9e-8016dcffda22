<?php

namespace common\library\ai_sdr\interfaces;

/**
 * 线索自动归档接口
 * 
 * 定义线索归档服务的核心方法，支持依赖注入和Mock测试
 */
interface LeadAutoArchiveInterface
{
    /**
     * 批量根据域名归档线索
     * 
     * @param array $domains 域名列表
     * @param bool $createIfNotExists 如果不存在是否创建
     * @return array 归档结果，键为域名，值为线索对象
     */
    public function archiveByBatchDomain(array $domains, bool $createIfNotExists = false): array;
    
    /**
     * 根据单个域名归档线索
     * 
     * @param string $domain 域名
     * @param bool $createIfNotExists 如果不存在是否创建
     * @return mixed 线索对象或false
     */
    public function archiveByDomain(string $domain, bool $createIfNotExists = false);
    
    /**
     * 批量根据邮箱归档线索
     * 
     * @param array $emails 邮箱列表
     * @param bool $createIfNotExists 如果不存在是否创建
     * @return array 归档结果
     */
    public function archiveByBatchEmail(array $emails, bool $createIfNotExists = false): array;
    
    /**
     * 根据公司信息归档线索
     * 
     * @param array $companyInfo 公司信息
     * @param bool $createIfNotExists 如果不存在是否创建
     * @return mixed 线索对象或false
     */
    public function archiveByCompanyInfo(array $companyInfo, bool $createIfNotExists = false);
}
