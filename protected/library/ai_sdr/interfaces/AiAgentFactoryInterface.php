<?php

namespace common\library\ai_sdr\interfaces;

use common\library\ai_agent\AgentWorkflowService;
use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_agent\SdrSellerIndustryAnalyze;

/**
 * AI Agent工厂接口
 * 
 * 负责创建各种AI Agent服务实例，支持依赖注入和Mock测试
 */
interface AiAgentFactoryInterface
{
    /**
     * 创建潜客质量分析Agent
     *
     * @return mixed
     */
    public function createQualityAnalysisAgent();

    /**
     * 创建卖家行业分析Agent
     *
     * @return mixed
     */
    public function createSellerIndustryAnalyzer();
    
    /**
     * 创建EDM写作Agent
     * 
     * @return mixed EDM写作Agent实例
     */
    public function createEdmWriteAgent();
    
    /**
     * 创建Agent工作流服务
     *
     * @return mixed
     */
    public function createAgentWorkflowService();
    
    /**
     * 创建背景调研Agent
     * 
     * @return mixed 背景调研Agent实例
     */
    public function createBackgroundCheckAgent();
    
    /**
     * 创建产品分类Agent
     * 
     * @return mixed 产品分类Agent实例
     */
    public function createProductCategoryAgent();
    
    /**
     * 创建卖家档案Agent
     * 
     * @return mixed 卖家档案Agent实例
     */
    public function createSellerProfileAgent();
    
    /**
     * 创建主页分析Agent
     * 
     * @return mixed 主页分析Agent实例
     */
    public function createHomepageAgent();
}
