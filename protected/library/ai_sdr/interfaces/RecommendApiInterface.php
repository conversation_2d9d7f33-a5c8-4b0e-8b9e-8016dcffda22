<?php

namespace common\library\ai_sdr\interfaces;

/**
 * 推荐API接口
 * 
 * 定义推荐服务的核心方法，支持依赖注入和Mock测试
 */
interface RecommendApiInterface
{
    /**
     * 根据域名获取公司画像
     * 
     * @param array $domains 域名列表
     * @return array 公司画像数据，键为域名，值为画像信息
     */
    public function getCompanyProfileByDomains(array $domains): array;
    
    /**
     * 根据画像匹配公司
     *
     * @param int $matchType 匹配类型 (行业/产品)
     * @param array $industryIds 行业ID列表
     * @param array $industryProducts 产品列表
     * @param string|null $beforePortraitIds 分页参数
     * @param array $excludeDomains 排除域名
     * @param int $pageSize 页面大小
     * @return array 匹配的公司列表
     */
    public function getMatchCompanyByProfile(
        $matchType,
        $industryIds = [],
        $industryProducts = [],
        $beforePortraitIds = null,
        $excludeDomains = [],
        $pageSize = 100
    ): array;
    
    /**
     * 获取邮箱质量评级
     * 
     * @param array $emails 邮箱列表
     * @return array 邮箱质量评级，键为邮箱，值为评级
     */
    public function getMailQualityRating(array $emails): array;
    
    /**
     * 获取推荐列表
     * 
     * @return array 推荐数据列表
     */
    public function getRecommendList(): array;
    
    /**
     * 根据搜索标签获取推荐列表
     *
     * @param array $tagList 标签列表
     * @param array $type 类型过滤
     * @param array $keywords 关键词过滤
     * @param array $sort 排序条件
     * @param mixed $hasCompany 是否有公司信息
     * @return array 推荐列表
     */
    public function getRecommendListBySearchTag(
        array $tagList,
        array $type = [],
        array $keywords = [],
        array $sort = [],
        $hasCompany = null
    ): array;
}
