<?php

namespace common\library\ai_sdr\task_detail;

use xiaoman\orm\common\Operator;
use xiaoman\orm\repository\Repository;


/**
 * class AiSdrTaskDetailOperator
 * @package common\library\ai_sdr\task_detail
 * @method AiSdrTaskDetailFormatter getFormatter()
 */
class AiSdrTaskDetailOperator extends Operator
{
    const TASK_LIST = [];

    public function create($fields = []) {
        if (empty($fields)) {
            $fields = ['task_id', 'user_id', 'lead_id', 'source','usage_record_id', 'stage', 'lead_quality', 'product_ids', 'company_types', 'status', 'enable_flag', 'stage', 'public_homepage','stage_dig_time', ];
        } else {
            $fields = array_intersect($fields, ['task_id', 'user_id', 'lead_id', 'source','usage_record_id', 'stage', 'lead_quality', 'product_ids', 'company_types', 'status', 'enable_flag', 'stage', 'company_id', 'public_homepage','stage_dig_time', ]);
        }
        $data = $this->get($fields);
        $time = xm_function_now();
        array_walk($data, function ($item) use ($time) {
            $item['create_time'] = $time;
            $item['update_time'] = $time;
        });

        return $this->batchInsert($data);
    }

    public function update(array $fields) {
        $fields['update_time'] = xm_function_now();
        return $this->execute($fields);
    }

    public function batchUpdate(array $data) {
        $updateSqlArr = [];
        $table = $this->object->getMetadata()::table();
        $columns = $this->object->getMetadata()->getColumnsKeys();
        $params = [];
        foreach ($data as $detailId => $detail) {
            $validColumns = array_intersect(array_keys($detail), $columns);
            $updateSql = "UPDATE {$table} SET ";
            $updateFields = [];
            foreach ($validColumns as $column) {
                if ($column == 'id') {
                    continue;
                }
                $updateFields[] = "{$column} = :{$column}_{$detailId}";
                $params[":{$column}_{$detailId}"] = $detail[$column];
            }
            $updateSql .= implode(',', $updateFields)." WHERE id = :id_{$detailId}";
            $params[":id_{$detailId}"] = $detailId;
            $updateSqlArr[] = $updateSql;
        }
        $repositoryClass = $this->object->getMetadata()::repository();
        /**
         * @var $repository Repository
         */
        $repository = new $repositoryClass($this->object->getClientId());
        $repository->setMetadata($this->object->getMetadata());
        return $repository->execute(implode(";", $updateSqlArr), $params);
    }
}