# AI SDR 依赖注入重构计划

## 🎯 目标

将AI SDR业务代码从硬编码依赖重构为支持依赖注入的架构，实现真正的业务流程测试覆盖。

## 📋 重构范围

### 阶段1: 核心服务重构 (优先级: 高)

#### 1.1 AISdrService 重构
**当前问题**:
- 直接实例化 `RecommendApi`、`InnerApi`、`LeadAutoArchive` 等外部服务
- 无法进行完整的业务流程测试

**重构方案**:
```php
class AISdrService {
    private RecommendApiInterface $recommendApi;
    private LeadAutoArchiveInterface $leadAutoArchive;
    private QueueServiceInterface $queueService;
    
    public function __construct(
        int $clientId,
        int $userId,
        ?RecommendApiInterface $recommendApi = null,
        ?LeadAutoArchiveInterface $leadAutoArchive = null,
        ?QueueServiceInterface $queueService = null
    ) {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->recommendApi = $recommendApi ?? new RecommendApi($clientId, $userId);
        $this->leadAutoArchive = $leadAutoArchive ?? new LeadAutoArchive($clientId, $userId);
        $this->queueService = $queueService ?? new QueueService();
    }
}
```

#### 1.2 SdrDetailExecutor 重构
**当前问题**:
- 直接实例化各种AI Agent服务
- 状态机执行过程中的外部依赖无法Mock

**重构方案**:
```php
class SdrDetailExecutor {
    private AiAgentFactoryInterface $aiAgentFactory;
    private RecommendApiInterface $recommendApi;
    
    public function __construct(
        int $clientId,
        int $userId,
        ?AiAgentFactoryInterface $aiAgentFactory = null,
        ?RecommendApiInterface $recommendApi = null
    ) {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->aiAgentFactory = $aiAgentFactory ?? new AiAgentFactory($clientId, $userId);
        $this->recommendApi = $recommendApi ?? new RecommendApi($clientId, $userId);
    }
}
```

#### 1.3 RecommendCompanyService 重构
**当前问题**:
- 直接实例化 `AgentWorkflowService`、`AiBackgroundCheckService` 等
- 推荐流程无法完整测试

### 阶段2: 接口定义 (优先级: 高)

#### 2.1 核心接口定义
```php
interface RecommendApiInterface {
    public function getCompanyProfileByDomains(array $domains): array;
    public function getMatchCompanyByProfile(int $matchType, array $criteria, ?string $beforePortraitIds = null, int $pageSize = 20): array;
    public function getMailQualityRating(array $emails): array;
}

interface AiAgentFactoryInterface {
    public function createQualityAnalysisAgent(): SdrLeadQualityAnalysisAgent;
    public function createSellerIndustryAnalyzer(): SdrSellerIndustryAnalyze;
    public function createEdmWriteAgent(): SdrEdmWriteAgent;
    public function createAgentWorkflowService(): AgentWorkflowService;
}

interface LeadAutoArchiveInterface {
    public function archiveByBatchDomain(array $domains, bool $createIfNotExists = false): array;
}

interface QueueServiceInterface {
    public function dispatch($job): void;
    public function dispatchSync($job): void;
}
```

### 阶段3: Mock实现增强 (优先级: 中)

#### 3.1 增强现有Mock服务
```php
class MockRecommendApi implements RecommendApiInterface {
    private array $companyProfiles = [];
    private array $matchResults = [];
    private array $emailRatings = [];
    
    public function setCompanyProfiles(array $profiles): void;
    public function setMatchResults(array $results): void;
    public function setEmailRatings(array $ratings): void;
}

class MockAiAgentFactory implements AiAgentFactoryInterface {
    private MockAiServices $mockAiServices;
    
    public function createQualityAnalysisAgent(): MockQualityAnalysisAgent;
    public function createSellerIndustryAnalyzer(): MockSellerIndustryAnalyzer;
    // ...
}
```

## 🚀 实施计划

### 第1周: 接口定义和核心重构
- [ ] 定义核心业务接口
- [ ] 重构 AISdrService 构造函数
- [ ] 重构 SdrDetailExecutor 构造函数
- [ ] 创建对应的Mock实现

### 第2周: 业务流程测试
- [ ] 编写完整的AI SDR工作流测试
- [ ] 测试任务创建 -> 挖掘 -> 质量分析 -> 营销的完整流程
- [ ] 验证状态机转换的正确性

### 第3周: 扩展和优化
- [ ] 重构 RecommendCompanyService
- [ ] 完善异常场景测试
- [ ] 优化测试执行速度

## 📊 预期收益

### 测试覆盖提升
- **当前**: 单元测试85%，功能测试受限于外部依赖
- **目标**: 单元测试90%+，完整业务流程测试覆盖

### 开发效率提升
- **Mock环境开发**: 无需依赖真实AI服务和推荐API
- **测试执行速度**: 从分钟级降低到秒级
- **CI/CD集成**: 稳定的自动化测试

### 代码质量提升
- **依赖关系清晰**: 明确的接口定义
- **可维护性**: 易于替换和升级外部服务
- **可扩展性**: 支持不同环境的服务实现

## 🔧 技术实现细节

### 向后兼容策略
```php
// 保持现有构造函数签名，添加可选参数
public function __construct(
    int $clientId,
    int $userId,
    // 新增可选依赖注入参数
    ?RecommendApiInterface $recommendApi = null,
    ?LeadAutoArchiveInterface $leadAutoArchive = null
) {
    // 向后兼容的默认实现
    $this->recommendApi = $recommendApi ?? new RecommendApi($clientId, $userId);
    $this->leadAutoArchive = $leadAutoArchive ?? new LeadAutoArchive($clientId, $userId);
}
```

### 测试环境配置
```php
// 测试基类中的依赖注入
abstract class AiSdrTestCase extends WebFunctionalTestCase {
    protected function createAiSdrService(array $mocks = []): AISdrService {
        return new AISdrService(
            $this->testClientId,
            $this->testUserId,
            $mocks['recommendApi'] ?? MockServices::createMockRecommendApi(),
            $mocks['leadAutoArchive'] ?? MockServices::createMockLeadAutoArchive(),
            $mocks['queueService'] ?? MockServices::createMockQueueService()
        );
    }
}
```

## 📝 风险评估

### 低风险
- ✅ 向后兼容的构造函数设计
- ✅ 渐进式重构，不影响现有功能
- ✅ 完整的测试覆盖验证

### 中等风险
- ⚠️ 接口设计需要仔细考虑未来扩展性
- ⚠️ Mock实现需要与真实服务保持一致

### 缓解措施
- 📋 详细的接口文档和测试用例
- 🔄 定期与真实服务进行集成测试验证
- 📊 监控重构前后的测试覆盖率变化

---

**负责人**: Augment Agent  
**创建时间**: 2025-01-06  
**预计完成**: 2025-01-20
