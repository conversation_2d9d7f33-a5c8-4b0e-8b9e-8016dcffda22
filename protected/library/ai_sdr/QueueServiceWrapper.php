<?php

namespace common\library\ai_sdr;

use common\library\ai_sdr\interfaces\QueueServiceInterface;
use common\library\queue_v2\QueueService;

/**
 * 队列服务包装器
 * 
 * 将静态的QueueService包装为实例方法，以支持依赖注入
 */
class QueueServiceWrapper implements QueueServiceInterface
{
    /**
     * 异步分发任务到队列
     * 
     * @param mixed $job 任务对象
     * @return void
     */
    public function dispatch($job): void
    {
        QueueService::dispatch($job);
    }
    
    /**
     * 同步执行任务
     * 
     * @param mixed $job 任务对象
     * @return void
     */
    public function dispatchSync($job): void
    {
        if (method_exists($job, 'handle')) {
            $job->handle();
        }
    }
    
    /**
     * 延迟分发任务
     * 
     * @param mixed $job 任务对象
     * @param int $delay 延迟时间（秒）
     * @return void
     */
    public function dispatchAfter($job, int $delay): void
    {
        QueueService::later($job, $delay);
    }
    
    /**
     * 批量分发任务
     * 
     * @param array $jobs 任务列表
     * @return void
     */
    public function dispatchBatch(array $jobs): void
    {
        foreach ($jobs as $job) {
            $this->dispatch($job);
        }
    }
}
