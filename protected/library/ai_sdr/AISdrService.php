<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * Author: <PERSON>(<PERSON><PERSON>)
 * Date: 2025/3/29
 * Time: 14:16
 */

namespace common\library\ai_sdr;

use common\components\BaseObject;
use common\library\account\Client;
use common\library\ai_agent\SdrLeadQualityAnalysisAgent;
use common\library\ai_sdr\buyer_portrait\CrmBuyerPortraitList;
use common\library\ai_sdr\dig_record\AiSdrDigRecordFilter;
use common\library\ai_sdr\dig_record\BatchAiSdrDigRecord;
use common\library\ai_sdr\interfaces\RecommendApiInterface;
use common\library\ai_sdr\interfaces\LeadAutoArchiveInterface;
use common\library\ai_sdr\interfaces\QueueServiceInterface;
use common\library\ai_sdr\product\ProductList;
use common\library\ai_sdr\profile\ClientProfile;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task\BatchAiSdrTask;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailOperator;
use common\library\ai_sdr\task_detail\BatchAiSdrTaskDetail;
use common\library\ai_sdr\task_record\AiSdrTaskRecordFilter;
use common\library\ai_sdr\task_record\BatchAiSdrTaskRecord;
use common\library\ai_sdr\task_record\builder\BackgroundCheckBuilder;
use common\library\ai_sdr\usage_record\AiProductUsageRecordFilter;
use common\library\api\InnerApi;
use common\library\cache\RedisCache;
use common\library\cms\setting\process\Base;
use common\library\customer\Company;
use common\library\customer_v2\CompanyList;
use common\library\customer_v3\customer\CustomerList;
use common\library\lead\Lead;
use common\library\lead\LeadAutoArchive;
use common\library\lead\LeadCustomer;
use common\library\lead_v3\form\LeadInputForm;
use common\library\lead_v3\LeadList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\queue_v2\job\AiSdrAddCompanyJob;
use common\library\queue_v2\job\AiSdrCrmBuyerPortraitJob;
use common\library\queue_v2\job\AiSdrHutchLeadJob;
use common\library\queue_v2\job\AiSdrDigTaskJob;
use common\library\queue_v2\job\AiSdrLeadQualityAnalyzeJob;
use common\library\queue_v2\QueueService;
use common\library\recommend_plaza\RecommendApi;
use common\library\setting\library\origin\Origin;
use common\library\setting\library\status\Status;
use common\library\setting\library\tag\Tag;
use common\library\util\Arr;
use common\library\util\PgsqlUtil;
use GroupMail;
use xiaoman\orm\database\data\EGT;
use xiaoman\orm\database\data\ELT;
use xiaoman\orm\database\data\In;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\LT;
use xiaoman\orm\database\data\NotEqual;
use xiaoman\orm\database\data\NotIn;
use xiaoman\orm\database\data\Range;

class AISdrService  {
    protected $clientId;
    protected $userId;

    // 依赖注入的服务
    protected RecommendApiInterface $recommendApi;
    protected LeadAutoArchiveInterface $leadAutoArchive;
    protected QueueServiceInterface $queueService;

    public function __construct(
        $clientId,
        $userId,
        ?RecommendApiInterface $recommendApi = null,
        ?LeadAutoArchiveInterface $leadAutoArchive = null,
        ?QueueServiceInterface $queueService = null
    ) {
        if (empty($clientId) || empty($userId)) {
            $user = \User::getLoginUser();
            $this->clientId = $user->getClientId();
            $this->userId = $user->getUserId();
        } else {
            $this->clientId = $clientId;
            $this->userId = $userId;
        }

        // 依赖注入，如果没有提供则使用默认实现
        $this->recommendApi = $recommendApi ?? new RecommendApi($this->clientId, $this->userId);
        $this->leadAutoArchive = $leadAutoArchive ?? new LeadAutoArchive($this->userId);
        $this->queueService = $queueService ?? new QueueServiceWrapper();
    }

    public function getTaskList() {
        $filter = new AiSdrTaskFilter($this->clientId);
        $filter->source = new NotEqual(Constant::TASK_SOURCE_SYSTEM);
        $filter->end_stage = new NotEqual(Constant::AI_SDR_STAGE_NOT_DEFINED);
        $filter->task_status = new NotEqual(Constant::AI_SDR_TASK_STATUS_PAUSED);
        $filter->enable_flag = 1;
        $filter->order('create_time', 'desc');
        $filter->select(['task_id', 'source', 'email', 'end_stage', 'current_stage','create_time']);
        $tasks =  $filter->rawData();
        // 每种 task 保留最近的第一个
        $validTask = [];
        foreach ($tasks as $task) {
            $taskSource = $task['source'];
            if (!isset($validTask[$taskSource])) {
                $validTask[$taskSource] = $task;
            }
        }
        return $validTask;
    }

    public function processTask($taskId = 0) {
        if ($taskId) {
            $task = new AiSdrTask($this->clientId, $taskId);
            $task->isNew() && throw new \RuntimeException(\Yii::t("ai","jRSr"));
            if ($task->task_status == Constant::AI_SDR_TASK_STATUS_PAUSED) {
                throw new \RuntimeException(\Yii::t('ai', "ai sdr任务已暂停"));
            }
            $task->getFormatter()->displayFields([
                'task_id',
                'current_stage',
                'end_stage'
            ]);
            $tasks = [$task->getAttributes()];
        } else {
            $tasks = $this->getTaskList();
            if (empty($tasks)) {
                throw new \RuntimeException(\Yii::t("ai","jRSr"));
            }
        }
        $endTasks = [];
        foreach ($tasks as $task) {
            if ($task['current_stage'] <= $task['end_stage']) {
                $this->processStageJob($task['task_id'], $task['current_stage'], $task['end_stage']);
            } else {
                $endTasks[] = [
                    'task_id' => $task['task_id'],
                ]; // 记录已完成的任务
            }
        }

        if (!empty($endTasks)) {
            $batchTask = new BatchAiSdrTask($this->clientId);
            $batchTask->initFromData($endTasks);
            $batchTask->getOperator()->update([
                'task_status' => Constant::AI_SDR_TASK_STATUS_FINISHED,
            ]);
        }
    }

    protected function processStageJob($taskId, $stage, $endStage) {
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            \LogUtil::info(\Yii::t("ai","jRSr"));
            return;
        }

        // 防并发处理重复数据
        $redis = \RedisService::cache();
        $key = sprintf(Constant::REDIS_CACHE_TASK_KEY, $this->clientId, $task->task_id);
        $result = $redis->set($key, 1, 'EX', 3600, 'NX');
        if (!$result) {
            \LogUtil::info("client_id {$this->clientId} task {$task->task_id} is already processing");
            return;
        }

        $sdrDetailExecutor = new SdrDetailExecutor($this->clientId, $this->userId);
        $sdrDetailExecutor->setTask($task);
        // 处理大于等当前阶段的任务逻辑
        try {
            foreach (Constant::AI_SDR_STAGE_FUNCTION as $stageKey => $function) {
                if ($stage > $stageKey) {
                    \LogUtil::info("client_id {$this->clientId} task_id {$taskId} current stage {$stage} is after {$stageKey}, skip process function {$function}");
                    continue;
                }
                if ($stageKey > $endStage) {
                    \LogUtil::info("client_id {$this->clientId} task_id {$taskId} {$stageKey} is after end stage {$stage}, skip process function {$function}");
                    continue;
                }
                $this->$function($task, $sdrDetailExecutor);
            }
        } catch (\Exception $exception) {
            \LogUtil::info("client_id {$this->clientId} task_id {$taskId} process stage {$stage} failed, exception: ".$exception->getMessage());
            throw $exception;
        } finally {
            $redis->del([$key]); // 处理完成后删除锁
        }
    }

    protected function marketing(AiSdrTask $task, SdrDetailExecutor $executor) {
        list($count, $batchDetail) = $this->loadDetailList($task, [
            Constant::DETAIL_STATUS_VALIDATE_CONTACTS,
            Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN,
            Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN
        ]);
        if (!$count && $task->current_stage == Constant::AI_SDR_STAGE_MARKETING) {
            \LogUtil::info("no leads need to marketing");
            $this->updateTaskStatus($task, Constant::AI_SDR_STAGE_EFFECTIVE);
        }
        /**
         * @var $batchDetail AiSdrTaskDetailOperator
        */
        $batchDetail->getFormatter()->displayBuyerProfile(true);
        $batchDetail->getFormatter()->displayMarketingContact(true);
        $batchDetail->getFormatter()->baseInfoSetting();
        $detailList = $batchDetail->getAttributes();

        $clientProfile = new ClientProfile($this->clientId);
        $user = \User::getUserObject($this->userId);
        $detailLeads = SdrLeadDetail::initFromArray($this->clientId, $detailList);
        $executor->setEdmMailAddress($task->email);
        $executor->setClientProfile($clientProfile->getAttributes());
        $executor->setCurrentEdmCount($user->info()->getCurrentCount());
        $detailLeads = $executor->process($detailLeads, Constant::DETAIL_STATUS_CREATE_MARKETING_PLAN); // 依赖上一步的执行结果决定是否立即执行下一流程
        $executor->process($detailLeads, Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN);
    }

    protected function updateTaskStatus(AiSdrTask $task, $targetStatus) {
        $task->current_stage = $targetStatus;
        $task->update_time = xm_function_now();
        $task->update(['current_stage', 'update_time']);
    }

    protected function loadDetailList(AiSdrTask $task, $targetStatus, $limit = 0) {
        $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task->task_id;
        $detailFilter->status = new In($targetStatus);
        $detailFilter->stage = new LT($task->end_stage);
        $count = $detailFilter->count();
        $limit && $detailFilter->limit($limit);
        return [$count, $detailFilter->find()];
    }

    /**
     * 如果不需要二次调用ai 背调服务，没有异步回调触发校验联系人，需要定时触发校验
    */
    protected function backgroundChecking(AiSdrTask $task, SdrDetailExecutor $executor) {
        if (!SdrDetailExecutor::$needBackgroundRecheck) {
            [$count, $backgroundCheckDetails] = $this->loadDetailList($task, [Constant::DETAIL_STATUS_BACKGROUND_CHECKING]);
            $backgroundCheckDetails->getFormatter()->baseInfoSetting();
            $backgroundCheckDetailLeads = $backgroundCheckDetails->getAttributes();
            $backgroundCheckDetailLeads = SdrLeadDetail::initFromArray($this->clientId, $backgroundCheckDetailLeads);
            $executor->process($backgroundCheckDetailLeads, Constant::DETAIL_STATUS_VALIDATE_CONTACTS);
        }

        [$count, $batchDetail] = $this->loadDetailList($task, [Constant::DETAIL_STATUS_ADD, Constant::DETAIL_STATUS_LABEL], Constant::DAILY_LIMIT);
        $batchDetail->getFormatter()->baseInfoSetting();
        $detailLeads = $batchDetail->getAttributes();
        $detailLeads = SdrLeadDetail::initFromArray($this->clientId, $detailLeads);
        $executor->process($detailLeads, Constant::DETAIL_STATUS_BACKGROUND_CHECKING); // ai 背调异步返回结果

        if (!$count && $task->current_stage == Constant::AI_SDR_STAGE_REACHABLE) {
            \LogUtil::info("all leads have checked background, no need proceed");
            $this->updateTaskStatus($task, Constant::AI_SDR_STAGE_MARKETING);
            \LogUtil::info("client_id {$this->clientId} task {$task->task_id} finish all background check details, start marketing now");
        }
    }

    public static function addSdrTagToLead(Lead $lead, AiSdrTask $task, int $leadQuality) {
        $systemTags = [Tag::TAG_AI_SDR];
        $validTag = Constant::LEAD_TAG_QUALITY_MAP[$leadQuality]??null;
        if ($validTag) {
            $systemTags[] = $validTag;
        }
        $tags = array_merge($systemTags, $task->tags);
        $lead->setTag(0, $tags);
        $lead->save();
    }

    public function createLead(AiSdrTask $sdrTask, int $productUsageId, LeadAutoArchive $leadAutoArchive, string $domain, array $buyerProfile, int $leadQuality, array $reason, $isShow = false) {
        $sdrTaskId = $sdrTask->task_id;
        $leads = $leadAutoArchive->archiveByBatchDomain([$domain], true);
        $lead = $leads[$domain]??false;
        if (!$lead) {
            \LogUtil::info("domain {$domain} archive to lead fail");
            return false;
        }

        if ($isShow) {
            self::addSdrTagToLead($lead, $sdrTask, $leadQuality);
        }

        /**
         * @var  $lead Lead
         */
        $detail = new AiSdrTaskDetail($this->clientId);
        $detail->lead_id = $lead->lead_id;
        $detail->task_id = $sdrTaskId;
        $detail->user_id = $this->userId;
        $detail->source = $sdrTask->source;
        $detail->stage = Constant::AI_SDR_STAGE_DIG;
        $detail->usage_record_id = $productUsageId;
        $detail->product_ids = array_unique($buyerProfile['main_products']??[]);
        $detail->company_types = array_unique($buyerProfile['company_type']??[]);
        $detail->public_homepage = array_unique(is_array($buyerProfile['public_homepage'] ?? '') ? $buyerProfile['public_homepage'] : json_decode($buyerProfile['public_homepage']??"", true));
        $detail->lead_quality = $leadQuality;
        $detail->enable_flag = intval($isShow);
        $detail->status = SdrDetailExecutor::$needBackgroundRecheck?Constant::DETAIL_STATUS_ADD:Constant::DETAIL_STATUS_BACKGROUND_CHECKING;
        if ($detail->enable_flag) {
            $detail->stage_dig_time = xm_function_now();
        }
        $success = $detail->create();
        if (!$success) {
            \LogUtil::info("domain {$domain} save lead succes but save sdr detail fail");
            return false;
        }

        if ($detail->enable_flag) {
            $this->updateStatTotal($this->clientId, $sdrTaskId, 1);
        }

        $buyerProfile['logo'] = $lead->image_list[0]??'';
        $buyerProfile['company_hash_id'] = $lead->company_hash_id;
        $buyerProfile['has_contacts'] = boolval($lead->main_customer_email);
        $batchRecord = new BatchAiSdrTaskRecord($this->clientId);
        $time = xm_function_now();
        $backgroundReport = $this->formatInternalBackgrounCheckReport($buyerProfile);
        $batchRecordData = [[
            'task_id' => $sdrTaskId,
            'type' => Constant::RECORD_TYPE_ADD_LEAD,
            'data' => [
                'domain' => $domain,
                'source' => \common\library\auto_market\Constant::DIG_SOURCE_COMPANY_SEARCH,
            ],
            'detail_id' => $detail->id,
            'lead_id' => $lead->lead_id,
            'estimate_time' => $time,
            'executed_time' => xm_function_now(),
            'create_time' => $time,
            'update_time' => $time,
            'refer_type' => 0,
            'refer_id' => 0,
        ], [
            'task_id' => $sdrTaskId,
            'type' => Constant::RECORD_TYPE_BACKGROUND_CHECK,
            'data' => $backgroundReport,
            'lead_id' => $lead->lead_id,
            'detail_id' => $detail->id,
            'estimate_time' => $time,
            'executed_time' => xm_function_now(),
            'create_time' => $time,
            'update_time' => $time,
            'refer_type' => 0,
            'refer_id' => 0,
        ], [
            'task_id' => $sdrTaskId,
            'type' => Constant::RECORD_TYPE_ANALYZE_QUALITY,
            'data' => $reason,
            'lead_id' => $lead->lead_id,
            'detail_id' => $detail->id,
            'estimate_time' => $time,
            'executed_time' => xm_function_now(),
            'create_time' => $time,
            'update_time' => $time,
            'refer_type' => 0,
            'refer_id' => 0,
        ]];

        $batchRecord->initFromData($batchRecordData);
        $batchRecord->getOperator()->create();
        return $detail->id;
    }

    public function processHutch(AiSdrTask $task) {
        $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task->task_id;
        $detailFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $detailFilter->status = Constant::DETAIL_STATUS_ADD;
        $detailFilter->select(['lead_id', 'id']);
        $leadIds = array_column($detailFilter->rawData(), 'id', 'lead_id');

        if (empty($leadIds)) {
            \LogUtil::info("no leads need to hutch");
            $task->current_stage = Constant::AI_SDR_STAGE_REACHABLE;
            $task->update_time = xm_function_now();
            $task->update(['current_stage', 'update_time']);
            return;
        }

        $leadList = new LeadList($this->userId);
        $leadList->setClientId($this->clientId);
        $leadList->setLeadId(array_keys($leadIds));
        $leadList->setSkipPrivilege(true);
        $leadList->setSkipFilterStatus(true);
        $leadList->setIsArchive(null);
        $leadList->setFields(['lead_id', 'homepage', 'main_customer_email']);
        $leadList = $leadList->find();

        if (!$leadList){
            \LogUtil::info('function processHutch, leadList are empty, task_id: '. $task->task_id);
            return;
        }

        $domainMap = [];
        foreach ($leadList as $lead) {
            $leadId = $lead['lead_id']??0;
            $homepage = $lead['homepage']??'';
            $mainCustomerEmail = $lead['main_customer_email']??'';
            $domain = $this->getLeadDomain($homepage, $mainCustomerEmail);
            if (empty($domain)) {
                \LogUtil::info("client_id {$this->clientId} lead_id {$leadId} hutch domain is empty");
                continue;
            }
            $domainMap[$domain] = [
                'lead_id' => $leadId,
                'detail_id' => $leadIds[$leadId]??0,
            ];
        }

        $buyerPortraitList = new CrmBuyerPortraitList();
        $domainChunk = array_chunk($domainMap, 100, true);
        $batchRecord = new BatchAiSdrTaskRecord($this->clientId);
        $batchDetail = new BatchAiSdrTaskDetail($this->clientId);
        foreach ($domainChunk as $chunk) {
            $batchRecordData = [];
            $batchDetailData = [];
            $domains = array_keys($chunk);
            $buyerPortraitList->setDomains($domains);
            $buyerPortraits = $buyerPortraitList->find();
            if (empty($buyerPortraits)) {
                \LogUtil::info("client_id {$this->clientId} hutch buyer portrait is empty", [
                    "domain" => json_encode($domains)
                ]);
                continue;
            }
            $buyerPortraits = array_column($buyerPortraits, null, 'domain');
            $time = xm_function_now();
            foreach ($buyerPortraits as $domain => $buyerPortrait) {
                $detail = $domainMap[$domain]??[];
                $detailId = $detail['detail_id']??0;
                $leadId = $detail['lead_id']??0;
                if (empty($detailId) || empty($leadId)) {
                    \LogUtil::info("client_id {$this->clientId} hutch detail_id {$detailId} domain {$domain} not found");
                    continue;
                }

                $batchDetailData[$detailId] = [
                    'company_types' => PgsqlUtil::formatArray($buyerPortrait['company_type']??[]),
                    'product_ids' => PgsqlUtil::formatArray($buyerPortrait['main_products']??[]),
                ];

                $buyerPortrait['logo'] = '';
                $buyerPortrait['company_hash_id'] = '';
                $buyerPortrait['has_contacts'] = true;
                $backgroundReport = $this->formatInternalBackgrounCheckReport($buyerPortrait);
                $batchRecordData[] = [
                    'task_id' => $task->task_id,
                    'type' => Constant::RECORD_TYPE_BACKGROUND_CHECK,
                    'data' => $backgroundReport,
                    'lead_id' => $leadId,
                    'detail_id' => $detailId,
                    'estimate_time' => $time,
                    'executed_time' => $time,
                    'create_time' => $time,
                    'update_time' => $time,
                    'refer_type' => 0,
                    'refer_id' => 0
                ];
            }

            if (!empty($batchRecordData)) {
                $batchRecord->initFromData($batchRecordData);
                $batchRecord->getOperator()->create();
            }
            if (!empty($batchDetailData)) {
                $batchDetail->getOperator()->batchUpdate($batchDetailData);
            }
        }
    }

    public function getLeadDomain($homepage, $mainCustomerEmail):string {
        if (!empty($homepage)) {
            return \common\library\ai\service\RecommendService::getDomain($homepage);
        } else {
            if (!empty($mainCustomerEmail)) {
                return \common\library\ai\service\RecommendService::getDomain($mainCustomerEmail);
            } else {
                return '';
            }
        }
    }

    /**
     * 打标入库
    */
    public function processDig(AiSdrTask $task) {
        // 防并发打标数据
        $redis = \RedisService::cache();
        $key = sprintf(Constant::REDIS_AI_SDR_TASK_DIG_PROCESSING_KEY, $this->clientId, $task->task_id);
        $result = $redis->set($key, 1, 'EX', 3600, 'NX');
        if (!$result) {
            \LogUtil::info("client_id {$this->clientId} task {$task->task_id} is already processing");
            return;
        }

        try {
            $taskId = $task->task_id;
            $clientProfile = new ClientProfile($this->clientId);
            if ($clientProfile->isNew()) {
                throw new \RuntimeException(\Yii::t("ai", "Client profile not found. Please ensure the client profile is created before processing recommendations."));
            }
            $clientProfile->getFormatter()->setShowShortProductName(true);
            $clientProfile->getFormatter()->setNeedStrip(false);
            $clientProfileStr = json_encode($clientProfile->getAttributes(), JSON_UNESCAPED_UNICODE);

            $task->getFormatter()->displayTotalLimit(true);
            $limitInfo = $task->getAttributes(['total_limit', 'force_end_time', 'stat_total']);
            $alreadyDigCount = $limitInfo['stat_total']??0;

            $productUsageRecordFilter = new AiProductUsageRecordFilter($this->clientId);
            $productUsageRecordFilter->ai_sdr_task_id = $taskId;
            $productUsageRecordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $productUsageRecordFilter->select(['record_id', 'upstream_product']);
            $usageRecordIds = $productUsageRecordFilter->rawData();
            $usageRecordMap = array_column($usageRecordIds, 'upstream_product', 'record_id');

            $digRecordFilter = new AiSdrDigRecordFilter($this->clientId);
            $digRecordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $digRecordFilter->lead_quality = Constant::LEAD_QUALITY_UNKNOWN;
            $digRecordFilter->usage_record_id = new In(array_keys($usageRecordMap));
            $count = $digRecordFilter->count();
            $pageSize = 10;
            $page = ceil($count / $pageSize);
            $digRecordFilter->select(['record_id', 'domain', 'usage_record_id']);

            // 使用注入的LeadAutoArchive服务
            $leadAutoArchive = $this->leadAutoArchive;
            if (method_exists($leadAutoArchive, 'setOrigin')) {
                $leadAutoArchive->setOrigin(Origin::SYS_ORIGIN_AI_SDR);
            }
            if (method_exists($leadAutoArchive, 'setContactLimit')) {
                $leadAutoArchive->setContactLimit(10);
            }
            if (method_exists($leadAutoArchive, 'setScene')) {
                $leadAutoArchive->setScene(\common\library\discovery\Constant::SCENE_NORMAL);
            }

            $agent = new SdrLeadQualityAnalysisAgent($this->clientId, $this->userId);
            // 使用注入的RecommendApi服务
            $recommendApi = $this->recommendApi;
            $batchDigRecord = new BatchAiSdrDigRecord($this->clientId);
            $invalidCount = 0;
            $successDetailIds = [];
            $flag = SdrDetailExecutor::$needBackgroundRecheck;
            for ($i = 0; $i < $page; $i++) {
                $digRecordFilter->limit($pageSize, $i);
                $result = $digRecordFilter->rawData();
                $domainMap = array_column($result, null, 'domain');
                try {
                    $domains = array_keys($domainMap);
                    \LogUtil::info("client_id {$this->clientId} task_id {$taskId} process lead quality domains ".json_encode($domains));
                    !empty($domains) && $buyerProfiles = $recommendApi->getCompanyProfileByDomains($domains);
                } catch (\Exception $exception) {
                    \LogUtil::info("client_id {$this->clientId} task_id {$taskId} get company profile fail because ".$exception->getMessage());
                    continue;
                }
                $buyerProfiles = array_column($buyerProfiles??[], null, 'domain');

                $updateData = [];
                foreach ($buyerProfiles as $domain => $buyerProfile) {
                    $record = $domainMap[$domain]??[];
                    $recordId = $record['record_id']??0;
                    if (empty($recordId)) {
                        \LogUtil::info("cannot find source dig record_id for domain {$domain}");
                        continue;
                    }

                    if ($this->reachTotalLimit($task, $alreadyDigCount, count($successDetailIds))) {
                        break 2;
                    }

                    $usageProductId = $record['usage_record_id']??0;
                    $matchUpstreamProduct = $usageRecordMap[$usageProductId]??0;
                    $buyerProfileArr = Arr::only($buyerProfile, ['domain', 'company_profile', 'employee_count', 'founding_time', 'business_strength', 'company_type', 'industry',
                        'main_products', 'product_category', 'country_code', 'public_homepage']);
                    $buyerProfileArr['match_upstream_product'] = $matchUpstreamProduct;
                    try {
                        $answer = $agent->process([
                            'client_profile' => $clientProfileStr,
                            'buyer_profile' => json_encode($buyerProfileArr, JSON_UNESCAPED_UNICODE),
                        ]);

                        $answerRecordId = $answer['record_id']??0;
                        $answer = $answer['answer']??[
                            'domain' => $domain,
                            'lead_quality' => Constant::LEAD_QUALITY_UNKNOWN,
                            'reason' => []
                        ];
                        $leadQuality = $answer['lead_quality'];
                        $updateData[] = [
                            'record_id' => $recordId,
                            'lead_quality' => $leadQuality,
                            'ai_service_record_id' => $answerRecordId,
                        ];

                        switch ($leadQuality) {
                            case Constant::LEAD_QUALITY_HIGH:
                            case Constant::LEAD_QUALITY_MEDIUM:
                                $reason  = is_array($answer['reason'])? $answer['reason'] : [$answer['reason']];
                                $detailId = $this->createLead($task, $usageProductId, $leadAutoArchive, $domain, $buyerProfile, $leadQuality, $reason, !$flag);
                                break;
                            case Constant::LEAD_QUALITY_LOW:
                            default:
                                $invalidCount += 1;
                                break;
                        }
                        if (!empty($detailId)) {
                            $successDetailIds[] = $detailId;
                        }
                    } catch (\Exception $exception) {
                        \LogUtil::info("analyze lead quality for domain {$domain} fail because ".$exception->getMessage());
                        continue;
                    }
                }
                !empty($updateData) && $batchDigRecord->getOperator()->batchUpdateByPk($updateData);
                \LogUtil::info("client_id {$this->clientId} task_id {$taskId} process lead quality, total {$count}, current page {$i}, valid count ".count($successDetailIds).", invalid count {$invalidCount}");
            }

            \LogUtil::info("client_id {$this->clientId} task_id {$taskId} process lead quality success, success detail count ".count($successDetailIds));

            if (!empty($successDetailIds) && $flag) {
                if ($task->source == Constant::TASK_SOURCE_AI_SDR) {
                    $dailyLimit = Client::getClient($this->clientId)->getExtentAttribute(Client::EXTERNAL_KEY_AI_SDR_DAILY_LIMIT, Constant::DAILY_LIMIT);
                } else {
                    $dailyLimit = Client::getClient($this->clientId)->getExtentAttribute(Client::EXTERNAL_KEY_CRM_EP_AI_SDR_DAILY_LIMIT, Constant::DAILY_LIMIT_FOR_LITE_FREE);
                }
                $dailyLimit = Client::getClient($this->clientId)->getExtentAttribute(Client::EXTERNAL_KEY_AI_SDR_DAILY_LIMIT, Constant::DAILY_LIMIT);
                $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
                $detailFilter->id = new In($successDetailIds);
                $detailFilter->task_id = $taskId;
                $detailFilter->limit($dailyLimit); // ai背调一天只有 100 次
                $batchDetail = $detailFilter->find();
                $batchDetail->getFormatter()->baseInfoSetting();
                $rawData = $batchDetail->getAttributes();

                $detailLeads = SdrLeadDetail::initFromArray($this->clientId, $rawData);
                \LogUtil::info("start background recheck for task_id {$taskId} and lead_id ".json_encode(array_column($rawData, 'lead_id')));
                $executor = new SdrDetailExecutor($this->clientId, $this->userId);
                $executor->setTask($task);
                $executor->process($detailLeads, Constant::DETAIL_STATUS_BACKGROUND_CHECKING);
            }
        } catch (\Exception $exception) {
            \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} process lead quality failed, error ".$exception->getMessage());
            throw $exception;
        } finally {
            $redis->del([$key]);
        }
    }

    protected function formatInternalBackgrounCheckReport($buyerProfile) {
        $domain = $buyerProfile['domain']??'';
        $companyName = $buyerProfile['company_name']??'';
        $params= [
            'company_name' => $companyName,
            'company_homepage' => $domain,
        ];
        $employeesCount = $buyerProfile['employee_count']??'';
        $employeesCount = explode("-", $employeesCount);
        $homepage = is_array($buyerProfile['public_homepage'] ?? '') ? $buyerProfile['public_homepage'] : json_decode($buyerProfile['public_homepage']??"", true);
        $background = [
            'background_task_id' => 0,
            'params' => $params,
            'report' => [
                'logo' => $buyerProfile['logo']??'',
                'params' => $params,
                'country' => $buyerProfile['country_code'],
                'homepage' => $homepage[0]??($domain??''),
                'has_company' => 1,
                'is_internal' => 1,
                'social_media' => [],
                'main_products' => implode(",", $buyerProfile['main_products']??[]),
                'industry_ids' => $buyerProfile['industry_ids']??[],
                'company_hash_id' => $buyerProfile['company_hash_id']??'',
                'company_description' => $buyerProfile['company_profile']??'',
                'has_contacts' => $buyerProfile['has_contacts']??0,
                'employees_max' => intval($employeesCount[0]??0),
                'employees_min' => intval($employeesCount[1]??0),
            ],
        ];

        return $background;
    }

    /**
     * 挖掘任务先挖掘->打标->保存线索
     * 孵化任务入库线索->获取买家画像->打标
    */
    public function dig(AiSdrTask $task) {
        if ($task->isDig()) {
            $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
            $detailFilter->task_id = $task->task_id;
            $currentDetailCount = $detailFilter->count();
            if ($currentDetailCount >= Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT) {
                \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} 潜客数量已达上限");
                $task->current_stage = Constant::AI_SDR_STAGE_REACHABLE;
                $task->update_time = xm_function_now();
                $task->update(['current_stage', 'update_time']);
                return;
            } elseif ($task->task_status == Constant::AI_SDR_TASK_STATUS_DRAFT && $currentDetailCount >= Constant::AI_SDR_DRAFT_POTENTIAL_CUSTOMER_LIMIT) {
                \LogUtil::info("client_id {$this->clientId} 草稿任务 {$task->task_id} 潜客数量已达上限");
                return;
            }

            // 检查今日背调上限
            $redis = \RedisService::aiCache();
            $todayDetailCount = $redis->get(sprintf(Constant::TASK_DAILY_LIMIT_CACHE_KEY, $this->clientId, $task->task_id));
            if ($todayDetailCount < Constant::DAILY_LIMIT) {
                \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} has not reached daily limit for recommend company, current count {$todayDetailCount}");
                $job = new AiSdrHutchLeadJob($this->clientId, $task->task_id);
                $this->queueService->dispatch($job);
            }

            // 检查待分析的潜客数量，小于等于 1000 才投递推荐任务
            $usageRecordFilter = new AiProductUsageRecordFilter($this->clientId);
            $usageRecordFilter->ai_sdr_task_id = $task->task_id;
            $usageRecordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $usageRecordFilter->select(['record_id']);
            $usageRecordIds = $usageRecordFilter->rawData();

            $digRecordFilter = new AiSdrDigRecordFilter($this->clientId);
            $digRecordFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
            $digRecordFilter->lead_quality = Constant::LEAD_QUALITY_UNKNOWN;
            $digRecordFilter->usage_record_id = new In(array_column($usageRecordIds, 'record_id'));
            $digRecordCount = $digRecordFilter->count();
            if ($digRecordCount < Constant::DIG_THRESHOLD) {
                \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} has not enough company to value quality, continue recommending");
                $job = new AiSdrDigTaskJob($this->clientId, $task->task_id);
                $this->queueService->dispatch($job);
            }
        } else {
            \LogUtil::info("client_id {$this->clientId} task_id {$task->task_id} hutch existed leads, start getting buyer profiles");
            $job = new AiSdrHutchLeadJob($this->clientId, $task->task_id);
            QueueService::dispatch($job);
        }
    }

    public function saveBackgroundCheckResult(
        $taskId, $reportId, array $params, $isRead, $cheerCount, $referType, $referId, array $report, $status,
        $createTime,
    ) {
        $sdrLeadDetail = new AiSdrTaskDetail($this->clientId, $referId);
        if ($sdrLeadDetail->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","ai sdr记录不存在"));
        }

        $task = new AiSdrTask($this->clientId, $sdrLeadDetail->task_id);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }

        $builder = new BackgroundCheckBuilder($this->clientId, 0);
        $recordId = $builder->update($taskId, $referId, [
            'report_id' => $reportId,
            'is_read' => $isRead,
            'cheer_count' => $cheerCount,
            'report' => $report,
            'status' => $status,
        ]);
        \LogUtil::info("update ai background result success, record_id {$recordId}");

        if ($sdrLeadDetail->source == Constant::DETAIL_SOURCE_LEAD || $sdrLeadDetail->source == Constant::DETAIL_SOURCE_COMPANY){
            if (!$sdrLeadDetail->product_ids && !$sdrLeadDetail->company_types){
                $lead = new Lead($this->clientId, $sdrLeadDetail->lead_id);

                $homepage = $report['homepage'] ?? $lead->homepage ?? '';
                $company_name = $report['company_name'] ?? $lead->name ?? '';

                $domain = \common\library\ai\service\RecommendService::getDomain($homepage);
                if ($domain){
                    $crmBuyerPortraitJob = new AiSdrCrmBuyerPortraitJob($this->clientId, $this->userId, $referId, $domain, $company_name);
                    QueueService::dispatch($crmBuyerPortraitJob);
                }
            }
        }

        if ($sdrLeadDetail->source == Constant::DETAIL_SOURCE_DIG) { // 在挖掘到的时候已经打标入库
            $sdrLeadDetail->getFormatter()->baseInfoSetting();
            $sdrLeadDetail = $sdrLeadDetail->getAttributes();
            $sdrLeadDetail = (new SdrLeadDetail($this->clientId))->initFromSingle($sdrLeadDetail);

            $sdrDetailExecutor = new SdrDetailExecutor($this->clientId, $this->userId);
            $sdrDetailExecutor->setTask($task);
            $sdrDetailExecutor->process([$sdrLeadDetail], Constant::DETAIL_STATUS_VALIDATE_CONTACTS);
            return;
        }

        $job = new AiSdrLeadQualityAnalyzeJob($this->clientId, $this->userId, $sdrLeadDetail->task_id, $referId);
        $this->queueService->dispatch($job);
    }

    //todo:后续变更，暂定下面字段
    public function getTaskDetails($taskId)
    {
        return $this->getStatistic($taskId);
    }

    public function initDynamicTrail($taskId): array
    {
        $defaultRecord = array_reverse(Constant::DEFAULT_RECORD, true);

        $task = new AiSdrTask($this->clientId, $taskId);
        $endStage = $task->end_stage;
        $delivery_as = $task->delivery_as;

        if ($endStage == Constant::AI_SDR_STAGE_REACHABLE){
            unset($defaultRecord['createMarketingPlan']);
            unset($defaultRecord['marketingRound1']);
            unset($defaultRecord['marketingRound2']);
            unset($defaultRecord['marketingRound3']);
        }

        if ($endStage == Constant::AI_SDR_STAGE_MARKETING){
            unset($defaultRecord['marketingRound1']);
            unset($defaultRecord['marketingRound2']);
            unset($defaultRecord['marketingRound3']);
        }

//        $defaultRecord['deliverySuccess'] = [
//            'type' => Constant::RECORD_TYPE_DELIVERY_SUCCESS,
//            'status' => 0,
//            'delivery' => [
//                'type' => $delivery_as == Constant::AI_SDR_TASK_DELIVERY_TYPE_LEAD ? Constant::RECORD_REFER_TYPE_LEAD : Constant::RECORD_REFER_TYPE_CONTACT,
//            ]
//        ];

        return $defaultRecord;
    }


    public function getBuyerPortrait(array $domains)
    {
        $api = new InnerApi('okki_leads_service');
        $api->setHttpMethod(InnerApi::HTTP_METHOD_POST);

        $buyerPortraitResult = $api->call('getBuyerPortrait', [
            'domains' => $domains
        ]);

        return $buyerPortraitResult['data'] ?? [];
    }

    public function addContactByLeadIds($task_id, array $lead_ids): int
    {
        //过滤已经添加的线索
        $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;
        $detail = $detailFilter->rawData();

        $addedLeadIds = collect($detail)->pluck('lead_id')->toArray();  //已经添加的线索
        $notAddedLeadIds = array_diff($lead_ids, $addedLeadIds);    //未添加的线索

        if (empty($notAddedLeadIds)){
            return 0;
        }

        $leadList = new \common\library\lead_v3\LeadList($this->userId);
        $leadList->setLeadId($notAddedLeadIds);
        $leadList->getFormatter()->setSpecifyFields(['lead_id', 'homepage']);
        $leadData = $leadList->find();

        foreach ($leadData as &$leadDatum) {
            $homepage = $leadDatum['homepage'] ?? '';
            if (!$homepage) {
                continue;
            }

            $domain = \common\library\ai\service\RecommendService::getDomain($homepage);
            $leadDatum['domain'] = $domain ?? '';
        }

        $domains = collect($leadData)->where('domain', '!=', '')->pluck('domain')->toArray();

        if ($domains){
            //todo crm买家画像
        }

        //批量写入潜客列表
        $batchData = [];
        foreach ($leadData as $datum){
            $lead_id = $datum['lead_id'];
            $domain = $datum['domain'] ?? '';

            $batchData[] = [
                'task_id' => $task_id,
                'lead_id' => $lead_id,
                'user_id' => $this->userId,
                'source' => Constant::DETAIL_SOURCE_LEAD,
                'product_ids' => $domain ? $buyerPortrait[$domain]['product_ids'] ?? [] : [],
                'company_types' => $domain ? $buyerPortrait[$domain]['company_type'] ?? [] : [],
            ];
        }

        if ($batchData){
            $batchTaskDetails = new BatchAiSdrTaskDetail($this->clientId);
            $batchTaskDetails->initFromData($batchData);
            $count = $batchTaskDetails->getOperator()->create(['task_id', 'lead_id', 'user_id', 'source', 'product_ids', 'company_types']);

            // 潜客开发过程
            $detailFilter->lead_id = new In($notAddedLeadIds);
            $detailFilter->select(['id', 'lead_id']);
            $detailData = $detailFilter->rawData();

            $batchRecordData = [];
            foreach ($detailData as $detailDatum) {
                $batchRecordData[] = [
                    'task_id' => $task_id,
                    'detail_id' => $detailDatum['id'],
                    'lead_id' => $detailDatum['lead_id'],
                    'type' => Constant::RECORD_TYPE_ADD_LEAD,
                    'data' => [],
                    'estimate_time' => xm_function_now(),
                    'executed_time' => xm_function_now(),
                    'create_time' => xm_function_now(),
                    'update_time' => xm_function_now(),
                    'refer_type' => Constant::RECORD_REFER_TYPE_LEAD,
                    'refer_id' => $detailDatum['lead_id'],
                ];
            }

            $batchRecords = new BatchAiSdrTaskRecord($this->clientId);
            $batchRecords->initFromData($batchRecordData);
            $batchRecords->getOperator()->create();


            $batchTask = new BatchAiSdrTask($this->clientId);
            $batchTask->initFromData([[
                'task_id' => $task_id,
            ]]);
            $batchTask->getOperator()->update([
                'current_stage' => Constant::AI_SDR_STAGE_DIG, // 添加了新的，重置任务的当前处理阶段
                'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            ]);
            self::updateStatTotal($this->clientId, $task_id, $count);
        }

        return $count ?? 0;
    }

    public function addContactByCompanyIds($task_id, $company_ids)
    {
        $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;
        $detailData = $detailFilter->rawData();

        $addedCompanyIds = collect($detailData)->where('company_id', '!=', 0)->pluck('company_id')->toArray();
        $company_ids = array_diff($company_ids, $addedCompanyIds);

        if (empty($company_ids)){
            return 0;
        }

        $batchData = [];
        foreach ($company_ids as $company_id) {
            $batchData[] = [
                'task_id' => $task_id,
                'lead_id' => 0,
                'user_id' => $this->userId,
                'source' => Constant::DETAIL_SOURCE_COMPANY,
                'company_id' => $company_id,
            ];
        }

        if ($batchData) {
            $batchTaskDetails = new BatchAiSdrTaskDetail($this->clientId);
            $batchTaskDetails->initFromData($batchData);
            $count = $batchTaskDetails->getOperator()->create(['task_id', 'lead_id', 'user_id', 'source', 'company_id']);

            $batchTask = new BatchAiSdrTask($this->clientId);
            $batchTask->initFromData([[
                'task_id' => $task_id,
            ]]);
            $batchTask->getOperator()->update([
                'current_stage' => Constant::AI_SDR_STAGE_DIG, // 添加了新的，重置任务的当前处理阶段
                'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING,
            ]);
            self::updateStatTotal($this->clientId, $task_id, $count);
        }

        $job = new AiSdrAddCompanyJob($this->clientId, $this->userId, $task_id);
        $this->queueService->dispatch($job);

        return $count ?? 0;
    }

    public function addContactByFilters($task_id, $source, $filters, $criteria_type, $criteria = '')
    {
        if (empty($filters)){
            return 0;
        }

        if ($source == Constant::DETAIL_SOURCE_LEAD){
            $leadList = new LeadList($this->userId);
            $leadList->setFilters($filters, $criteria_type, $criteria);
            $leadList->setUserNum([0]);
            $leadList->getFormatter()->setSpecifyFields(['lead_id']);
            $leadData = $leadList->find();

            $lead_ids = collect($leadData)->pluck('lead_id')->toArray();

            return $this->addContactByLeadIds($task_id, $lead_ids);
        }

        if ($source == Constant::DETAIL_SOURCE_COMPANY){
            $companyList = new \common\library\customer_v3\company\list\CompanyList($this->userId);
            $companyList->setFilters($filters, $criteria_type, $criteria);
            $companyList->setUserNum([0]);
            $companyList->getFormatter()->setSpecifyFields(['company_id']);
            $companyData = $companyList->find();

            $company_ids = collect($companyData)->pluck('company_id')->toArray();

            return $this->addContactByCompanyIds($task_id, $company_ids);
        }

        return 0;
    }

    public function getClientProductList() : array {
        $clientProfile = new ClientProfile($this->clientId);
        $products = $clientProfile->standard_product;

        $productList = new ProductList();
        $productList->setProductId($products);
        $productList->setFields(['product_id', 'description']);
        return $productList->find();
    }

    public function getStatistic($task_id, $startDate = '', $endDate = '', $keyword = ''): array
    {
        //交付阶段
        $task = new AiSdrTask($this->clientId, $task_id);
        if ($task->isNew()){
            return [];
        }

        $endStage = $task->end_stage;
        $source = $task->source;
        $last_view_time = $task->last_view_time;

        $detailFilter = new \common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task_id;
        $detailFilter->stage = new NotEqual(Constant::AI_SDR_STAGE_NOT_DEFINED);
        $detailFilter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $detailFilter->lead_id = new NotEqual(0);

        if ($task->isDig()){
            $detailFilter->lead_quality = new \xiaoman\orm\database\data\EGT(Constant::LEAD_QUALITY_MEDIUM);  //挖掘场景过滤未分层、低价值潜客
        }

        $totalData = $detailFilter->rawData();
        $lead_ids = collect($totalData)->pluck('lead_id')->toArray();

        if (!$startDate && !$endDate){
            if ($last_view_time){
                if (substr($last_view_time, 0, 10) == date('Y-m-d')){
                    $startDate = date('Y-m-d 00:00:00');
                } else {
                    $startDate = $last_view_time;
                }
            } else {
                $startDate = '1970-01-01 00:00:01';
            }

            $endDate = date('Y-m-d 23:59:59');
        }

        if ($lead_ids){
            $recordFilter = new AiSdrTaskRecordFilter($this->clientId);
            $recordFilter->task_id = $task_id;
            $recordFilter->lead_id = new In($lead_ids);
            $recordFilter->enable_flag = 1;
            $recordFilter->executed_time = new \xiaoman\orm\database\data\DateRange($startDate, $endDate);
            $recordData = $recordFilter->rawData();

            $recordFilter->removeWhere(['executed_time']);
            $recordFilter->refer_type = Constant::RECORD_REFER_TYPE_EDM;
            $edmRecordData = $recordFilter->rawData();

            $refer_ids = collect($edmRecordData)->pluck('refer_id')->toArray();
            if ($refer_ids) {
                $refer_ids = implode(',', $refer_ids);

                $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
                $sql = "SELECT task_id, last_view_time, reply_time FROM tbl_group_mail WHERE task_id IN ($refer_ids) AND enable_flag = 1";
                $groupMailData = $db->createCommand($sql)->queryAll();
            }
        }

        return [
            'end_stage' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[$endStage],
            'type' => $source,
            'last_view_time' => $last_view_time,
            'list' => [
                [
                    'type' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[\common\library\ai_sdr\Constant::AI_SDR_STAGE_DIG],
                    'status' => 1,
                    'total' => collect($totalData)->count(),
                    'today' => collect($recordData ?? [])->where('type', Constant::RECORD_TYPE_ADD_LEAD)->count(),
                ],
                [
                    'type' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[\common\library\ai_sdr\Constant::AI_SDR_STAGE_REACHABLE],
                    'status' => $endStage >= Constant::AI_SDR_STAGE_REACHABLE ? 1 : 0,
                    'total' => collect($totalData)->where('stage', '>=', Constant::AI_SDR_STAGE_REACHABLE)->count(),
                    'today' => collect($recordData ?? [])->where('type', Constant::RECORD_TYPE_CHECK_CONTACTS)->count(),
                ],
                [
                    'type' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[\common\library\ai_sdr\Constant::AI_SDR_STAGE_MARKETING],
                    'status' => $endStage >= Constant::AI_SDR_STAGE_MARKETING ? 1 : 0,
                    'total' => collect($totalData)->where('stage', '>=', Constant::AI_SDR_STAGE_MARKETING)->count(),
                    'today' => collect($recordData ?? [])->where('type', Constant::RECORD_TYPE_CREATE_MARKET_PLAN)->count(),
                ],
                [
                    'type' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[\common\library\ai_sdr\Constant::AI_SDR_STAGE_EFFECTIVE],
                    'status' => $endStage >= Constant::AI_SDR_STAGE_EFFECTIVE ? 1 : 0,
                    'total' => collect($totalData)->where('stage', '>=', Constant::AI_SDR_STAGE_EFFECTIVE)->count(),
                    'today' => collect($groupMailData ?? [])->where('last_view_time', '>=', $startDate)->where('last_view_time', '<=', $endDate)->count(),
                ],
                [
                    'type' => \common\library\ai_sdr\Constant::AI_SDR_STAGE_MAP[\common\library\ai_sdr\Constant::AI_SDR_STAGE_HIGHVALUE],
                    'status' => $endStage >= Constant::AI_SDR_STAGE_HIGHVALUE ? 1 : 0,
                    'total' => collect($totalData)->where('stage', '>=', Constant::AI_SDR_STAGE_HIGHVALUE)->count(),
                    'today' => collect($groupMailData ?? [])->where('reply_time', '>=', $startDate)->where('reply_time', '<=', $endDate)->count(),
                ]
            ]
        ];
    }

    public function getTaskSetting($task_type = Constant::TASK_SOURCE_AI_SDR)
    {
        $filter = new AiSdrTaskFilter($this->clientId);
        $filter->source = $task_type;
        $filter->order('create_time','desc');
        $taskData = $filter->select(['task_id', 'end_stage', 'current_stage', 'email', 'is_incremental_supplement', 'is_automatic_conversion', 'delivery_as', 'delivery_status', 'tags', 'delivery_customer_stage'])->rawData();
        return $taskData[0] ?? [];
    }

    /**
     * 测试用function
    */
    public function sendEdm($taskId, $leadId, $round, $estimateTime, $email, $isKeyContact = 0)
    {
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }

        $recordFilter = new AiSdrTaskRecordFilter($this->clientId);
        $recordFilter->lead_id = $leadId;
        $recordFilter->type = Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN;
        $recordFilter->getQuery()->rawWhere(" AND jsonb_exists(data, 'round') and data->>'round' = '{$round}'");
        $recordFilter->limit(1);
        $count = $recordFilter->count();
        if (!$count) {
            throw new \RuntimeException(\Yii::t("ai", "sdr lead record not found"));
        }
        $batchRecord = $recordFilter->find();
        $batchRecord->getOperator()->update([
            'estimate_time' => $estimateTime,
        ]);

        $recordFilter->CleanWhere();
        $recordFilter->lead_id = $leadId;
        $recordFilter->type = Constant::RECORD_TYPE_CHECK_CONTACTS;
        $recordFilter->limit(1);
        $count = $recordFilter->count();
        if (!$count) {
            throw new \RuntimeException(\Yii::t('ai', "sdr lead record not found"));
        }
        $recordFilter->select(['record_id', 'data']);
        $result = $recordFilter->rawData()[0];
        $contacts = $result['data'];
        $contacts[] = [
            'name' => \EmailUtil::getEmailPrefix($email),
            'title' => '',
            'email' => $email,
            'isKeyContact' => $isKeyContact,
            'isEmailValid' => \common\library\auto_market\Constant::MAIL_LEVEL_GOOD,
            'source' => Constant::TASK_SOURCE_IMPORT,
        ];
        $batchRecord = new BatchAiSdrTaskRecord($this->clientId);
        $batchRecord->initFromData([[
            'record_id' => $result['record_id']
        ]]);
        $batchRecord->getOperator()->update([
            'data' => $contacts,
        ]);

        $leadCustomer = new LeadCustomer($this->clientId);
        $leadCustomer->name = \EmailUtil::getEmailPrefix($email);
        $leadCustomer->email = $email;
        $lead = new Lead($this->clientId, $leadId);
        $lead->setCustomerList([$leadCustomer]);
        $lead->save();

        $detail = new AiSdrTaskDetail($this->clientId);
        $detail->load([
            'client_id' => $this->clientId,
            'task_id' => $task->task_id,
            'lead_id' => $leadId,
            'enable_flag' => BaseObject::ENABLE_FLAG_TRUE,
        ]);
        $detail->getFormatter()->displayMarketingContact(true);
        $detail->getFormatter()->baseInfoSetting();
        $detail = $detail->getAttributes();

        $user = \User::getUserObject($this->userId);
        $sdrDetail = new SdrLeadDetail($this->clientId);
        $sdrDetail->initFromSingle($detail);
        $sdrExecutor = new SdrDetailExecutor($this->clientId, $this->userId);
        $sdrExecutor->setTask($task);
        $sdrExecutor->setEdmMailAddress($task->email);
        $sdrExecutor->setCurrentEdmCount($user->info()->getCurrentCount());
        $sdrExecutor->process([$sdrDetail], Constant::DETAIL_STATUS_EXECUTE_MARKETING_PLAN);
    }

    public function getCardList($task_id)
    {
        $cardMap = Constant::AI_SDR_CARD_MAP;

        if (!PrivilegeService::getInstance($this->clientId)->hasFunctional(PrivilegeConstants::FUNCTIONAL_LEAD_POOL)){
            unset($cardMap[Constant::AI_SDR_CARD_LEAD_SLEEP]);
        }

        $detailFilter = new AiSdrTaskDetailFilter($this->clientId);
        $detailFilter->task_id = $task_id;
        $detailFilter->enable_flag = 1;

        $res = [];
        foreach ($cardMap as $setting) {
            if ($setting['source'] == Constant::DETAIL_SOURCE_LEAD) {
                $leadList = new LeadList($this->userId);
                $leadList->setFilters($setting['filters'], $setting['criteria_type'], $setting['criteria']);
                $leadList->getFormatter()->setSpecifyFields(['lead_id']);
                $leadData = $leadList->find();
                $lead_ids = collect($leadData)->pluck('lead_id')->toArray();

                $detailFilter->source = Constant::DETAIL_SOURCE_LEAD;
                $detailFilter->lead_id = new In($lead_ids);

                $detailData = $detailFilter->select(['lead_id'])->rawData();
                $detail_lead_ids = collect($detailData)->pluck('lead_id')->toArray();

                $added = count($detail_lead_ids);
                $new = count(array_diff($lead_ids, $detail_lead_ids));
            }

            if ($setting['source'] == Constant::DETAIL_SOURCE_COMPANY){
                $companyList = new \common\library\customer_v3\company\list\CompanyList($this->userId);
                $companyList->setFilters($setting['filters'], $setting['criteria_type'], $setting['criteria']);
                $companyList->getFormatter()->setSpecifyFields(['company_id']);
                $companyData = $companyList->find();
                $company_ids = collect($companyData)->pluck('company_id')->toArray();

                $detailFilter->source = Constant::DETAIL_SOURCE_COMPANY;
                $detailFilter->company_id = new In($company_ids);

                $detailData = $detailFilter->select(['company_id'])->rawData();
                $detail_company_ids = collect($detailData)->pluck('company_id')->toArray();

                $added = count($detail_company_ids);
                $new = count(array_diff($company_ids, $detail_company_ids));
            }

            $res[] = [
                'card_type' => $setting['card_type'],
                'title' => $setting['title'],
                'sub_title' => $setting['sub_title'],
                'added' => $added ?? 0,
                'new' => $new ?? 0,
            ];
        }

        return $res;
    }

    public function getWorksReport($task_id, $beginDate, $endDate)
    {
        $potentialCustomerFilter =  new AiSdrTaskDetailFilter($this->clientId);
        $potentialCustomerFilter->task_id = $task_id;
        $potentialCustomerFilter->enable_flag = 1;
        $potentialCustomerFilter->create_time = new Range($beginDate,$endDate,true,false);
        $potentialCustomerCount = $potentialCustomerFilter->count();

        $pg = \PgActiveRecord::getDbByClientId($this->clientId);
        $typeContacts = Constant::RECORD_TYPE_CHECK_CONTACTS;
        $sql = "
            SELECT COUNT(*) AS contacts_count FROM tbl_ai_sdr_task_record 
            WHERE create_time >= '$beginDate' AND create_time < '$endDate'
            AND enable_flag = 1 
            AND type = $typeContacts 
            AND task_id = $task_id
            AND data ? 'isEmailValid'
            AND (data->>'isEmailValid')::int IN (1,2,3)
            AND client_id = {$this->clientId}
            AND task_id = {$task_id}
        ";
        $validContactCount = $pg->createCommand($sql)->queryScalar();

        $effectiveReachFilter = new AiSdrTaskRecordFilter($this->clientId);
        $effectiveReachFilter->select(['refer_id']);
        $effectiveReachFilter->task_id = $task_id;
        $effectiveReachFilter->enable_flag = 1;
        $effectiveReachFilter->type = Constant::RECORD_TYPE_EXECUTE_MARKET_PLAN;
        $effectiveReachFilter->create_time = new Range($beginDate,$endDate,true,false);
        $referIds = implode(',', array_map(function ($item) {
            return $item['refer_id'];
        }, $effectiveReachFilter->rawData()));
        $db = \ProjectActiveRecord::getDbByClientId($this->clientId);
        if (!empty($referIds)) {
            $sql = "SELECT COUNT(*) AS reach_count FROM tbl_group_mail WHERE delivery_time != '1970-01-01 00:00:01' 
                    AND task_id IN ({$referIds})";
            $effectiveReachCount = $db->createCommand($sql)->queryScalar();
        } else {
            $effectiveReachCount = 0;
        }

        $successfulDeliveryFilter = new AiSdrTaskRecordFilter($this->clientId);
        $successfulDeliveryFilter->task_id = $task_id;
        $successfulDeliveryFilter->enable_flag = 1;
        $successfulDeliveryFilter->create_time =  new Range($beginDate,$endDate,true,false);
        $successfulDeliveryFilter->type = Constant::RECORD_TYPE_DELIVERY_SUCCESS;
        $successfulDeliveryCount = $successfulDeliveryFilter->count();

        return [
            "potential_customer" => $potentialCustomerCount,
            "valid_contact" => $validContactCount,
            "email_effective_reach" => $effectiveReachCount,
            "successful_delivery" => $successfulDeliveryCount
        ];
    }

    public function pauseOrStartTask(int $taskId) {
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException(\Yii::t("ai","jRSr"));
        }
        if ($task->task_status == Constant::AI_SDR_TASK_STATUS_FINISHED) {
            throw new \RuntimeException(\Yii::t('ai', "ai sdr任务已完成"));
        }
        $svc = new \common\library\ai_sdr\MessageService($this->clientId, $taskId);
        if ($task->task_status == Constant::AI_SDR_TASK_STATUS_PAUSED) {
            $task->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
            $svc->newResume();
        } else {
            $task->task_status = Constant::AI_SDR_TASK_STATUS_PAUSED;
            $task->pause_time = xm_function_now();
            $svc->newPausedMessage();
        }
        $task->update_time = xm_function_now();
        $task->update(['task_status', 'update_time']);
    }

    public function createAiSdrTask($taskType)
    {
        if (!in_array($taskType, [\common\library\ai_sdr\Constant::TASK_SOURCE_AI_SDR, \common\library\ai_sdr\Constant::TASK_SOURCE_IMPORT, Constant::TASK_SOURCE_CRM_EP])) {
            throw new \RuntimeException("type {$taskType} is not supported");
        }
        $taskFilter = new AiSdrTaskFilter($this->clientId);
        $taskFilter->enable_flag = 1;
        $taskFilter->source = $taskType;
        $task = $taskFilter->select(['task_id'])->rawData();
        if (!empty($task)) {
            return $task[0]['task_id'];
        }
        $newTask = new \common\library\ai_sdr\task\AiSdrTask($this->clientId);
        $newTask->source = $taskType;
        $newTask->create();
        return $newTask->task_id;
    }

    public function createDraftDigTask($source = Constant::TASK_SOURCE_AI_SDR) {
        //已存在ai_sdr类型的任务，不再创建草稿态的任务
        $filter = new AiSdrTaskFilter($this->clientId);
        $filter->source = $source;
        $filter->enable_flag = 1;
        $filter->select(['task_id']);
        $existedTask = $filter->rawData();
        if (!empty($existedTask)) {
            return $existedTask[0]['task_id'];
        }

        $task = new AiSdrTask($this->clientId);
        $task->source = $source;
        $task->task_status = Constant::AI_SDR_TASK_STATUS_DRAFT;
        $task->end_stage = Constant::AI_SDR_STAGE_MARKETING;
        $task->create();
        return $task->task_id;
    }

    public function reachTotalLimit(AiSdrTask $task, $currentCount, $successCount)
    {
        if (($currentCount+$successCount) > Constant::AI_SDR_POTENTIAL_CUSTOMER_COUNT_LIMIT) {
            \LogUtil::info("client_id {$this->clientId} dig task 潜客数量已达上限");
            return true;
        }
        if ($task->task_status == Constant::AI_SDR_TASK_STATUS_DRAFT && ($successCount+$currentCount) >= Constant::AI_SDR_DRAFT_POTENTIAL_CUSTOMER_LIMIT) {
            \LogUtil::info("client_id {$this->clientId} 草稿 dig task 潜客数量已达上限");
            return true;
        }

        // TODO 上限改为task维度

        $task->getFormatter()->displayTotalLimit(true);
        $limitInfo = $task->getAttributes(['total_limit', 'force_end_time', 'stat_total']);
        if ($limitInfo['total_limit'] > 0) {
            //todo 总数未达标补偿
            if ($limitInfo['force_end_time'] && $limitInfo['force_end_time'] < xm_function_now()) {
                return true;
            }
            return $limitInfo['stat_total'] > $limitInfo['total_limit'];
        }
        return false;
    }

    public function getFrontedStageDetails($taskId, $originStage, $targetStage)
    {
        if ($originStage <= $targetStage) {
            return [];
        }

        $stageList = [];
        foreach (Constant::AI_SDR_STAGE_ARRAY as $stage){
            if ($stage > $targetStage && $stage <= $originStage) {
                $stageList[] = $stage;
            }
        }
        $stageList = implode(",", $stageList);
        $endTypeList = implode(',', [Constant::RECORD_TYPE_HATCH_SUCCESS, Constant::RECORD_TYPE_HATCH_FAILED, Constant::RECORD_TYPE_DELIVERY_SUCCESS, Constant::RECORD_TYPE_DELIVERY_FAILED, Constant::RECORD_TYPE_HATCH_ABORT]);

        $pg = \PgActiveRecord::getDbByClientId($this->clientId);
        $recordTable = 'tbl_ai_sdr_task_record';
        $detailTable = 'tbl_ai_sdr_task_detail';

        $sql = "
            SELECT * 
            FROM $detailTable AS a
            WHERE task_id = $taskId
            AND stage IN ($stageList)
            AND enable_flag = 1
            AND NOT EXISTS(
                SELECT 1
                FROM $recordTable AS b
                WHERE a.id = b.detail_id AND a.client_id = b.client_id AND b.type IN ($endTypeList) 
            )
        ";
         return  $pg->createCommand($sql)->queryAll();
    }

    public function cacheTaskStage(int $aiSdrTaskId, int $endStage)
    {
        $redis = \RedisService::sf();
        $value = $endStage;
        $key = sprintf(Constant::REDIS_AI_SDR_TASK_END_STAGE_KEY, $this->clientId, $aiSdrTaskId);
        $redis->set($key, $value, 'EX', 86400);
    }

    public function getCacheTaskStage($aiSdrTaskId)
    {
        $redis = \RedisService::sf();
        $key = sprintf(Constant::REDIS_AI_SDR_TASK_END_STAGE_KEY, $this->clientId, $aiSdrTaskId);
        return (int)$redis->get($key);
    }

    /**
     * @param $taskId
     * @param $originStage
     * @param $targetStage
     * @return void 异步交付潜客
     */
    public function confirmDeliveryFrontedStageDetails($taskId, $originStage,$targetStage)
    {
        $executor = new SdrDetailExecutor($this->clientId, $this->userId);
        $task = new AiSdrTask($this->clientId, $taskId);
        if ($task->isNew()) {
            throw new \RuntimeException("task_id($taskId) of client_id($this->clientId) is not existed");
        }
        $executor->setTask($task);
        $details = $this->getFrontedStageDetails($taskId, $originStage, $targetStage);
        foreach ($details as $detail) {
            //每次交付前检查状态
            $currentEndStage = $this->getCacheTaskStage($taskId);
            if (empty($currentEndStage)) {
                $currentEndStage = (new AiSdrTask($this->clientId, $taskId))->end_stage;
                $this->cacheTaskStage($taskId, $currentEndStage);
            }
            if ($detail['stage'] < $currentEndStage) {
                continue;
            }
            //交付
            $detail['product_ids'] = explode(",", trim($detail['product_ids'], '{}'));
            $detail['company_types'] = explode(",", trim($detail['company_types'], '{}'));

            $sdrLeadDetail = new SdrLeadDetail($this->clientId);
            $sdrLeadDetail->initFromSingle($detail);
            $executor->checkTaskStage($sdrLeadDetail, $detail['stage'], $originStage);
        }
    }

   // todo 待优化
    public static function updateStatTotal($clientId, $taskId, $incrCount)
    {
        if ($incrCount == 0) {
            return;
        }
        $sql = "UPDATE tbl_ai_sdr_task SET stat_total = stat_total + $incrCount WHERE task_id = $taskId";
        \PgActiveRecord::getDbByClientId($clientId)->createCommand($sql)->execute();
    }
}