<?php


namespace common\library\product_v2\search;


use xiaoman\orm\metadata\Metadata;

/**
 * Trait GetSearcher
 * @package common\library\product_v2\search
 * @property  $clientId
 * @method Metadata getMetadata
 */
trait GetSearcher
{
    /**
     * @var EsFilter
     */
    private $_searcher;

    public function getSearcher()
    {
        if (!$this->_searcher)
            $this->_searcher = $this->initSearcher();

        return $this->_searcher;
    }

    /**
     * @return EsFilter
     */
    protected function initSearcher() : EsFilter
    {
        $class = $this->getMetadata()::searcher();
        return new $class($this->clientId);
    }

    public function search(array $params, array $option=[])
    {
        //暂未考虑高亮场景
        $searcher = $this->getSearcher();
        $ids = $searcher->paramsMapping($params)->findIds();
        if( empty($ids) )
        {
            $this->rawWhere(' and 1=0 ');
        }else
        {
            $objectIdKey = $this->getMetadata()::objectIdKey();
            $this->{$objectIdKey} = $ids;
        }

        $searcher->resetSetting();
    }
}
