<?php

/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2021/12/14 18:10:01
 */

namespace common\library\trade_document\page\user;

use common\components\BaseObject;
use common\library\orm\pipeline\operator\UpdateIndexTask;
use Constants as CommonConst;
use common\library\trade_document\Constants;
use xiaoman\orm\common\Operator;

/**
 * @method PageFilter getFilter()
 */
class PageOperator extends Operator
{
    const TASK_LIST = [
        'update_index'  => [
            'require_fields' => ['page_id'],
            'module_type'    => CommonConst::TYPE_TRADE_DOCUMENT,
            'task_class'     => UpdateIndexTask::class,
        ],
    ];

    public function read()
    {
        $this->execute(['is_viewed' => Constants::PAGE_VIEWED]);
    }

    public function delete()
    {
        if (!$this->object->getListAttributes()) {
            return 0;
        }

        $user = $this->object->getDomainHandler();
        $userId = $user ? $user->getUserId() : 0;

        $updateData = [
            'enable_flag' => BaseObject::ENABLE_FLAG_FALSE,
            'update_user' => $userId,
            'update_time' => date('Y-m-d H:i:s'),
        ];

        return $this->standardProcess(
            $updateData,
            $this->afterOperate(CommonConst::TRADE_DOC_INDEX_TYPE_DELETE, $updateData, $userId, true)
        );
    }

    public function modifyLinks(array $link_ids)
    {
        if (!$this->object->getListAttributes()) {
            return 0;
        }

        $user = $this->object->getDomainHandler();
        $userId = $user ? $user->getUserId() : 0;

        $updateData = [
            'link_ids' => $link_ids,
            'update_user' => $userId,
            'update_time' => date('Y-m-d H:i:s'),
        ];

        return $this->standardProcess(
            $updateData,
            $this->afterOperate(CommonConst::TRADE_DOC_INDEX_TYPE_UPDATE, $updateData, $userId, true)
        );
    }

    public function moveToCategory(int $categoryId)
    {
        if (!$this->object->getListAttributes()) {
            return 0;
        }

        $user = $this->object->getDomainHandler();
        $userId = $user ? $user->getUserId() : 0;

        $updateData = [
            'category' => $categoryId,
            'update_user' => $userId,
            'update_time' => date('Y-m-d H:i:s'),
        ];

        return $this->standardProcess(
            $updateData,
            $this->afterOperate(CommonConst::TRADE_DOC_INDEX_TYPE_UPDATE, $updateData, $userId, true)
        );
    }


    /**
     * @param int $operateType @see CommonConst::TRADE_DOC_INDEX_TYPE_CREATE...
     * @param array $setValue
     * @param $opUserId
     * @param bool $returnTask
     * @return array[]|void
     */
    public function afterOperate(int $operateType, array $setValue, $opUserId, bool $returnTask = false)
    {
        $tasks = [
            'update_index' => ['update_type' => $operateType, 'user_id' => $opUserId],
        ];

        if ($returnTask) {
            return $tasks;
        }

        $this->afterInsertProcess($setValue, $tasks);
    }
}
