<?php
/**
 * Created by PhpStorm.
 * User: ganyaoyao
 * Date: 18/5/9
 * Time: 11:27
 */
namespace common\library\export;

use common\library\custom_field\company_field\CompanyField;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\CustomFieldService;
use common\library\customer_v3\company\orm\Company;
use common\library\object\field\FieldConstant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\library\common\PublicTypeMetadata;
use common\library\setting\user\UserSetting;
use common\library\util\AreaUtil;
use common\library\util\Speed;
class CompanyCustomerExport extends Export {

    //客户导出公司系统字段
    public static $companyMap = [];

    public static $customerMap = [];

    public static $contactMap = [];

    public static $allCompanyFieldMap = [];
    public static $allCustomerFieldMap = [];
    public static $customerFieldSetting = [];
    public static $companyFieldSetting = [];

    public static $contactType = [];

    protected static $country_list = array();

    public static $dateField = [
        'tips_latest_update_time',//Tips最近更新时间
        'latest_write_follow_up_time',//最近「写跟进」时间
        'edm_time',//最近发EDM时间
        'send_mail_time',//最近发件时间
        'receive_mail_time',//最近收件时间
        'latest_send_ali_tm_time',//最近发送阿里TM消息时间
        'latest_receive_ali_tm_time',//最近收到阿里TM消息时间
        'latest_receive_ali_trade_time',//最近收到阿里TM询盘时间
        'transaction_order_first_time',//首次成交订单日期
        'latest_whatsapp_time',//最近WhatsApp沟通时间
        'success_opportunity_first_time',//首次赢单日期
        'latest_transaction_order_time',//最近成交订单日期
        'latest_success_opportunity_time',//最近赢单日期
    ];

    //是否来自os系统的断约导出
    protected bool $from_os = false;
    public function __construct($params)
    {
        if (isset($params['os_task_id']) && $params['os_task_id']) {
            $this->from_os = true;
        }
    }

    /*
     * @deprecated 已废弃
     */
    public  function getCompanyMap(){
        $map = [
            'archive_time' => 0,
            'serial_id' => 1,
            'customer_name' => 2,
            'short_name' => 3,
            'owner' => 6,
            'trail_status' => 7,
            'star' => 8,
            'group_name' => 9,
            'pool_name' => 10,
            'tel' => 11,
            'origin_name' => 12,
            'biz_type' => 13,
            'category_ids' => 14,
            'country' => 15,
            'annual_procurement' => 16,
            'intention_level' => 17,
            'scale_id' => 18,
            'address' => 19,
            'fax' => 20,
            'homepage' => 21,
            'customer_remark' => 22,
        ];

        //客户分组
        if (!PrivilegeService::getInstance(Export::$clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING])) {
            unset($map['pool_name']);
        }
        return $map;

    }

    public function getExportDataCount($params,$user){
        $companyObj = new CompanyListExportHelper($user->getUserId());
        $count = $companyObj->countCompanyList($params,$user);
        return $count;
    }

    public function insertTableData(&$fp,&$failCount, $params,$dataCount)
    {

        $userInfo = Export::$userInfo;
        $pageSize = Export::$maxPageSize;
        $taskId = Export::$taskId;
        Speed::log(sprintf("export@insertDataStart:task_id[%s] count[%s]", $taskId,$dataCount));

        $pageCount = ceil($dataCount / $pageSize);
        $pageCount = $pageCount > 0 ? $pageCount : 1;

        $companyObj = new CompanyListExportHelper($userInfo->getUserId());
        for ($i = 1; $i <= $pageCount; $i++) {
            echo $i . '/' . $pageCount;
            echo "\n";
            $curPage = $i;
            $company_list = $companyObj->findCompanyList($params, $userInfo, $curPage, $pageSize);
            if(!$company_list || !is_array($company_list)){
                \LogUtil::info(sprintf("CompanyCustomerExport@CompanyEmpty:task_id[%s] params[%s] curPage[%s] pageSize[%s]", self::$taskId,json_encode($params),$curPage,$pageSize));
                return false;
            }
            $this->insertCompanyCustomerData($fp,$failCount,$company_list,$userInfo);
        }
    }

    public function insertCompanyCustomerData(&$fp, &$failCount, $company_list, $user){

        $company_map =  self::$companyMap;
        $customer_map = self::$customerMap;
        $contact_map = self::$contactMap;
        $company_external_map = self::$companyFieldSetting;
        $customer_external_map = self::$customerFieldSetting;

        $columnCount = count($company_map)+count($customer_map)+count($contact_map);

        foreach ($company_list as $company) {
            $is_public = empty($company['user_id']??[]);
            $company_row = array_fill(0, $columnCount, '');

            $company_external_field_map = array_column($company['external_field_data'],null,'id');

            foreach ($company_map as $id => $index) {
                
                // 字段权限
                $noPermission = false;
                foreach ($company['field_privilege_stats'] ?? [] as $fieldPrivilegeStat) {
                    if ($fieldPrivilegeStat['refer_type'] != \Constants::TYPE_COMPANY) {
                        continue;
                    }
                    if (in_array($id, $fieldPrivilegeStat['disable'])) {
                        $company_row[$index] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
                        $noPermission = true;
                        break;
                    }
                }
                if ($noPermission) {
                    continue;
                }

                if(in_array($id, $company_external_map)){
                    $extFieldId = $id;
                    if(!isset($company_external_field_map[$extFieldId])){
                        continue;
                    }
                    $extFieldValue = $company_external_field_map[$extFieldId];

                    if (is_array($extFieldValue['value']) && $extFieldValue['value']) {

                        //2022-09-14 产品需求：导出时，对于下拉多选的历史 删除/修改选项 一并进行导出
                        //判断下拉多选的值，是否在系统设置的选项里面
                        /*if ($extFieldValue['field_type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                            $extFieldValue['value'] = Helper::filterExtInfo($extFieldValue['ext_info'], $extFieldValue['value']);

                        } else {
                            $extFieldValue['value'] = join(',', $extFieldValue['value']);
                        }*/

                        $extFieldValue['value'] = join(',', $extFieldValue['value']);

                        //判断下拉的值，是否在系统设置的选项里面
                    } else if ($extFieldValue['value'] && $extFieldValue['field_type'] == CustomFieldService::FIELD_TYPE_SELECT) {
                        //对于下拉单选选的历史 删除/修改选项 一并进行导出
                        //$extFieldValue['value'] = Helper::filterExtInfo($extFieldValue['ext_info'], array($extFieldValue['value']));
                    } else if (is_array($extFieldValue['value']) && !$extFieldValue['value']) {
                        $extFieldValue['value'] = "";
                    }

                    $company_row[$index] = $extFieldValue['value'];
                    continue;
                }
                if (!isset($index))
                    continue;
                switch ($id) {
                    case 'origin':
                    case 'origin_list':
                        $company_row[$index] =  $company['origin_name'] ?? '';
                        break;
                    case 'archive_time':
                        $company_row[$index] = date('Y/m/d H:i:s',strtotime($company[$id]));
                        break;
                    case 'owner':
                        $owner_name_list = array_column($company[$id], 'name');
                        $company_row[$index] = implode(';', $owner_name_list);
                        break;
                    case 'last_edit_user':
                    case 'create_user':
                        $company_row[$index] = $company[$id]['name'] ?? '';
                        break;
                    case 'serial_id':
                        $serial_id = $company[$id] ?? '';
                        $company_row[$index] = (str_starts_with($serial_id, '0') && is_numeric($serial_id)) ? "'".$serial_id : $serial_id;
                        break;
                    case 'trail_status':
                        $company_row[$index] = $company[$id]['status_name'] ?? '';
                        break;
                    case 'category_ids':
                        $category_list = array();
                        if (!empty($company[$id])) {
                            foreach ($company[$id] as $ids) {
                                $category = \Category::getEnNamesByIds($ids);
                                $category = implode('-', $category);
                                $category_list[] = $category;
                            }
                        }
                        $company_row[$index] = implode(';', $category_list);
                        break;
                    case 'country':
                        if (!array_key_exists($company[$id], self::$country_list)) {
                            $country_data = \CountryService::checkNameInTable($company[$id]);
                            if ($country_data) {
                                self::$country_list[$company[$id]] = $country_data;
                            }
                        }
                        $company_row[$index] = self::$country_list[$company[$id]]['country_name'] ?? $company[$id];
                        break;

                    case 'province':
                        $company_row[$index] = AreaUtil::getProvinceIfExists($company[$id]) ?? $company[$id];
                        break;
                    case 'city':
                        $company_row[$index] = AreaUtil::getCityIfExists($company[$id]) ?? $company[$id];
                        break;
                    case 'tel':
                        $tel_area_code = isset($company['tel_area_code']) ? $company['tel_area_code'] : '';
                        if($tel_area_code){
                            $tel_area_code = '+'.$tel_area_code.'-';
                        }
                        $tel = $tel_area_code.$company[$id];
                        $company_row[$index] = $tel ? "'".$tel : "";
                        break;
                    case 'fax':
                        $company_row[$index] = ' '.$company[$id];
                        break;
                    case 'scale_id':
                        //规模Map
                        $scaleIdMap = \common\library\customer_v3\company\orm\Company::SCALE_MAP;
                        $company_row[$index] = $scaleIdMap[$company[$id]] ?? '';
                        break;
                    case 'intention_level':
                        $company_row[$index] = \CustomerOptionService::intentionLevelMap()[$company[$id]] ?? '';
                        break;
                    case 'annual_procurement':
                        $company_row[$index] = \CustomerOptionService::annualProcurementMap()[$company[$id]] ?? '';
                        break;
                    case 'tag':
                        $tag_list = array_column($company[$id], 'tag_name');
                        $company_row[$index] = implode(';', $tag_list);
                        break;
                    case 'product_group_ids':
                        $product_group_list = array_column($company[$id], 'name');
                        $company_row[$index] = implode(';', $product_group_list);
                        break;
                    case 'score':
                        $company_row[$index] = $company[$id]['total'] ?? '';
                        break;
                    case 'assess':
                        Export::exportAssess($company_row, $index, $company);
                        break;
                    case 'last_trail':
                        $company_row[$index] = \common\library\customer\Helper::getLastTrailValue($company[$id] ?? []);
                        break;
                    case 'swarm_list':
                        if (!$is_public) {
                            $swarm_list = array_column($company[$id], 'name');
                            $company_row[$index] = implode(';', $swarm_list);
                        } else {
                            $company_row[$index] = '';
                        }
                        break;
                    case 'ali_store_id':
                        $alibaba_store_list = array_column($company['source_detail']['alibaba_store_info'] ?? [],'store_name');
                        $site_list = array_column($company['source_detail']['site_info'] ?? [],'web_site_name');
                        $store_str = !empty($alibaba_store_list) ? '店铺: '.implode(',', $alibaba_store_list).' ' : '';
                        $site_str = !empty($site_list) ? '网站: '.implode($site_list) : '';
                        $company_row[$index] = $store_str.$site_str;
                        break;
                    case 'archive_type':
                        $company_row[$index] = \CustomerOptionService::archiveTypeMap()[$company[$id]] ?? '';
                        break;
                    case 'last_remark_trail':
                        $company_row[$index] = $company[$id]['data']['content'] ?? '';
                        break;
                    case 'public_type':
                        if($is_public) {
                            $company_row[$index] = PublicTypeMetadata::getExtraDataMap(\Constants::TYPE_COMPANY)[$company[$id]] ?? '';
                        }else{
                            $company_row[$index] = '';
                        }
                        break;
                    case 'public_reason':
                        if (!empty($company[$id]) && $is_public) {
                            $company_row[$index] = $company[$id];
                        } else {
                            $company_row[$index] = '';
                        }
                        break;
                    case 'main_lead_id':
                        $company_row[$index] = $company['lead']['lead_name'] ?? '';
                        break;
                    case 'next_move_to_public_date':
                        $company_row[$index] = ($is_public || $company[$id] == '1970-01-01 00:00:00') ? '' : $company[$id];
                        break;
                    //处理日期字段显示1970/1/1 问题
                    case in_array($id, self::$dateField):
                        $company_row[$index] = ($company[$id] == '1970-01-01 00:00:00') ? '' : $company[$id];
                        break;
                    case 'pin_user_list':
                        $company_row[$index] = implode(';',array_column($company[$id], 'name') ?? []);
                        break;
					case 'alibaba_last_owner':
						$temp = $company['alibaba_last_owner_info'] ?? [];
						$names = [];
						foreach ($temp as $value) {
							if (empty($value['last_owner_info']) || (empty($value['last_owner_info']['first_name']) && empty($value['last_owner_info']['last_name']))) {
								continue;
							}
							$names[] = $value['last_owner_info']['first_name'] . $value['last_owner_info']['last_name'];
						}
						$company_row[$index] = !empty($names) ? implode(';', array_unique($names)) : '';
						break;
					case 'alibaba_user_id':
						$temp = $company['alibaba_user_info'] ?? [];
						$names = [];
						foreach ($temp as $value) {
							if (empty($value['owner_info']) || (empty($value['owner_info']['first_name']) && empty($value['owner_info']['last_name']))) {
								continue;
							}
							$names[] = $value['owner_info']['first_name'] . $value['owner_info']['last_name'];
						}
						$company_row[$index] = !empty($names) ? implode(';', array_unique($names)) : '';
						break;
                    default:
                    $company_row[$index] = empty($company[$id]) ? '' : $company[$id];
                }
            }

            //判断如果没有选择联系人字段时，直接导出
            if (empty($customer_map) && empty($contact_map))
            {
                $res = fputcsv($fp, $company_row);
                if ($res == false) {
                    $failCount++;
                }
                continue;
            }


            $customerList = new \common\library\customer_v3\customer\CustomerList($user->getClientId());
            $customerList->setCompanyId($company['company_id']);
            $customer_data = $customerList->find();

            if(empty($customer_data)){
                \LogUtil::info(sprintf("CompanyCustomerExport@customerEmpty:task_id[%s] company_id[%s]", self::$taskId,$company['company_id']));
            }
            foreach ($customer_data as $customer) {
                $customer_row = [];
                $customer_external_field_map = $customer['external_field_data'];
                foreach ($customer_map as $id => $index) {
                    
                    // 字段权限
                    $noPermission = false;
                    foreach ($company['field_privilege_stats'] as $fieldPrivilegeStat) {
                        if ($fieldPrivilegeStat['refer_type'] != \Constants::TYPE_CUSTOMER) {
                            continue;
                        }
                        if (in_array($id, $fieldPrivilegeStat['disable'])) {
                            $customer_row[$index] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
                            $noPermission = true;
                            break;
                        }
                    }
                    if ($noPermission) {
                        continue;
                    }
                    
                    if(in_array($id,$customer_external_map)){
                        $extFieldId = $id;
                        if(!isset($customer_external_field_map[$extFieldId])){
                            continue;
                        }
                        $extFieldValue = $customer_external_field_map[$extFieldId];
                        if (is_array($extFieldValue)) {
                            $extFieldValue = join(',', $extFieldValue);
                        }
//                        if (is_array($extFieldValue['value']) && $extFieldValue['value']) {
//
//                            //2022-09-14 产品需求：导出时，对于下拉多选的历史 删除/修改选项 一并进行导出
//                            //判断下拉多选的值，是否在系统设置的选项里面
//                            /*if ($extFieldValue['field_type'] == CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
//                                $extFieldValue['value'] = Helper::filterExtInfo($extFieldValue['ext_info'], $extFieldValue['value']);
//
//                            } else {
//                                $extFieldValue['value'] = join(',', $extFieldValue['value']);
//                            }*/
//                            $extFieldValue['value'] = join(',', $extFieldValue['value']);
//
//                            //判断下拉的值，是否在系统设置的选项里面
//                        } else if ($extFieldValue['value'] && $extFieldValue['field_type'] == CustomFieldService::FIELD_TYPE_SELECT) {
//                            $extFieldValue['value'] = Helper::filterExtInfo($extFieldValue['ext_info'], array($extFieldValue['value']));
//                        } else if (is_array($extFieldValue['value']) && !$extFieldValue['value']) {
//                            $extFieldValue['value'] = "";
//                        }
//
//                        $customer_row[$index] = $extFieldValue['value'];
                        $customer_row[$index] = $extFieldValue;
                        continue;
                    }

                    if (!isset($index)) continue;
                   /* //公海分组没有，索引要往后减
                    if (!PrivilegeService::getInstance(Export::$clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING])) {
                        if($index>=9){
                            $index--;
                        }
                    }*/
                    if (empty($customer[$id])) {
                        $customer_row[$index] = '';
                    } elseif ($id == 'gender') {
                        $gender = '';
                        if ($customer[$id] == 1) {
                            $gender = '男';
                        } elseif ($customer[$id] == 2) {
                            $gender = '女';
                        }
                        $customer_row[$index] = $gender;
                    }  elseif ($id == 'post_grade') {
                        $post_grade = '';
                        if ($customer[$id] == 1) {
                            $post_grade = '普通职员';
                        } elseif ($customer[$id] == 2) {
                            $post_grade = '中层管理者';
                        } elseif ($customer[$id] == 3) {
                            $post_grade = '高层管理者';
                        }
                        $customer_row[$index] = $post_grade;
                    } elseif($id == 'tel'){
                        $tel_area_code = isset($customer['tel_area_code']) ? $customer['tel_area_code'] : '';
                        if($tel_area_code)
                            $tel_area_code = $tel_area_code.' ';
                        $customer_row[$index] = $tel_area_code.$customer[$id];
                    } elseif($id == 'tel_list'){
                        $customerTelMap = [];
                        if(!is_array($customer[$id])){
                            $customer[$id] = json_decode($customer[$id], true);
                        }
                        foreach ($customer[$id] as $telItem) {
                            if(count($telItem) > 1 && $telItem[0]??''){
                                $customerTelMap[] = '＋' . implode('-', $telItem);
                            }else{
                                $customerTelMap[] = implode('', $telItem);
                            }
                        }
                        $telList = implode(';', $customerTelMap);
                        $customer_row[$index] = $telList ? "'".$telList : "";//避免excel 转义
                    } else {
                        $customer_row[$index] = $customer[$id];
                    }
                }

                if (!empty($contact_map)) {
                    $platformMap = [];
                    foreach($customer['contact'] as $contactItem)
                    {
                        if(!isset($contactItem['type']) || !isset($contactItem['value']) || !is_string($contactItem['value'])){//脏数据处理
                            continue;
                        }
                        $platform = $contactItem['type'];
                        $platformMap[$platform][] = $contactItem['value'] ? "'".$contactItem['value'] : "";
                    }

                    foreach ($contact_map as $platform => $index)
                    {
                        if (in_array('contact', $fieldPrivilegeStat['disable'] ?? [])) {
                            $customer_row[$index] = \Yii::t('privilege', FieldConstant::FIELD_VALUE_EXPORT_MASK);
                            continue;
                        }
                        $customer_row[$index] = isset($platformMap[$platform]) ? implode(';', $platformMap[$platform]) : '';
                    }
                }

                $row = array_replace($company_row, $customer_row);

                $res = fputcsv($fp, $row);
                if ($res == false) {
                    $failCount++;
                }
            }
        }

    }


    public function getFirstLIneData($type, $fields)
    {
        $first_line = array();

        //获取所有字段
        $allField = \common\modules\internal\library\export\Helper::getCustomerAllField(Export::$clientId,Export::$userInfo->getUserId());

        if ($this->from_os) {
            $exportFields = $allField;
        } else {

            $setting = new UserSetting(Export::$clientId, Export::$userInfo->getUserId(), UserSetting::CUSTOMER_EXPORT_MAP);
            $value = $setting->getValue();

            if (!empty($value)) {
                $exportFields = json_decode($value,true);
            } else {
                $exportFields = \CustomerOptionService::customerDefaultExportMap();
            }

            //校验是否有效字段
            $allFieldMap = array_map(function ($id, $type) {
                return $type .'_'. $id;
            }, array_column($allField, 'id'), array_column($allField, 'type'));

            $exportFields = array_values(array_filter($exportFields, function ($item) use($allFieldMap) {
                return in_array($item['type'].'_'.$item['id'], $allFieldMap);
            }));
        }

        $externalField = ["id", "name", "require", "hint", "field_type", "disable_flag", "ext_info"];
        //客户自定义字段
        $companyFieldSetting = \common\library\customer\Helper::getExternalFieldSetting(self::$clientId, \Constants::TYPE_COMPANY, 0, $externalField);
        //$companyExternalFields = array_column($companyFieldSetting,'id');
        $companyExternalMap = array_column($companyFieldSetting,'id','name');
        
        //联系人自定义字段
        $customerFieldSetting = \common\library\customer\Helper::getExternalFieldSetting(self::$clientId, \Constants::TYPE_CUSTOMER, 0, $externalField);
        $customerExternalMap = array_column($customerFieldSetting,'id','name');

        //社交平台
        $socialPlatform = array_keys(\CustomerOptionService::customerContactMap());
        
        $index = 0;

        foreach ($exportFields as $field) {
            $fieldId = $field['id'];
            $fieldName = $field['name'];
            $fieldType = $field['type'];
            if ($fieldType == \Constants::TYPE_COMPANY) {
                //客户自定义字段
                if (in_array($fieldId, $companyExternalMap)) {
                    $first_line[] = $fieldName; //Excel 输出的表头
                    self::$companyMap[$fieldId] = $index;
                    self::$companyFieldSetting[] = $fieldId;
                    $index++;
                    continue;
                }
                //客户字段
                $first_line[] = $fieldName; //Excel 输出的表头
                self::$companyMap[$fieldId] = $index;
                $index++;

                if ($fieldId == 'assess' && Export::checkAssessSwitch()) {
                    $first_line = array_merge($first_line, [
                        \Yii::t('field', 'first_total_assess'),
                        \Yii::t('field', 'second_total_assess')
                    ]);
                    $index = $index + 2;
                }
                continue;
            }

            if ($fieldType == \Constants::TYPE_CUSTOMER) {
                //社交平台单独处理
                if ($fieldId == 'contact') {
                    foreach ($socialPlatform as $platform)
                    {
                        $first_line[] = ucfirst($platform); //Excel 输出的表头
                        self::$contactMap[$platform] = $index;
                        $index++;
                    }
                    continue;
                }

                if (in_array($fieldId, $customerExternalMap)) {
                    $first_line[] = $fieldName;
                    self::$customerMap[$fieldId] = $index;
                    self::$customerFieldSetting[] = $fieldId;
                    $index++;
                    continue;
                }
                $first_line[] = $fieldName; //Excel 输出的表头
                self::$customerMap[$fieldId] = $index;
                $index++;
            }

        }

        if (empty($first_line)) {
            throw new \Exception(\Yii::t('customer', 'Please set export file field'));
        }
        return $first_line;
    }

    /*
      * 获取Excel的表头数据
      * @param  int $   type   类型：1:客户 4:线索跟进 5:员工动态统计 6下属统计 7客户统计
      * @param  array  $fields 请求字段信息
      * @return array  excel表头的字段
      * */
    public function getFirstLineDataOld($type, $fields)
    {
        $first_line = array();
        $isNeedSettingField = 1;
        self::$companyMap = $this->getCompanyMap();

        //有传入指定导出的字段，就导出需要导出的系统字段
        if ($fields && is_array($fields)) {
            foreach ($fields as $key => $value) {
                if (!in_array($value, self::$allCompanyFieldMap)) {
                    continue;
                }
                if (self::$allCompanyFieldMap[$value]) {
                    $allFieldMap = array_merge(self::$companyMap, self::$customerMap);
                    $first_line[$allFieldMap[$value]] = self::$allCompanyFieldMap[$value]; //Excel 输出的表头
                    self::$searchField[] = $key; //最终输出的字段映射
                }
            }
        } else {

            //不传入，导出所有系统字段
            $allFieldMap = array_merge(self::$companyMap, self::$customerMap);
            foreach (self::$allCompanyFieldMap as $key => $value) {

                //公海分组
                if ($key == 'pool_name' && !PrivilegeService::getInstance(Export::$clientId)->hasFunctional([PrivilegeConstants::FUNCTIONAL_COMPANY_POOL_SETTING])) {
                    unset(self::$allCompanyFieldMap['pool_name']);
                    continue;
                }
                $first_line[$allFieldMap[$key]] = $value;
                self::$searchField[] = $key;
            }
        }

        $externalField = ["id", "name", "require", "hint", "field_type", "disable_flag", "ext_info"];
        //是否需要输出自定义字段
        if ($isNeedSettingField) {
            $countFirstLine = count($first_line) - 1;
            //获取自定义字段
            $companyFieldSetting = \common\library\customer\Helper::getExternalFieldSetting(self::$clientId, \Constants::TYPE_COMPANY, 0, $externalField);
            foreach ($companyFieldSetting as $setting) {
                $first_line[] = $setting['name'];
                $countFirstLine++;
                self::$companyMap[$setting['name']] = $countFirstLine;
                self::$companyFieldSetting[$setting['id']] = $setting['name'];
            }


            $countFirstLine = count($first_line) - 1;
            //获取自定义字段
            $customerFieldSetting = \common\library\customer\Helper::getExternalFieldSetting(self::$clientId, \Constants::TYPE_CUSTOMER, 0, $externalField);
            foreach ($customerFieldSetting as $setting) {
                $first_line[] = $setting['name'];
                $countFirstLine++;
                self::$customerMap['ext_' . $setting['name']] = $countFirstLine;
                self::$customerFieldSetting[$setting['id']] = $setting['name'];
            }

        }
        //重新排序
        ksort($first_line);
        //重新建立key值
        $first_line = array_values($first_line);
        return $first_line;
    }

    public function formatShowMsg($params)
    {
        $conditionString = '';
        return $conditionString;
    }

}
