<?php

namespace common\library\search\field;

use common\library\search\builder\BooleanBuilder;
use common\library\search\builder\ExistBuilder;
use common\library\search\builder\TermBuilder;
use common\library\workflow\WorkflowConstant;

class ContactField extends NestedField implements CustomEncoder
{

    public $properties = [
        'type' => [
            'class' => KeywordField::class,
        ],
        'value' => [
            'class' => AccountField::class,
        ],
    ];

    public $primaryKey = 'type';

    public function getBuilder($value, $operator, $valueType = null)
    {
        $builder = new BooleanBuilder();

        if ($valueType) {
            switch ($operator) {
                case WorkflowConstant::FILTER_OPERATOR_IS_NULL:
                    //需要将本层级的must_not转成上层级的must_not
                    $builder->transToMustNot(true)->must(new TermBuilder('type', $valueType));
                    return $builder;
                default:
                    $builder->must(new TermBuilder('type', $valueType));
                    break;
            }
        } elseif ($operator == WorkflowConstant::FILTER_OPERATOR_IS_NULL) {
            //需要将本层级的must_not转成上层级的must_not
            $builder->transToMustNot(true)->must(new TermBuilder('value', $valueType));
            return $builder;
        }

        if (strtolower($valueType ?? '') == 'whatsapp') {
            $subBuilder = new BooleanBuilder();
            $subBuilder->should($this->useProperty('value', $value, $operator));
            $subBuilder->should(new TermBuilder('value.tel', $value));
            $subBuilder->minimumShouldMatch(1);
            $builder->must($subBuilder);
        } else {
            $builder->must($this->useProperty('value', $value, $operator));
        }

        return $builder;
    }

    public static function encode($data, $fieldSetting)
    {
        if (!isset($data['contact'])) {
            return [];
        }
        $contact = json_decode($data['contact'], true);
        $contact = is_array($contact) ? $contact : [];
        foreach ($contact as &$contactDetail) {
            isset($contactDetail['value']) && $contactDetail['value'] = is_string($contactDetail['value']) ? $contactDetail['value'] : '';
        }

        return $contact;
    }
}