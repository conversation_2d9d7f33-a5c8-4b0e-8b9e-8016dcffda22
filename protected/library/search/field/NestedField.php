<?php

namespace common\library\search\field;

use common\library\search\SearchConstant;
use common\library\search\SearchIndexBuilder;

class NestedField extends AbstractField
{
    public $propertyType = SearchConstant::PROPERTY_TYPE_NESTED;

    public $properties = [];

    public $primaryKey;

    public function setProperties(array $properties)
    {
        $this->properties = array_replace($this->properties, $properties);
    }

    protected function getFilters()
    {
    }

    protected function getAnalyzer()
    {
    }

    protected function getCharFilters()
    {
    }

    protected function getFieldList()
    {
        if (!empty($this->properties) && empty($this->fieldList)) {
            foreach ($this->properties as $fieldId => $fieldSetting) {
                $this->fieldList[] = SearchIndexBuilder::initField($fieldId, $fieldSetting, implode('.', array_filter([$this->path, $this->fieldId])));
            }
        }

        return $this->fieldList;
    }

    public function getPropertySetting()
    {
        return [$this->fieldId => $this->getProperty()];
    }

    protected function getProperty()
    {
        $property = [
            'type' => SearchConstant::PROPERTY_TYPE_NESTED,
            'properties' => [],
        ];
        foreach ($this->getFieldList() as $field) {
            if (!is_a($field, AbstractField::class)) {
                continue;
            }
            $property['properties'] = array_merge($property['properties'], $field->getPropertySetting());
        }

        return $property;
    }

    protected function getNormalizer()
    {
    }

    protected function getTokenizer()
    {
    }

    public function getBuilder($value, $operator, $valueType = null)
    {
    }

    protected function useProperty($propertyName, $value, $operator)
    {
        if (isset($this->properties[$propertyName])) {
            $class = $this->properties[$propertyName]['class'] ?? '';

            if ($class && class_exists($class)) {
                return (new $class($propertyName))->getBuilder($value, $operator);
            }
        }
    }
}