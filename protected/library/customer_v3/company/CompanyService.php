<?php

namespace common\library\customer_v3\company;

use common\library\customer_v3\company\orm\BatchCompany;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\company\orm\CompanyMetadata;
use common\library\customer_v3\customer\orm\CustomerFilter;
use common\library\object\field\service\ScopeUserFieldService;
use common\library\swarm\SwarmService;
use common\library\util\PgsqlUtil;
use LogUtil;
use phpDocumentor\Reflection\Types\This;
use xiaoman\orm\database\data\InArray;

class CompanyService {
    
    
    protected int           $clientId;
    protected \User         $opUser;
    protected CompanyFilter $filter;
    
    public function __construct($clientId) {
        
        $this->clientId = $clientId;
        
        \User::isLogin() && $this->opUser = \User::getLoginUser();
    }
    
    public function getFilter() {
        
        if (empty($this->filter)) {
            
            $this->filter = new CompanyFilter($this->clientId);
        }
        
        return $this->filter;
    }
    
    public function getMaxSerialId() {
        
        $filter = $this->getFilter();
        
        $filter->rawWhere(" AND serial_id ~ '^[0-9]+$' ");
        
        $filter->setRawSelect(' MAX(CAST(serial_id AS DECIMAL)) max_number ');
        
        $list = $filter->rawData(false);
        
        return current($list)['max_number'] ?? 0;
    }
    
    public function getPoolCustomerByIds($customerIds) {
        
        if (empty($customerIds)) {
            return [];
        }
        
        $customerFilter = new CustomerFilter($this->clientId);
        
        $customerFilter->select(['company_id', 'customer_id', function () { return "lower(email) as email"; }]);
        $customerFilter->user_id = new InArray('');
        $customerFilter->customer_id = $customerIds;
        
        $companyFilter = new CompanyFilter($this->clientId);
        $companyFilter->select(['ali_store_id']);
        
        $customerFilter->initJoin()->leftJoin($companyFilter)->on('company_id', 'company_id');
        
        return $customerFilter->rawData();
    }
    
    public static function buildUpdateScopeUsers(int $clientId, array $row, $returnAsPgStr = false): string|array|null
    {
        $scopeUserService = new ScopeUserFieldService($clientId, new CompanyMetadata($clientId));
        $result = $scopeUserService->buildScopeUserIds([$row]);
        LogUtil::info('[buildUpdateScopeUsersSql] company_scope_user_ids, result: ' . json_encode($result));
        
        $scopeUsers = reset($result)['scope_user_ids'] ?? null;
        if (is_null($scopeUsers))    {
            return null;
        }

        return $returnAsPgStr ? "'" . PgsqlUtil::formatArray($scopeUsers) . "'" : $scopeUsers;
    }
    
}
