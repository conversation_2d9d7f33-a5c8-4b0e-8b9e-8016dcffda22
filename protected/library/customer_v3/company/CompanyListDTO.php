<?php

namespace common\library\customer_v3\company;

use common\library\customer_v3\company\list\CompanyList;
use common\library\validation\Validator;
use common\library\workflow\WorkflowConstant;
use LogUtil;
use ReflectionClass;
use Throwable;

/**
 * actionCompanyList
 */
class CompanyListDTO {
    
    
    public string  $scenario                      = CompanyList::SCENARIO_DEFAULT;
    public int     $search_model                  = \Constants::SEARCH_MODEL_SEARCHER;
    public string  $keyword                       = '';
    public string  $search_field                  = '';
    public int     $search_duplicate_flag         = 0;
    public int     $pin                           = 0;
    public array   $status_id                     = [];
    public array   $country                       = [];
    public array   $province                      = [];
    public array   $city                          = [];
    public array   $biz_type                      = [];
    public array   $origin                        = [];
    public array   $group_id                      = [];
    public array   $pool_id                       = [];
    public string  $start_date                    = '';
    public string  $end_date                      = '';
    public array   $tags                          = [];
    public ?array  $user_num                      = [1, 2];
    public array   $star                          = [];
    public array   $category_ids                  = [];
    public int     $curPage                       = 1;
    public int     $pageSize                      = 20;
    public string  $sort_field                    = CompanyList::DEFAULT_SORT_FIELD;
    public string  $sort_type                     = 'desc';
    public string  $sort_scene                    = '';
    public int     $params_info                   = 0;
    public int     $recent_select_flag            = 0;
    public int     $will_public                   = 0;
    public int     $receive_period                = 0;
    public int     $get_count                     = 0;
    public string  $last_owner                    = '';
    public array   $company_field                 = [];
    public array   $customer_field                = [];
    public int     $tag_match_mode                = CompanyList::TAG_MATCH_MODE_SINGLE;
    public int     $compare_day                   = 0;
    public int     $compare_day_op                = CompanyList::LESS_THAN;
    public int     $acquired_company_day          = 0;
    public int     $show_all                      = 0;
    public string  $show_field_key                = '';
    public array   $user_id                       = [];
    public string  $scenario_data                 = '';
    public array   $archive_type                  = [];
    public array   $stage_type                    = [];
    public array   $archive_types                 = [];
    public string  $report_item_unique_key        = '';
    public array   $last_owner_ids                = [];
    public ?string $min_success_opportunity_count = '';
    public ?string $max_success_opportunity_count = '';
    public ?string $min_performance_order_count   = '';
    public ?string $max_performance_order_count   = '';
    public string  $deal_time_start_date          = '';
    public string  $deal_time_end_date            = '';
    public int     $get_contract_list             = 0;
    public int     $main_customer_flag            = 0;
    public string  $filter_ext_field              = '';
    public int     $high_light_flag               = 0;
    public array   $exclude_user_id               = [];
    public array   $create_user                   = [];
    public array   $last_edit_user                = [];
    public string  $edit_begin_time               = '';
    public string  $edit_end_time                 = '';
    public string  $private_begin_time            = '';
    public string  $private_end_time              = '';
    public string  $public_begin_time             = '';
    public string  $public_end_time               = '';
    public ?int    $max_release_count             = null;
    public ?int    $min_release_count             = null;
    public int     $alibaba_company_task_id       = 0;
    public int     $send_mail_day                 = 0;
    public int     $had_send_mail                 = 0;
    public array   $annual_procurement            = [];
    public array   $intention_level               = [];
    public array   $ali_store_id                  = [];
    public ?int    $duplicate_flag                = null;
    public string  $next_follow_up_begin_time     = '';
    public string  $next_follow_up_end_time       = '';
    public string  $recent_follow_up_begin_time   = '';
    public string  $recent_follow_up_end_time     = '';
    public int     $sub_user_id                   = 0;
    public int     $swarm_id                      = 0;
    public int     $ai_swarm_id                   = 0;
    public array   $swarm_list                    = [];
    public array   $filters                       = [];
    public int     $criteria_type                 = 1;
    public string  $criteria                      = '';
    public string  $temp_swarm_id                 = '';
    public array   $company_ids                   = [];
    public array   $time_filters                  = [];
    public array   $public_type                   = [];
    public ?int    $feed_type                     = null;
    public array   $growth_level                  = [];
    public array   $public_reason_id              = [];
    public array   $assess                        = [];
    public array   $pin_user_list                 = [];
    public array   $origin_list                   = [];
    public int     $filter_disable_fields         = 0;
    public int     $layout_flag                   = 0;      // 布局标识，如果前端传1，会在列表接口增加 xx_info 的key表示布局字段的格式
    public int     $only_editable                 = 0; // 只显示可编辑的
    
    
    public function __construct(array $data = []) {
        
        $data = $this->filterData($data);
        
        $this->validate($data);
        
        foreach ($data as $key => $value) {
            try {
                $this->{$key} = $value;
            } catch (Throwable $e) {
                LogUtil::warning("参数错误: {$e->getMessage()}", ['key' => $key, 'value' => $value]);
            }
        }
    }
    
    private function validate($data) {
        
        $rule = [
            'curPage'               => 'integer',
            'pageSize'              => 'integer|min:1|max:100',
            'sort_field'            => 'regex:/^\w{2,64}$/',
            'sort_type'             => 'string|in:asc,desc',
            'keyword'               => 'string|max:1000',
            'company_ids'           => 'array',
            'company_ids.*'         => 'required|integer',
            'swarm_list'            => 'array',
            'swarm_list.*'          => 'integer',
            'swarm_id'              => 'integer',
            'ai_swarm_id'           => 'integer',
            'user_id'               => 'array',
            'user_id.*'             => 'integer',
            'growth_level'          => 'array',
            'growth_level.*'        => 'integer',
            'tag_match_mode'        => 'integer',
            'public_reason_id'      => 'array',
            'public_reason_id.*'    => 'numeric',
            'public_type'           => 'array|required_with:public_reason_id',
            'public_type.*'         => 'numeric',
            'assess'                => 'array',
            'assess.*'              => 'numeric',
            'high_light_flag'       => 'integer',
            'pin_user_list'         => 'array',
            'pin_user_list.*'       => 'integer',
            'origin_list'           => 'array',
            'origin_list.*'         => 'numeric',
            'filter_disable_fields' => 'numeric|in:0,1',
            'compare_day'           => 'numeric|max:3650',
            'filters'               => 'array',
            'filters.*.field_type'  => 'required_with:filters',
            'filters.*.operator'    => 'required_with:filters',
            'only_editable'         => 'integer|in:0,1',
        ];
        
        
        (new Validator($data, $rule))->validate();
        
        
        foreach (($this->filters ?? []) as $item) {
            
            if (in_array($item['field_type'], [\common\library\field\Constant::FIELD_TYPE_DATE, \common\library\field\Constant::FIELD_TYPE_DATETIME])) {
                
                if (!in_array($item['operator'], [WorkflowConstant::FILTER_OPERATOR_EQUAL, WorkflowConstant::FILTER_OPERATOR_NOT_EQUAL]) && is_array($item['value'] ?? '')) {
                    
                    throw new \RuntimeException('WRONG SELECT EQUAL DATE PARAM');
                }
                if (!in_array($item['operator'], [WorkflowConstant::FILTER_OPERATOR_IS_NULL, WorkflowConstant::FILTER_OPERATOR_NOT_NULL]) && empty($item['value'])) {
                    
                    throw new \RuntimeException('WRONG SELECT NULL DATE PARAM');
                }
            }
        }
    }
    
    /**
     * 先过滤前端的非预期传参
     *
     * @param mixed $data
     * @return mixed
     */
    private function filterData(array $data): array {
        
        $reflectionClass = new ReflectionClass($this);
        
        foreach ($data as $field => &$datum) {
            
            is_array($datum) && $datum = array_filter($datum, function ($item) {
                
                return $item == 0 || !empty($item);
            });
            
            if (!$reflectionClass->hasProperty($field) || ($datum !== 0 && $datum !== '0' && empty($datum))) {
                
                unset($data[$field]);
                
                continue;
            }
            
            $property = $reflectionClass->getProperty($field);
    
            ($property->getType() == 'array' && !is_array($datum)) && $datum = (array)(json_decode($datum, true) ?? $datum);
        }
        
        return $data;
    }
    
    public function getParam(): array {
        
        $result = [];
        
        $reflectionClass = new ReflectionClass($this);
        
        foreach ($reflectionClass->getProperties() as $property) {
            
            $result[$property->getName()] = $this?->{$property->getName()};
        }
        
        return $result;
    }
}
