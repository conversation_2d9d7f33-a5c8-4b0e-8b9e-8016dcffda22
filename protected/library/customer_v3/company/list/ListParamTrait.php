<?php

namespace common\library\customer_v3\company\list;

use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\custom_field\CustomFieldService;
use common\library\customer\CompanySearchList;
use common\library\customer\field_unique\DuplicateFlagBuilder;
use common\library\customer_v3\common\database\CusTag;
use common\library\customer_v3\common\database\MergeQuery;
use common\library\customer_v3\company\orm\Company;
use common\library\customer_v3\customer\orm\CustomerFilter;
use common\library\opportunity\OpportunityList;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\search\SearchApi;
use common\library\setting\item\ItemSettingConstant;
use common\library\setting\library\origin\OriginApi;
use common\library\setting\library\swarm\Swarm;
use common\library\swarm\SwarmService;
use common\library\todo\Feed;
use common\library\todo\TodoConstant;
use common\library\util\SqlBuilder;
use common\library\workflow\WorkflowConstant;
use common\vendor\xiaoman\orm\src\database\data\LowerEqual;
use common\vendor\xiaoman\orm\src\database\data\LowerIn;
use xiaoman\orm\database\data\ArrayContains;
use xiaoman\orm\database\data\DateRange;
use xiaoman\orm\database\data\EGT;
use xiaoman\orm\database\data\GT;
use xiaoman\orm\database\data\InArray;
use xiaoman\orm\database\data\LT;
use xiaoman\orm\database\data\Range;

trait ListParamTrait {


    // 数据查看范围相关
    protected $activityType          = 0;
    protected $filterFreezeUserFlag  = false;
    protected $includeDeleteUserFlag = false;
    protected $excludeUserId;
    protected $isArchive             = true;
    protected $duplicateFlag;
    protected $lastOwner;
    protected $companyIds;
    protected $includeChildGroup     = true;
    protected $userIdFlag            = false; // 可见范围为全公司时走 <> '{}'
    protected $groupId;
    protected $poolId;
    private   $hasBuildParam         = false;
    protected $compareDay            = 0;
    protected $compareDayOp          = self::LESS_THAN;
    protected $compareMailTime       = 0;
    protected $hasMailTime           = false;
    protected $tel;
    
    // flag标识相关
    protected $hasCustomerEmail = null;
    
    // join 相关
    
    protected $customerWhere;
    protected $customerParams;
    
    protected $dataReader = false;
    
    // keyword搜索
    protected $keyword;
    protected $searchDuplicate;
    protected $processUserId;
    protected $ownerType;
    protected $userType;
    protected $searchResultIds;
    protected $searcher;
    protected $externalSearchFields = [];
    protected $sortBySearchScore    = false;
    protected $searchModel          = \Constants::SEARCH_MODEL_SEARCHER;
    
    // 日期时间范围筛选
    protected $beginArchiveTime;
    protected $endArchiveTime;
    protected $beginCreateTime;
    protected $endCreateTime;
    protected $beginOrderTime;
    protected $endOrderTime;
    protected $editBeginTime;
    protected $editEndTime;
    protected $beginUpdateTime;
    protected $endUpdateTime;
    protected $privateBeginTime;
    protected $privateEndTime;
    protected $publicBeginTime;
    protected $publicEndTime;
    protected $beginEdmTime;
    protected $endEdmTime;
    protected $beginFollowUpTime;
    protected $endFollowUpTime;
    protected $nextFollowUpBeginTime;
    protected $nextFollowUpEndTime;
    protected $recentFollowUpBeginTime;
    protected $recentFollowUpEndTime;
    
    // 数值范围筛选
    protected $maxReleaseCount;
    protected $minReleaseCount;
    
    // 国家地区筛选
    protected $country;
    protected $province;
    protected $city;
    
    // 配置项筛选
    protected $archiveType;
    /*
     *  兼容查询两种archive_type的情况
     *  场景：代办自动创建客户跳转客户列表
     */
    protected $archiveTypes;
    protected $bizType;
    
    protected $statusId;
    protected $assess;
    protected $growthLevel;
    protected $originList;
    protected $star        = [];
    protected $trailStatus;
    protected $categoryIds = [];
    
    // 客户标签筛选
    protected $tagUserId    = null;
    protected $tags         = [];
    protected $tagMatchMode = self::TAG_MATCH_MODE_COMPLETE;
    
    
    protected        $createUser;
    protected array  $lastEditUser = [];
    protected string $userIdSql;
    protected bool   $isPrivateOnly;
    
    
    protected $stageType;
    protected $minSuccessOpportunityCount;
    protected $maxSuccessOpportunityCount;
    protected $minPerformanceOrderCount;
    protected $maxPerformanceOrderCount;
    protected $startDealTime;
    protected $endDealTime;
    protected $startLatestWhatsappTime;
    protected $endLatestWhatsappTime;
    protected $startLatestWhatsappReceiveTime;
    protected $endLatestWhatsappReceiveTime;
    protected $startLatestWriteFollowUpTime;
    protected $endLatestWriteFollowUpTime;
    protected $intentionLevel;
    protected $annualProcurement;
    protected $aliStoreId;
    protected $showAllPermission = PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW;
    protected $alibabaCompanyTaskId;
    
    protected $swarmJoinFlag;
    protected $swarmId;
    protected $swarmList = [];
    protected $tempSwarmRuleInfo;
    private   $swarmAliasInc = 0;
    
    protected $publicType;
    protected $feedType = null;
    
    
    // 首次成单日期
    protected $beginTransactionOrderFirstTime;
    protected $endTransactionOrderFirstTime;
    
    // 首次赢单日期
    protected $beginSuccessOpportunityFirstTime;
    protected $endSuccessOpportunityFirstTime;
    // 客户编号
    protected $serialId;
    
    protected $companyId;
    protected $mainLeadId;
    
    protected $lastSql;
    
    protected $productGroupIds;
    protected $excludeCompanyIds;
    protected $statusEditType     = null;
    protected $acquiredCompanyDay = 0;//最近N天我获取（目前：转移、共享给我'2018-01-17'）的客户
    protected $opportunityFlag;
    protected $publicReasonId;
    protected $publicRemindDay    = 0;
    protected $receivePeriod      = 0;
    protected $sendMailDay        = 0;
    protected $hadSendMail        = false;

//    todo
    protected $leadArchiveBeginTime;
    protected $leadArchiveEndTime;
    
    protected $companyHashId;
    
    protected $aiSwarmId;
    
    
    abstract protected function buildUserIdSql();
    
    public function setKeyword($keyword) {
        
        $this->keyword = strtolower(trim($keyword));
    }
    
    /**
     * @param mixed $searchFields
     */
    public function setSearchFields($searchFields) {
        
        if (!is_array($searchFields)) {
            $searchFields = [$searchFields];
        }
        $this->searchFields = $searchFields;
        $this->searcher->setDefaultSearchFields(false);
    }
    
    /**
     * @param  $searchModel
     */
    public function setSearchModel( $searchModel) {
        
        $this->searchModel = $searchModel;
    }
    
    /**
     * @return CompanySearchList|SearchApi
     */
    public function getSearcher() {
        
        return $this->searcher;
    }
    
    public function setSearchDuplicate($searchDuplicate) {
        
        $this->searchDuplicate = $searchDuplicate;
        $this->searcher->setSearchDuplicate($searchDuplicate);
    }
    
    public function setExternalFields($type,  $externalFields) {
        
        if (!empty($externalFields)) {
            $this->externalSearchFields[$type] = $externalFields;
        }
    }
    
    public function setPublicRemindDay($day) {
        
        $this->publicRemindDay = $day;
    }
    
    public function setLastOwner($last_owner_id) {
        
        $this->lastOwner = is_array($last_owner_id) ? array_filter($last_owner_id, 'is_numeric') : $last_owner_id;
    }
    
    public function setIsArchive($archive) {
        
        $this->isArchive = $archive;
    }
    
    
    public function setGroupId($groupId, $includeChildGroup = true) {
        
        if ((is_array($groupId) && count($groupId = array_filter($groupId, 'is_numeric'))) || is_numeric($groupId)) {
            $this->groupId = $groupId;
        } else {
            $this->groupId = null;
        }
        
        $this->includeChildGroup = $includeChildGroup;
    }
    
    public function setPoolId($poolId) {
        
        if ((is_array($poolId) && count($poolId = array_filter($poolId, 'is_numeric'))) || (is_numeric($poolId) && $poolId >= 0)) {
            $this->poolId = $poolId;
        } else {
            $this->poolId = null;
        }
    }
    
    public function getSwarmAliasInc()
    {
        // 增加别名自增，避免重复
        return $this->swarmAliasInc++;
    }
    
    
    public function setStar($star) {
        
        if (is_numeric($star)) {
            $star = [$star];
        } elseif (!is_array($star)) {
            $star = [];
        }
        $this->star = array_filter($star, 'is_numeric');
    }
    
    public function setTel($tel) {
        
        if (!is_array($tel)) {
            $tel = [$tel];
        }
        
        $this->tel = $tel;
    }
    
    public function setGrowthLevel( $growthLevel) {
        
        $this->growthLevel = array_filter($growthLevel, 'is_numeric');
    }
    
    public function setTrailStatus($statusId) {
        
        if ((is_array($statusId) && count($statusId = array_filter($statusId, 'is_numeric'))) || is_numeric($statusId)) {
            $this->trailStatus = $statusId;
        } else {
            $this->trailStatus = null;
        }
    }
    
    public function setCountry($country) {
        
        $this->country = $country;
    }
    
    public function setProvince($province) {
        
        $this->province = $province;
    }
    
    public function setCity($city) {
        
        $this->city = $city;
    }
    
    
    /**
     * 设置搜索谁的tag（默认是初始化companyList时传的tag）
     *
     * @param $userId
     */
    public function setTagUserId($userId) {
        
        $this->tagUserId = $userId;
    }
    
    public function setTags( $tags) {
        
        $tags = array_map('intval', $tags);
        
        $this->tags = $tags;
    }
    
    public function setTagMatchMode($tagMatchMode) {
        
        $this->tagMatchMode = $tagMatchMode;
    }
    
    public function setCompareDay($day) {
        
        $this->compareDay = (int)$day;
    }
    
    public function setActivityType( $activityType) {
        
        $this->activityType = $activityType;
    }
    
    public function setCompareDayOp($flag) {
        
        $this->compareDayOp = $flag;
    }
    
    public function setCompareMailTime($compareMailTime) {
        
        $this->compareMailTime = $compareMailTime;
    }
    
    public function setHasMailTime($flag) {
        
        $this->hasMailTime = $flag;
    }
    
    public function setBizType($bizType, $enableEmptyBizType = false) {
        
        if ((is_string($bizType) && !empty($bizType)) || (is_array($bizType) && count($bizType = array_filter($bizType, function ($v) { return is_string($v) && !empty($v); })))) {
            $this->bizType = $bizType;
        } else {
            if ($bizType === null) $this->bizType = null;
            if ($this->bizType == '' && $enableEmptyBizType == true) $this->bizType = '';
        }
    }
    
    public function setCategoryIds($categoryIds) {
        
        foreach ($categoryIds as &$categoryIdsEnum) {
            if (is_string($categoryIdsEnum)) {
                $categoryIdsEnum = json_decode($categoryIdsEnum, true);
            }
            if (is_array($categoryIdsEnum)) {
                $categoryIdsEnum = array_map('intval', $categoryIdsEnum);
            } else {
                $categoryIdsEnum = intval($categoryIdsEnum);
            }
        }
        
        $this->categoryIds = $categoryIds;
    }
    
    
    public function setOriginList($originList): void {
        
        $this->originList = (new OriginApi($this->clientId))->getIdList(array_filter((array)$originList, function ($item) {
            
            return !empty($item) && is_numeric($item);
        }), true, true, true);
    }

    public function getOriginList()
    {
        return $this->originList;
    }
    
    public function setHasCustomerEmail($hasCustomerEmail) {
        
        $this->hasCustomerEmail = $hasCustomerEmail;
    }
    
    public function setExcludeUserId($userId) {
        
        $this->excludeUserId = is_array($userId) ? $userId : [$userId];
    }
    
    public function setCreateUser( $createUser) {
        
        $this->createUser = $createUser;
    }
    
    public function setLastEditUser( $lastEditUser) {
        
        $this->lastEditUser = $lastEditUser;
    }
    
    public function setMaxReleaseCount($maxReleaseCount) {
        
        if (is_numeric($maxReleaseCount)) {
            $this->maxReleaseCount = $maxReleaseCount;
        }
    }
    
    public function setMinReleaseCount($minReleaseCount) {
        
        if (is_numeric($minReleaseCount)) {
            $this->minReleaseCount = $minReleaseCount;
        }
    }
    
    public function setArchiveTime($begin, $end) {
        
        if (!empty($begin)) {
            $this->beginArchiveTime = $begin;
        }
        
        if (!empty($end)) {
            $this->endArchiveTime = $end;
        }
    }
    
    public function setBeginArchiveTime($begin) {
        
        if (!empty($begin)) {
            $this->beginArchiveTime = $begin;
        }
    }
    
    public function setEndArchiveTime($end) {
        
        if (!empty($end)) {
            $this->endArchiveTime = $end;
        }
    }
    
    public function setOrderTime($begin, $end) {
        
        if (!empty($begin)) {
            $this->beginOrderTime = $begin;
        }
        
        if (!empty($end)) {
            $this->endOrderTime = $end;
        }
    }
    
    public function setUpdateTime($begin, $end) {
        
        if (!empty($begin)) {
            $this->beginUpdateTime = $begin;
        }
        
        if (!empty($end)) {
            $this->endUpdateTime = $end;
        }
    }
    
    public function setFollowUpTime($begin, $end) {
        
        if (!empty($begin)) {
            $this->beginFollowUpTime = $begin;
        }
        
        if (!empty($end)) {
            $this->endFollowUpTime = $end;
        }
    }
    
    public function setEdmTime($begin, $end) {
        
        if (!empty($begin)) {
            $this->beginEdmTime = $begin;
        }
        
        if (!empty($end)) {
            $this->endEdmTime = $end;
        }
    }
    
    public function setBeginCreateTime($begin) {
        
        if (!empty($begin)) {
            $this->beginCreateTime = $begin;
        }
    }
    
    public function setEndCreateTime($end) {
        
        if (!empty($end)) {
            $this->endCreateTime = $end;
        }
    }
    
    public function setEditBeginTime( $editBeginTime) {
        
        if (!empty($editBeginTime)) {
            $this->editBeginTime = $editBeginTime;
        }
    }
    
    public function setEditEndTime( $editEndTime) {
        
        if (!empty($editEndTime)) {
            $this->editEndTime = $editEndTime;
        }
    }
    
    public function setPrivateBeginTime( $privateBeginTime) {
        
        if (!empty($privateBeginTime)) {
            $this->privateBeginTime = $privateBeginTime;
        }
    }
    
    public function setPrivateEndTime( $privateEndTime) {
        
        if (!empty($privateEndTime)) {
            $this->privateEndTime = $privateEndTime;
        }
    }
    
    public function setPublicBeginTime( $publicBeginTime) {
        
        if (!empty($publicBeginTime)) {
            $this->publicBeginTime = $publicBeginTime;
        }
    }
    
    public function setPublicEndTime( $publicEndTime) {
        
        if (!empty($publicEndTime)) {
            $this->publicEndTime = $publicEndTime;
        }
    }
    
    public function setNextFollowUpBeginTime($nextFollowUpBeginTime) {
        
        if (!empty($nextFollowUpBeginTime)) {
            $this->nextFollowUpBeginTime = $nextFollowUpBeginTime;
        }
    }
    
    public function setNextFollowUpEndTime($nextFollowUpEndTime) {
        
        if (!empty($nextFollowUpEndTime)) {
            $this->nextFollowUpEndTime = $nextFollowUpEndTime;
        }
    }
    
    public function setRecentFollowUpBeginTime($recentFollowUpBeginTime) {
        
        $this->recentFollowUpBeginTime = $recentFollowUpBeginTime;
    }
    
    public function setRecentFollowUpEndTime($recentFollowUpEndTime) {
        
        $this->recentFollowUpEndTime = $recentFollowUpEndTime;
    }
    
    public function setHighLightFlag( $highLightFlag): void {
        
        $this->highLightFlag = $highLightFlag;
    }

    protected function buildDateField($field, $start, $end) {
    
        if (empty($field) || (empty($start) && empty($end))) {
        
            return;
        }
        
        $this->filter->{$field} = new DateRange($start ?: null, $end ?: null);
        
        $this->addListSearchFilter($field, [$start, $end], WorkflowConstant::FILTER_OPERATOR_RANGE);
    }
    
    
    
    public function joinCustomer($alias, $customerWhere = '', $customerParams = []) {

//        $customerFilter = new CustomerFilter($this->clientId);
        
        $table = $this->getSubTableName();
        
        if (!empty($customerWhere) && !empty($customerParams)) {
            $this->customerWhere = $customerWhere;
            $this->customerParams = $customerParams;
        }
        
        $this->filter->setRawJoin("left join $table as $alias on $this->alias.client_id=$alias.client_id AND $this->alias.{$this->entityName}=$alias.{$this->entityName} AND $alias.is_archive=1");
    }

//    todo
    public function setDataReader($flag) {
        
        $this->dataReader = $flag;
    }
    
    
    public function setDuplicateFlag($flag) {
        
        $this->duplicateFlag = $flag;
    }
    
    public function setArchiveType($archiveType) {
        
        $this->archiveType = $archiveType;
    }
    
    public function setArchiveTypes($types) {
        
        $this->archiveTypes = $types;
    }
    
    public function setAssess( $assess): void {
        
        // 兼容前端数据
        $assess = array_filter((array)$assess, 'is_numeric');
        $this->assess = $assess;
    }
    
    
    public function setFilterFreezeUserFlag( $filterFreezeUserFlag): void {
        
        $this->filterFreezeUserFlag = $filterFreezeUserFlag;
        $this->userIdFlag = $filterFreezeUserFlag;
    }
    
    public function setIncludeDeleteUserFlag( $includeDeleteUserFlag): void {
        
        $this->includeDeleteUserFlag = $includeDeleteUserFlag;
    }
    
    public function setUserIdFlag($userIdFlag) {
        
        $this->userIdFlag = $userIdFlag;
    }
    
    
    public function setLeadArchiveBeginTime( $leadArchiveBeginTime) {
        
        if (!empty($leadArchiveBeginTime)) {
            $this->leadArchiveBeginTime = $leadArchiveBeginTime;
        }
    }
    
    public function setLeadArchiveEndTime( $leadArchiveEndTime) {

        if (!empty($leadArchiveEndTime)) {
            $this->leadArchiveEndTime = $leadArchiveEndTime;
        }
    }
    
    
    /**
     * @param int $swarmId
     */
    public function setSwarmId( $swarmId) {
        
        if ($swarmId) {
            switch ($swarmId) {
                case Swarm::SWARM_ID_OF_ALL:
                    break;
                case Swarm::SWARM_ID_OF_FOLLOW:
//                    $this->showAll(true);
                    $this->setIsPin(true);
                    break;
//                case Swarm::SWARM_ID_OF_OWNER:
//                    $this->showAll(false);
//                    break;
                default:
                    $this->buildSwarmJoinFlag($swarmId);
                    break;
            }
            $this->swarmId = $swarmId;
        }
    }
    
    /**
     * @param mixed $tempSwarmId
     */
    public function setTempSwarmId($tempSwarmId) {
        
        if (!empty($tempSwarmId)) {
            $this->showAll(true);
            $this->tempSwarmRuleInfo = (new SwarmService($this->clientId))->getRuleInfoByTempSwarm($tempSwarmId);
            
            if (empty($this->tempSwarmRuleInfo) || empty($this->tempSwarmRuleInfo['filters'])) {
                
                return;
            }
            
            $this->buildSwarmJoinFlagByRuleInfo($this->tempSwarmRuleInfo);
        }
    }
    
    public function setSwarmList($swarmList) {
        
        $swarmList = array_filter((array)$swarmList);
        if (in_array(Swarm::SWARM_ID_OF_FOLLOW, $swarmList)) {
            $this->setIsPin(true);
        }
        
        $api = $this->getSwarmApi();
        
        $this->swarmList = array_diff($swarmList, $api->getExtraDataIds());
        
        if ($this->swarmList) {
            
            $this->buildSwarmJoinFlag($this->swarmList);
        }
    }
    
    
    public function setCompanyId($companyId) {
        
        if (!empty($companyId)) {
            $companyId = is_array($companyId) ? array_map('intval', $companyId) : intval($companyId);
        }
        $this->companyId = $companyId;
    }
    
    public function setProductGroupIds($productGroupIds) {
        
        if (!empty($productGroupIds)) {
            $productGroupIds = is_array($productGroupIds) ? array_filter($productGroupIds, 'is_numeric') : [$productGroupIds];
        }
        $this->productGroupIds = $productGroupIds;
    }
    
    public function setOrigin($origin, $allowEmpty = false) {
        
        if ($origin == 0 && $allowEmpty) {
            $origin = [0];
        }
        
        (!empty(array_filter((array)$origin)) || $allowEmpty) && $this->setOriginList(array_filter((array)$origin));
    }
    
    public function setIds($ids) {
        
        $this->setCompanyIds($ids);
    }
    
    public function setCompanyIds($companyIds) {
        
        if (!is_array($companyIds)) {
            $companyIds = [$companyIds];
        }
        $this->companyIds = array_map('intval', $companyIds);;
    }
    
    
    public function setExcludeCompanyIds($excludeCompanyIds) {
        
        if (!is_array($excludeCompanyIds)) {
            $excludeCompanyIds = [$excludeCompanyIds];
        }
        $this->excludeCompanyIds = array_map('intval', $excludeCompanyIds);
    }
    
    public function setStatusEditType($statusEditType) {
        
        $this->statusEditType = $statusEditType;
    }
    
    public function setAcquiredCompanyDay($acquiredCompanyDay) {
        
        $this->acquiredCompanyDay = $acquiredCompanyDay;
    }
    
    public function setOpportunityFlag($opportunityFlag) {
        
        $this->opportunityFlag = $opportunityFlag;
    }
    
    public function setPublicReasonId( $publicReasonId) {
        
        $this->publicReasonId = array_filter($publicReasonId);
    }
    
    public function setOpportunityStageType( $stageType, $alias = 'tc') {
        
        if (!empty($stageType)) {
            $this->stageType = $stageType;
        }
        $this->setAlias($alias);
    }
    
    public function setMinSuccessOpportunityCount($count) {
        
        if (is_numeric($count)) {
            $this->minSuccessOpportunityCount = $count;
        }
    }
    
    public function setMaxSuccessOpportunityCount($count) {
        
        if (is_numeric($count)) {
            $this->maxSuccessOpportunityCount = $count;
        }
    }
    
    public function setMinPerformanceOrderCount($count) {
        
        if (is_numeric($count)) {
            $this->minPerformanceOrderCount = $count;
        }
    }
    
    public function setMaxPerformanceOrderCount($count) {
        
        if (is_numeric($count)) {
            $this->maxPerformanceOrderCount = $count;
        }
    }
    
    public function setStartDealTime($dealTime) {
        
        $this->startDealTime = $dealTime;
    }
    
    public function setEndDealTime($dealTime) {
        
        $this->endDealTime = $dealTime;
    }
    
    public function setStartLatestWhatsappTime($latestWhatsappTime) {
        
        $this->startLatestWhatsappTime = $latestWhatsappTime;
    }
    
    public function setEndLatestWhatsappTime($latestWhatsappTime) {
        
        $this->endLatestWhatsappTime = $latestWhatsappTime;
    }
    
    public function setStartLatestWhatsappReceiveTime($latestWhatsappReceiveTime) {
        
        $this->startLatestWhatsappReceiveTime = $latestWhatsappReceiveTime;
    }
    
    public function setEndLatestWhatsappReceiveTime($latestWhatsappReceiveTime) {
        
        $this->endLatestWhatsappReceiveTime = $latestWhatsappReceiveTime;
    }
    
    public function setStartLatestWriteFollowUpTime($startLatestWriteFollowUpTime): void {
        
        $this->startLatestWriteFollowUpTime = $startLatestWriteFollowUpTime;
    }
    
    public function setEndLatestWriteFollowUpTime($endLatestWriteFollowUpTime): void {
        
        $this->endLatestWriteFollowUpTime = $endLatestWriteFollowUpTime;
    }
    
    public function setAlibabaCompanyTaskId($alibabaCompanyTaskId) {
        
        $this->alibabaCompanyTaskId = $alibabaCompanyTaskId;
    }
    
    public function setAliStoreId(array $aliStoreId) {
        
        $this->aliStoreId = array_filter($aliStoreId, 'is_numeric');
    }
    
    public function setIntentionLevel(array $intentionLevel) {
        
        $this->intentionLevel = array_filter($intentionLevel, 'is_numeric');
    }
    
    public function setAnnualProcurement(array $annualProcurement) {
        
        $this->annualProcurement = array_filter($annualProcurement, 'is_numeric');
    }
    
    public function setBeginTransactionOrderFirstTime($begin) {
        
        if (!empty($begin)) {
            $this->beginTransactionOrderFirstTime = $begin;
        }
    }
    
    public function setEndTransactionOrderFirstTime($end) {
        
        if (!empty($end)) {
            $this->endTransactionOrderFirstTime = $end;
        }
    }
    
    public function setBeginSuccessOpportunityFirstTime($begin) {
        
        if (!empty($begin)) {
            $this->beginSuccessOpportunityFirstTime = $begin;
        }
    }
    
    
    public function setEndSuccessOpportunityFirstTime($end) {
        
        if (!empty($end)) {
            $this->endSuccessOpportunityFirstTime = $end;
        }
    }
    
    public function setBeginOrderTime($begin) {
        
        if (!empty($begin)) {
            $this->beginOrderTime = $begin;
        }
    }
    
    public function setEndOrderTime($end) {
        
        if (!empty($end)) {
            $this->endOrderTime = $end;
        }
    }
    
    public function setPublicType($public_type) {
        
        $this->publicType = array_filter((array)$public_type, 'is_numeric');
    }
    
    public function setFeedType($feedType) {
        
        $this->feedType = $feedType;
    }
    
    public function setSerialId($serialId) {
        
        $this->serialId = trim($serialId);
    }
    
    public function setReceivePeriod($period) {
        
        $this->receivePeriod = $period;
    }
    
    public function setStatusId($statusId) {
        
        if ((is_array($statusId) && count($statusId = array_filter($statusId, 'is_numeric'))) || is_numeric($statusId)) {
            $this->trailStatus = $statusId;
        } else {
            $this->trailStatus = null;
        }
    }
    
    //支持移动,桌面端动态筛选的参数,filter_ext_field有值覆盖
    public function setFilterExtFields($filterExtField) {
        
        foreach ($filterExtField as $item) {
            if (!isset($item['key']) || !$item['keyword']) {
                continue;
            }
            if ($item['key'] == 'keyword') {
                $this->setKeyword($item['keyword']);
            }
            if ($item['key'] == 'min_success_opportunity_count') {
                $this->setMinPerformanceOrderCount($item['keyword']);
            }
            if ($item['key'] == 'max_success_opportunity_count') {
                $this->setMaxSuccessOpportunityCount($item['keyword']);
            }
            if ($item['key'] == 'min_performance_order_count') {
                $this->setMinPerformanceOrderCount($item['keyword']);
            }
            if ($item['key'] == 'max_performance_order_count') {
                $this->setMaxPerformanceOrderCount($item['keyword']);
            }
            if ($item['key'] == 'deal_time_start_date') {
                $this->setStartDealTime($item['keyword']);
            }
            if ($item['key'] == 'deal_time_start_date') {
                $this->setEndDealTime($item['keyword']);
            }
            if ($item['key'] == 'deal_time' && is_array($item['keyword'])) {
                
                $this->setStartDealTime($item['keyword'][0] ?? "");
                $this->setEndDealTime($item['keyword'][1] ?? "");
            }
            
            if ($item['key'] == 'archive_type') {
                $this->setArchiveType($item['keyword']);
            }
            if ($item['key'] == 'annual_procurement') {
                $this->setAnnualProcurement(is_array($item['keyword']) ? $item['keyword'] : json_decode($item['keyword'], true));
            }
            if ($item['key'] == 'intention_level') {
                $this->setIntentionLevel(is_array($item['keyword']) ? $item['keyword'] : json_decode($item['keyword'], true));
            }
            if ($item['key'] == 'public_time' && is_array($item['keyword'])) {
                $this->setPublicBeginTime($item['keyword'][0] ?? "");
                $this->setPublicEndTime($item['keyword'][1] ?? "");
            }
        }
    }
    
    public function setCompanyHashId($company_hash_id) {
        
        if (!is_array($company_hash_id)) {
            $company_hash_id = [$company_hash_id];
        }
        $this->companyHashId = $company_hash_id;
    }
    
    
    public function setAiSwarmId($aiSwarmId) {
        
        $this->aiSwarmId = $aiSwarmId;
    }
    
    public function buildCommonParam() {
        
        $entityId = $this->getPrimaryKey();
        
        $alias = $this->getFormattedAlias();
        
        $companyIds = [];

        //搜索得到companyIds 和指定companyIds 取交集
        if (!empty($this->companyIds)) {
            $companyIds = empty($companyIds) ? $this->companyIds : array_intersect($this->companyIds, $companyIds);
            if (empty($companyIds)) {
                
                $this->filter->alwaysEmpty();
                return;
            }
        }
        
        
        //如果companyIds存在，就不需要clientId了，这样规划器才会用到company_id的索引
        // ！！！！ 可能是版本更新，该限制不存在了
        if (empty($companyIds)) {
            
            $this->filter->client_id = $this->clientId;
            
        } else {
            if (!empty($this->filters) && !empty($this->criteriaType) && $this->criteriaType == WorkflowConstant::CRITERIA_TYPE_OR) {
                
                $this->filter->alwaysTrue();
                
                $this->filters[] = [
                    'field'      => $this->getPrimaryKey(),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator'   => WorkflowConstant::FILTER_OPERATOR_IN,
                    'value'      => $companyIds,
                    'refer_type' => $this->referType,
                    'rule_type'  => 2,
                ];
                
            } else {
                
                $this->filter->{$entityId} = $companyIds;
                
                $this->disableListSearchByFilter("{$entityId}查询");
            }
        }

//        todo?两个莫名其妙的东西
        if ($this->archiveType) {
            
            $this->filter->in('archive_type', $this->archiveType);
            
            $this->addListSearchFilter('archive_type', $this->archiveType);
        }
        
        if (!empty($this->archiveTypes)) {
            
            $this->filter->in('archive_type', $this->archiveTypes);
            
            $this->addListSearchFilter('archive_type', $this->archiveTypes);
        }
        
        //指定建档情况,0删除，1新线索 2建档
        if ($this->isArchive !== null) {
            if ($this->isArchive == 0) {
                $this->filter->removeWhere(['is_archive']);
            }
            $this->filter->in('is_archive', $this->isArchive);
        } else {
            
            $this->filter->removeWhere(['is_archive']);
        }
        
        //指定companyIds且忽略是否已建档
        if (!empty($this->companyIds) && $this->isArchive === null) {
            
            $this->filter->removeWhere(['is_archive']);
        }
        
        
        if (!($this->isArchive === true || $this->isArchive === 1 || $this->isArchive === [1] || $this->isArchive === ['1']
            || ($this->referType == \Constants::TYPE_LEAD && array_map(function ($item) { return $item > 0; }, (array)$this->isArchive)))) {
            $this->disableListSearchByFilter('错误的归档类型');
        }
        
        
        if ($this->isPin) {
            if (!empty($this->filters) && !empty($this->criteriaType) && $this->criteriaType == WorkflowConstant::CRITERIA_TYPE_OR) {
                $this->filters[] = [
                    'field'      => $this->getPrimaryKey(),
                    'field_type' => CustomFieldService::FIELD_TYPE_SELECT,
                    'operator'   => $this->notPinFlag ? WorkflowConstant::FILTER_OPERATOR_NOT_IN : WorkflowConstant::FILTER_OPERATOR_IN,
                    'value'      => $this->getPinIdList($this->viewingUserId),
                    'refer_type' => $this->referType,
                    'rule_type'  => 2,
                ];
            } else {
                
                $this->buildPin($this->viewingUserId, $this->notPinFlag);
                
                $this->disableListSearchByFilter('关注查询');
            }
        }
        
        if (!empty($this->pin_user_list)) {
            
            $this->buildPin($this->pin_user_list);
            
            $this->disableListSearchByFilter('关注人查询');
        }
        
        if (!empty($this->categoryIds)) {
            if (is_array(reset($this->categoryIds))) {
                $categoryCondition = [];
                foreach ($this->categoryIds as $item) {
                    $categoryId = json_encode($item);
                    $categoryCondition[] = "{$alias}category_ids @> '[$categoryId]'";
                }
                
                $this->filter->rawWhere(" and (" . implode(' or ', $categoryCondition) . ")");
                
            } else {
                
                $this->filter->category_ids = new ArrayContains($this->categoryIds);
                
            }
            
            $this->disableListSearchByFilter('主营产品查询');
        }
        
        if ($this->groupId !== null) {
            $groupIds = is_array($this->groupId) ? $this->groupId : [$this->groupId];
            
            if ($this->includeChildGroup) {
                //查询该分组和它的子分组
                $selectGroupIds = array_reduce($groupIds, function ($result, $groupId) {
                    
                    if ($groupId) {
                        $childGroupIds = \common\library\group\Helper::getChildrenIds($this->clientId,
                            \Constants::TYPE_COMPANY, $groupId);
                        $result = array_merge($result, $childGroupIds);
                    }
                    return $result;
                }, $groupIds);
            } else {
                $selectGroupIds = $groupIds;
            }
            
            
            $this->filter->in('group_id', $selectGroupIds);
            
            $this->addListSearchFilter('group_id', $selectGroupIds);
        }
        
        if (!empty($this->star)) {
            
            $this->filter->star = $this->star;
            
            $this->addListSearchFilter('star', $this->star);
        }
        
        
        if (!empty($this->country)) {
            
            $this->filter->country = is_array($this->country) ? new LowerIn($this->country) : new LowerEqual($this->country);
            
            $this->addListSearchFilter('country', $this->country);
        }
        
        if (!empty($this->province)) {
            
            $this->filter->in('province', $this->province);
            
            $this->addListSearchFilter('province', $this->province);
        }
        
        if (!empty($this->city)) {
            
            $this->filter->in('city', $this->city);
            
            $this->addListSearchFilter('city', $this->city);
        }
        
        if ($this->bizType !== null) {
            
            $this->filter->in('biz_type', $this->bizType);
            
            $this->addListSearchFilter('biz_type', $this->bizType);
        }
        
        if (!empty($this->originList)) {
            
            $this->filter->origin_list = new InArray($this->originList);
            
            $this->addListSearchFilter('origin', $this->originList, WorkflowConstant::FILTER_OPERATOR_IN);
        }
        
        if ($this->trailStatus !== null) {
            
            $this->filter->in('trail_status', $this->trailStatus);
            
            $this->addListSearchFilter('trail_status', $this->trailStatus);
        }
        
        if ($this->createUser) {
            
            $this->filter->in('create_user', $this->createUser);
            
            $this->addListSearchFilter('create_user', $this->createUser);
        }
        
        if ($this->lastEditUser) {
            
            $this->filter->in('last_edit_user', $this->lastEditUser);
            
            $this->addListSearchFilter('last_edit_user', $this->lastEditUser);
        }
        
        if ($this->maxReleaseCount || $this->minReleaseCount) {
            
            
            $minCount = is_numeric($this->minReleaseCount) ? $this->minReleaseCount : null;
            
            $maxCount = is_numeric($this->maxReleaseCount) ? $this->maxReleaseCount : null;
            
            $this->filter->release_count = new Range($minCount, $maxCount);
            
            $this->addListSearchFilter('release_count', $this->maxReleaseCount, WorkflowConstant::FILTER_OPERATOR_LESS_OR_EQUAL);
            
            unset($minCount, $maxCount);
        }
        
        
        if ($this->compareDay) {
            
            $compareTime = strtotime("-{$this->compareDay} day");
            
            $compareTime = $compareTime > 0 ? $compareTime : strtotime('1970-01-01 00:00:01');
            
            $date = date('Y-m-d 00:00:00', $compareTime);
            
            if ($this->compareDayOp == self::GREAT_THAN) {
                
                $this->filter->order_time = new EGT($date);
            } else {
                
                $this->filter->order_time = new LT($date);
            }
            
            $this->addListSearchFilter('order_time', $date, $this->compareDayOp == self::GREAT_THAN ? '>=' : '<');
        }
        
        $activityTypeSql = '';
        
        if ($this->activityType != 0) {
            switch ($this->activityType) {
                case Company::ACTIVITY_TYPE_THREE:
                    $activityTypeSql = " and EXTRACT(DAY FROM now() - order_time) < 3 ";
                    break;
                case Company::ACTIVITY_TYPE_SEVEN:
                    $activityTypeSql = " and EXTRACT(DAY FROM now() - order_time) >= 3 and EXTRACT(DAY FROM now() - order_time) < 7 ";
                    break;
                case Company::ACTIVITY_TYPE_THIRTY:
                    $activityTypeSql = " and EXTRACT(DAY FROM now() - order_time) >= 7 and EXTRACT(DAY FROM now() - order_time) < 30 ";
                    break;
                case Company::ACTIVITY_TYPE_NINETY:
                    $activityTypeSql = " and EXTRACT(DAY FROM now() - order_time) >= 30 and EXTRACT(DAY FROM now() - order_time) < 90 ";
                    break;
                case Company::ACTIVITY_TYPE_MORE_THAN_NINETY:
                    $activityTypeSql = " and EXTRACT(DAY FROM now() - order_time) >= 90 ";
                    break;
                default:
                    break;
            }
            
            $this->filter->rawWhere($activityTypeSql);
            
            $this->disableListSearchByFilter('活跃客户查询');
        }
        
        if ($this->compareMailTime != 0) {
            $realCompareTime = strtotime("-{$this->compareMailTime} day");
            $realCompareTime = $realCompareTime > 0 ? $realCompareTime : strtotime('1970-01-01 00:00:01');
            $date = date('Y-m-d 23:59:59', $realCompareTime);
            
            $this->filter->mail_time = new GT($date);
            
            $this->addListSearchFilter('mail_time', $date, WorkflowConstant::FILTER_OPERATOR_GREATER);
        }
        
        if ($this->isRecentSelected) {
            
            $this->buildRecentList($this->viewingUserId, \RecentSelect::TYPE_CUSTOMER_COMPANY);
            
            $this->disableListSearchByFilter('最近选择');
        }
        
        if ($this->ownerType == self::OWNER_TYPE_PUBLIC && $this->lastOwner) {
            
            $this->filter->in('last_owner', $this->lastOwner);
            
            $this->addListSearchFilter('last_owner', $this->lastOwner);
        }
        
        if ($this->hasMailTime) {
            // ? 会被解释为占位符 用pdo会报错 ? ?& ?| 对应exists exists_all exists_any 如果对象是jsonb就加jsonb_前缀
            //$sql .= " and user_data->'$this->viewingUserId' ? 'mail_time'";
            
            $this->filter->rawWhere(" and JSONB_EXISTS(user_data->'$this->viewingUserId', 'mail_time')");
            
            $this->disableListSearchByFilter('最近邮件往来');
        }
        
        if (!empty($this->tel)) {
            
            $this->filter->tel_full = $this->tel;
            
            $this->addListSearchFilter('tel', $this->tel);
        }
        
        
        if ($this->duplicateFlag !== null) {
            if ($this->duplicateFlag > 0 && $this->duplicateFlag == DuplicateFlagBuilder::DUPLICATE_TRUE) {
                
                $this->filter->duplicate_flag = new GT(0);
                
            } else if ($this->duplicateFlag > 0 && $this->duplicateFlag == DuplicateFlagBuilder::DUPLICATE_LEAD) {
                
                $number = DuplicateFlagBuilder::LEAD_NUMBER;
                
                $this->filter->rawWhere(" AND {$alias}duplicate_flag > 0 AND (duplicate_flag & {$number}::bigint) > 0 ");
                
            } else if ($this->duplicateFlag > 0 && $this->duplicateFlag == DuplicateFlagBuilder::DUPLICATE_COMPANY) {
                
                $number = DuplicateFlagBuilder::COMPANY_NUMBER;
                
                $this->filter->rawWhere(" AND {$alias}duplicate_flag > 0 AND (duplicate_flag & {$number}::bigint) > 0 ");
                
            } else if ($this->duplicateFlag == DuplicateFlagBuilder::DUPLICATE_FALSE) {
                
                $this->filter->duplicate_flag = 0;
            }
            
            $this->disableListSearchByFilter('疑似重复');
        }
        
        if (!empty($this->growthLevel)) {
            
            $this->filter->growth_level = new InArray($this->growthLevel, 'INTEGER');
            
            $this->addListSearchFilter('growth_level', $this->growthLevel, WorkflowConstant::FILTER_OPERATOR_IN);
        }
        
        if ($this->assess) {
            
            $assessSql = " AND ({$alias}score -> 'total_assess')::FLOAT IS NOT NULL  ";
            
            isset($this->assess[0]) && $assessSql = " AND ({$alias}score -> 'total_assess')::FLOAT >= {$this->assess[0]} ";
            isset($this->assess[1]) && $assessSql = " AND ({$alias}score -> 'total_assess')::FLOAT <= {$this->assess[1]} ";
            
            $this->filter->rawWhere($assessSql);
            
            $this->disableListSearchByFilter('新版评分查询');
        }
        
        if (!empty($this->tags)) {
            
            $this->filter->tag = new CusTag($this->tags, $this->tagMatchMode, (($this->tagUserId === null) ? $this->viewingUserId : $this->tagUserId));
            
            $this->disableListSearchByFilter('tag查询');
        }
        
        if ($this->filter->isJoinSence() && !empty($this->customerWhere) && !empty($this->customerParams)) {
            
            $this->filter->company_id = new MergeQuery($this->customerWhere, $this->customerParams);
            
            $this->disableListSearchByFilter('join customer 查询');
        }
        
        
        //小满发现公司company_hash_id查询
        if (!empty($this->companyHashId)) {
            
            $this->filter->company_hash_id = $this->companyHashId;
            
            $this->addListSearchFilter('company_hash_id', $this->companyHashId);
        }
        
        $reportSql = $this->buildReportSelect($this->getPrimaryKey(), $alias);
        
        if ($reportSql) {
            
            $this->disableListSearchByFilter('报表查询');
        }
        
        if ($this->publicRemindDay > 0) {
//            todo?
            $nextMoveToPublicDate = date('Y-m-d', strtotime("{$this->publicRemindDay} days"));
            
            $this->filter->next_move_to_public_date = new DateRange('1970-01-01', $nextMoveToPublicDate, 0, 1);
            
            $this->addListSearchFilter('next_move_to_public_date', ['1971-01-01', $nextMoveToPublicDate], WorkflowConstant::FILTER_OPERATOR_RANGE);
        }
        
        {
            //未来7天移入公海，以及分享给我的User_id列表
            if ($this->showAll) {
                //超管，不需要限制User_id，查询全部
                if (!$this->processUserId) {
                    $selectUserIds = [];
                } else {
                    //判断是否数组
                    if (!is_array($this->processUserId)) {
                        $selectUserIds = array($this->processUserId);
                    } else {
                        $selectUserIds = $this->processUserId;
                    }
                }
            } else {
                $selectUserIds = array($this->viewingUserId);
            }
            
            if ($this->receivePeriod > 0) {
                $this->disableListSearchByFilter('周期客户查询');
                //目前周期有day,week,mongth,year
                $companyIdArrs = \common\library\customer\public_record\Helper::findCompanyDataByPeriod($this->viewingUserId, $this->clientId, $this->receivePeriod);
                if (!empty($companyIdArrs)) {
                    
                    $this->filter->in('company_id', $companyIdArrs);
                } else // 无数据时 直接返回空
                {
                    $this->filter->alwaysEmpty();
                    return;
                }
            }
            
            if ($this->acquiredCompanyDay > 0) {
                $this->disableListSearchByFilter('周期跟进客户查询');
                $endAcquiredCompany = date('Ymd');
                $startAcquiredCompany = date('Ymd', strtotime("-{$this->acquiredCompanyDay}day"));
                
                $companyIds = \CompanyFollowRecord::getCompanyIdsByType($this->clientId, $selectUserIds,
                    [\CompanyFollowRecord::TYPE_ACQUIRED_FROM_TRANSFER, \CompanyFollowRecord::TYPE_ACQUIRED_FROM_SHARE],
                    $startAcquiredCompany, $endAcquiredCompany);
                
                if ($companyIds) {
                    
                    $this->filter->in('company_id', $companyIds);
                } else {
                    
                    $this->filter->alwaysEmpty();
                    return;
                }
            }
        }
        
        // 设置排除companyIds的，需要排除掉
        if (!empty($this->excludeCompanyIds) && is_array($this->excludeCompanyIds)) {
            
            $this->filter->notIn($this->filter->company_id, $this->excludeCompanyIds);
            
            $this->addListSearchFilter('company_id', $this->excludeCompanyIds, WorkflowConstant::FILTER_OPERATOR_NOT_IN);
        }
        
        if ($this->mainLeadId !== null) {
            
            $this->filter->in('main_lead_id', $this->mainLeadId);
            
            $this->addListSearchFilter('main_lead_id', $this->mainLeadId);
        }

        //商机状态处理
        if (!empty($this->stageType)) {
            $opportunityTableName = 'tbl_opportunity';
            if (count($this->stageType) == 4) {
                return;
            }
            
            if (!$this->getAlias()) {
                $this->setAlias('tc');
            }
            $this->filter->distinct(true);
            
            $opportunityAlias = 'O';
            $opportunityList = new OpportunityList($this->clientId);
            $opportunityList->setViewingUserId($this->viewingUserId);
            $opportunityList->setUserId($this->userId);
            $opportunityList->setShowAll($this->showAll ? true : false);
            $opportunityCondition = $opportunityList->buildUserSql($opportunityAlias . '.');
            
            $joinMethod = ' join ';
            
            $joinCondition = " $opportunityTableName as $opportunityAlias on {$this->getFormattedAlias()}client_id=$opportunityAlias.client_id AND {$this->getFormattedAlias()}company_id=$opportunityAlias.company_id AND $opportunityAlias.enable_flag = 1" . $opportunityCondition;
            
            if ($this->stageType == [0]) {
                
                $joinMethod = ' left join ';
                
                $this->filter->rawWhere(" and ( {$opportunityAlias}.company_id ISNULL) ");
                
            } elseif ($this->stageType == [1, 2, 3]) {
            
            } else {
                $stageTypes = implode(',', array_filter($this->stageType));
                
                $this->filter->rawWhere(" and {$opportunityAlias}.stage_type in ($stageTypes) ");
            }
            
            $joinSql = $joinMethod . $joinCondition;
            
            $this->filter->setRawJoin($joinSql);
            
            $this->disableListSearchByFilter('join商机查询');
        }

//        todo?????????
        //赢单商机相关查询
        $minCount = is_numeric($this->minSuccessOpportunityCount) ? $this->minSuccessOpportunityCount : null;
        
        $maxCount = is_numeric($this->maxSuccessOpportunityCount) ? $this->maxSuccessOpportunityCount : null;
        
        $this->filter->success_opportunity_count = new Range($minCount, $maxCount);
        
        $this->addListSearchFilter('success_opportunity_count', [$minCount, $maxCount], WorkflowConstant::FILTER_OPERATOR_RANGE);
        
        unset($minCount, $maxCount);
        
        
        if ($this->beginTransactionOrderFirstTime || $this->endTransactionOrderFirstTime) {
            
            $this->filter->transaction_order_first_time = new DateRange($this->beginTransactionOrderFirstTime ?? null, $this->endTransactionOrderFirstTime ?? null);
        }
        
        if ($this->beginSuccessOpportunityFirstTime || $this->endSuccessOpportunityFirstTime) {
            
            $this->filter->success_opportunity_first_time = new DateRange($this->beginSuccessOpportunityFirstTime ?? null, $this->endSuccessOpportunityFirstTime ?? null);
        }
        
        if ($this->serialId) {
            
            $this->filter->serial_id = $this->serialId;
        }
        
        //计入业绩订单相关查询
        $minCount = is_numeric($this->minPerformanceOrderCount) ? $this->minPerformanceOrderCount : null;
        
        $maxCount = is_numeric($this->maxPerformanceOrderCount) ? $this->maxPerformanceOrderCount : null;
        
        $this->filter->performance_order_count = new Range($minCount, $maxCount);
        
        $this->addListSearchFilter('performance_order_count', [$minCount, $maxCount], WorkflowConstant::FILTER_OPERATOR_RANGE);
        
        unset($minCount, $maxCount);
        
        //阿里巴巴国际站同步客户
        if ($this->alibabaCompanyTaskId) {
            list($privateCompanyIds, $publicCompanyIds,) = CustomerSyncHelper::getSyncTaskCompanyIds($this->alibabaCompanyTaskId);
            $taskCompanyIds = array_merge($privateCompanyIds, $publicCompanyIds);
            
            if (empty($taskCompanyIds)) {
                
                $this->filter->alwaysEmpty();
                return;
            } else {
                
                $this->filter->in('company_id', $taskCompanyIds);
            }
            
            $this->disableListSearchByFilter('执行companyId查询');
        }
        
        if (!empty($this->archiveTypes)) {
            
            $this->filter->archive_type = $this->archiveTypes;
            $this->addListSearchFilter('archive_type', $this->archiveTypes);
        }
        
        if ($this->intentionLevel) {
            
            $this->filter->intention_level = $this->intentionLevel;
            $this->addListSearchFilter('intention_level', $this->intentionLevel);
        }
        
        if ($this->annualProcurement) {
            
            $this->filter->annual_procurement = $this->annualProcurement;
            $this->addListSearchFilter('annual_procurement', $this->annualProcurement);
        }
        
        if ($this->startDealTime || $this->endDealTime) {
            
            $this->filter->deal_time = new DateRange($this->startDealTime ?? null, $this->endDealTime ?? null);
            $this->addListSearchFilter('deal_time', [$this->startDealTime, $this->endDealTime], WorkflowConstant::FILTER_OPERATOR_RANGE);
        }
        
        if ($this->startLatestWhatsappTime || $this->endLatestWhatsappTime) {
            
            $this->filter->latest_whatsapp_time = new DateRange($this->startLatestWhatsappTime ?? null, $this->endLatestWhatsappTime ?? null);
            $this->addListSearchFilter('latest_whatsapp_time', [$this->startLatestWhatsappTime, $this->endLatestWhatsappTime], WorkflowConstant::FILTER_OPERATOR_RANGE);
        }
        
        if ($this->startLatestWhatsappReceiveTime || $this->endLatestWhatsappReceiveTime) {
            
            $this->filter->latest_whatsapp_receive_time = new DateRange($this->startLatestWhatsappReceiveTime ?? null, $this->endLatestWhatsappReceiveTime ?? null);
            $this->addListSearchFilter('latest_whatsapp_receive_time', [$this->startLatestWhatsappReceiveTime, $this->endLatestWhatsappReceiveTime], WorkflowConstant::FILTER_OPERATOR_RANGE);
        }
        
        if ($this->startLatestWriteFollowUpTime || $this->endLatestWriteFollowUpTime) {
            
            $this->filter->latest_write_follow_up_time = new DateRange($this->startLatestWriteFollowUpTime ?? null, $this->endLatestWriteFollowUpTime ?? null);
            $this->addListSearchFilter('latest_write_follow_up_time', [$this->startLatestWriteFollowUpTime, $this->endLatestWriteFollowUpTime], WorkflowConstant::FILTER_OPERATOR_RANGE);
        }
        
        if (!empty($this->aliStoreId)) {
            
            $this->filter->ali_store_id = new InArray($this->aliStoreId);
            $this->addListSearchFilter('ali_store_id', $this->aliStoreId, WorkflowConstant::FILTER_OPERATOR_IN);
        }
        
        if (isset($this->publicType)) {
            
            $this->filter->public_type = $this->publicType;
            $this->addListSearchFilter('public_type', $this->publicType,);
        }
        
        if (isset($this->publicReasonId)) {
            
            $this->filter->public_reason_id = $this->publicReasonId;
            $this->addListSearchFilter('public_reason_id', $this->publicReasonId,);
        }
        
        if ($this->productGroupIds !== null) {
            
            $this->filter->product_group_ids = new InArray($this->productGroupIds);
            $this->addListSearchFilter('product_group_ids', $this->productGroupIds, WorkflowConstant::FILTER_OPERATOR_IN);
        }
        
        if ($this->feedType) {
            if (in_array($this->feedType, [
                TodoConstant::TODO_TYPE_COMPANY_TRANSFER_TO_ME,
                TodoConstant::TODO_TYPE_COMPANY_AUTO_CREATE,
                TodoConstant::TODO_TYPE_COMPANY_DATA_UPDATE,
                TodoConstant::TODO_TYPE_COMPANY_TRAIL_UPDATE,
                TodoConstant::TODO_TYPE_COMPANY_EMAIL_INVALID,
                TodoConstant::TODO_TYPE_COMPANY_CUSTOMS_UPDATE,
            ])) {
                $loginUser = \User::getLoginUser();
                $loginUserId = $loginUser->getUserId();
                $loginClientId = $loginUser->getClientId();
                
                $redis = \RedisService::cache();
                $key = implode('_', ['feed_company_ids', $this->feedType, $loginClientId, $loginUserId]);
                $feedCompanyIds = \RedisService::getCacheData($redis, $key, 5 * 60, function () {
                    
                    $feedList = new Feed(\Constants::TYPE_CUSTOMER, $this->feedType);
                    $feedListData = $feedList->readFeed(100, 0);
                    $feedListData = $feedListData['feed_list'] ?? [];
                    return array_values(array_unique(array_column($feedListData, 'company_id')));
                });
                
                $this->filter->in('company_id', $feedCompanyIds);
            }
        }
        
        if (!empty($this->swarmId)) {
            
            $api = $this->getSwarmApi();
            
            if (!in_array($this->swarmId, $api->getExtraDataIds())) {
                
                $ruleInfo = $api->cachedList($this->swarmId)[$this->swarmId] ?? [];
                
                $this->buildBySwarmRuleInfo($ruleInfo);
            }
        }
        
        if (!empty($this->swarmList)) {
            
            $api = $this->getSwarmApi();
            
            $ruleInfoList = $api->cachedList($this->swarmList, false, true) ?: [];
            
            foreach ($ruleInfoList as $ruleInfo) {
                
                $this->buildBySwarmRuleInfo($ruleInfo);
            }
        }
        
        if (!empty($this->aiSwarmId)) {
            $api = SwarmService::getSwarmApi(ItemSettingConstant::ITEM_TYPE_SWARM_AI, $this->clientId);
            $api->getParams()->setIgnoreOwnerUserId(true);
            $companyIds = $api->queryCompanyId($this->aiSwarmId);
            
            if (!empty($companyIds)) {
    
                $this->filter->company_id = $companyIds;
            } else {
    
                return $this->filter->alwaysEmpty();
            }
        }
        
        $this->buildDateField('archive_time', $this->beginArchiveTime, $this->endArchiveTime);
        $this->buildDateField('follow_up_time', $this->beginFollowUpTime, $this->endFollowUpTime);
        $this->buildDateField('order_time', $this->beginOrderTime, $this->endOrderTime);
        $this->buildDateField('update_time', $this->beginUpdateTime, $this->endUpdateTime);
        $this->buildDateField('edm_time', $this->beginEdmTime, $this->endEdmTime);
        $this->buildDateField('create_time', $this->beginCreateTime, $this->endCreateTime);
        $this->buildDateField('edit_time', $this->editBeginTime, $this->editEndTime);
        $this->buildDateField('private_time', $this->privateBeginTime, $this->privateEndTime);
        $this->buildDateField('public_time', $this->publicBeginTime, $this->publicEndTime);
        $this->buildDateField('next_follow_up_time', $this->nextFollowUpBeginTime, $this->nextFollowUpEndTime);
        $this->buildDateField('recent_follow_up_time', $this->recentFollowUpBeginTime, $this->recentFollowUpEndTime);
    }
}
