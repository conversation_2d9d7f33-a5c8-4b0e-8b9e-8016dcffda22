<?php

namespace common\library\customer_v3\company;

use CEmailValidator;
use common\library\custom_field\CustomFieldService;
use common\library\customer_v3\company\list\CompanyList;
use common\library\layout\LayoutConstants;
use common\library\object\field\FieldConstant;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\object\field\field_setting\CompanyFieldSetting;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\item\ItemSettingConstant;
use common\library\workflow\WorkflowConstant;

class CompanyListProcessor {
    
    
    const SORT_SCENE_SETTING = 'setting';
    
    const SORT_SCENE_SEARCH = 'search';
    
    const SORT_FIELD_MAP = [
        'country_region'    => 'country',
        'trail_status_name' => 'trail_status',
        'recent_time'       => 'order_time',
    ];

    // 特殊时间字段，最近 xxx 天 未XX | 已XX
    // example: 最近成交日期 最近10天未成交
    const SPECIAL_TIME_FIELDS = [
        'order_time',
        'recent_follow_up_time',
        'deal_time',
    ];
    
    
    protected int   $clientId;
    protected \User $opUser;
    
    private CompanyList $list;
    
    private CompanyListDTO $dto;
    
    private array $param;
    
    private mixed  $fieldList;
    private mixed   $isPrivate = null;
    private int    $viewingUserId;
    private string $swarmScene;
    private mixed $businessType = null;

    public function __construct($clientId, CompanyListDTO $dto) {
        
        $this->dto = $dto;
        
        $this->clientId = $clientId;
        
        $this->opUser = \User::getLoginUser();
        
        !empty($this->dto->swarm_id) && \LogUtil::info("companyList params:" . json_encode($_REQUEST));
        
        $this->viewingUserId = $this->dto->sub_user_id ?: $this->opUser->getUserId();
    }

    public function setIsPrivate()
    {
        $this->isPrivate = ($this->dto->show_field_key == \common\library\setting\user\UserSetting::PRIVATE_COMPANY_LIST_FIELD) || ($this->dto->user_num != [0]);
    }

    public function getIsPrivate()
    {
        if (is_null($this->isPrivate)) {
            $this->setIsPrivate();
        }
        return $this->isPrivate;
    }

    public function getBusinessType()
    {
        if (is_null($this->businessType)) {
            $this->businessType = $this->getIsPrivate() ? ObjConstant::BUSINESS_TYPE_COMPANY_PRIVATE : ObjConstant::BUSINESS_TYPE_COMPANY_POOL;
        }
        return $this->businessType;
    }
    
    public function run() {
        
        $this->list = new CompanyList($this->viewingUserId);
        
        $this->setIsPrivate();
        
        $this->prepareOrder();

        $this->prepareFilters();
        
        $this->validateViewPermissions();
        
        if ($result = $this->processListSetting()) {
            
            return $result;
        }

        if($this->dto->layout_flag){
            $this->list->getFormatter()->displayListLayoutFieldInfo(ObjConstant::BUSINESS_TYPE_COMMON);
        }

        return $this->executeFind();
    }

    private function prepareFilters()
    {
        [$commonFilters, $timeFilters] = \common\library\object\field\Helper::splitFilters($this->clientId, $this->dto->filters);

        $this->dto->filters = $commonFilters;
        $this->dto->time_filters = array_merge($this->dto->time_filters, $timeFilters);
    }
    
    
    private function prepareOrder() {
        
        $this->dto->sort_field = self::SORT_FIELD_MAP[$this->dto->sort_field] ?? $this->dto->sort_field ?? '';
        
        $this->prepareOrderBySortScene();
        
        $this->prepareOrderByExternal();
        
        $this->dto->sort_field = $this->dto->sort_field ?: list\CompanyList::DEFAULT_SORT_FIELD;
    }
    
    private function prepareOrderBySortScene() {
        
        $this->swarmScene = (!empty($this->dto->swarm_id) && $this->dto->user_num == [0]) ? 'publicSwarm' : 'swarm';
        
        $this->fieldList = \common\library\customer\Helper::getCompanyListFieldList(
            $this->clientId,
            $this->opUser->getUserId(),
            $this->dto->swarm_id ?: $this->dto->show_field_key ?: null,
            $this->swarmScene);
        
        if ($this->dto->sort_scene == self::SORT_SCENE_SETTING) {
            
            foreach ($this->fieldList as $companyListField) {
                
                if (!empty($companyListField['order']) && in_array($companyListField['order'], ['asc', 'desc'])) {
                    
                    $this->dto->sort_field = $companyListField['field'];
                    
                    if (!empty($this->dto->sort_field) && isset(self::SORT_FIELD_MAP[$this->dto->sort_field])) {
                        
                        $this->dto->sort_field = self::SORT_FIELD_MAP[$this->dto->sort_field];
                    }
                    
                    $this->dto->sort_type = $companyListField['order'];
                    
                    break;
                }
            }
        } elseif ($this->dto->sort_scene == self::SORT_SCENE_SEARCH) {
            
            foreach ($this->fieldList as &$companyListField) {
                
                unset($companyListField['order']);
            }
        }
    }
    
    /**
     * 1.不在允许允许范围内的排序字段都降级为order_time字段排序
     * 2.自定义字段的排序要对值进行默认值和类型转换处理
     */
    private function prepareOrderByExternal() {
        
        if (!is_numeric($this->dto->sort_field) && !in_array($this->dto->sort_field, \common\library\customer\BaseCompanyList::allowSortFields)) {
            
            $this->dto->sort_field = list\CompanyList::DEFAULT_SORT_FIELD;
        } elseif (is_numeric($this->dto->sort_field)) {
            
            $fieldInfo = \common\library\custom_field\Helper::getFieldListInfo($this->clientId, $this->opUser->getUserId(), \Constants::TYPE_COMPANY, 0, [$this->dto->sort_field]);
            
            $this->dto->sort_field = match (intval($fieldInfo[0]['field_type'] ?? 0)) {
                
                CustomFieldService::FIELD_TYPE_NUMBER => "cast(coalesce(nullif(external_field_data->>'{$this->dto->sort_field}',''),'-9223372036854775807') as decimal)",
                CustomFieldService::FIELD_TYPE_DATE => "cast(coalesce(nullif(external_field_data->>'{$this->dto->sort_field}',''),'1970-01-01') as date)",
                CustomFieldService::FIELD_TYPE_DATETIME => "cast(coalesce(nullif(external_field_data->>'{$this->dto->sort_field}',''),'1970-01-01 00:00:00') as date)",
                CustomFieldService::FIELD_TYPE_SELECT => "nullif(external_field_data->>'{$this->dto->sort_field}','')",
                default => list\CompanyList::DEFAULT_SORT_FIELD,
            };
        }
    }
    
    /**
     * 公私海鉴权，高优质除外
     *
     * @return void
     */
    private function validateViewPermissions() {
    
        if ($this->dto->scenario == CompanyList::SCENARIO_HIGH_QUALITY_CUSTOMER) {
            
            return;
        }
        
        $privilege = ($this->dto->show_field_key == \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD || count(array_intersect([0], $this->dto->user_num)))
            ? PrivilegeConstants::PRIVILEGE_CRM_COMPANY_POOL_VIEW : PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW;
        
        \common\library\privilege_v3\Helper::checkPermission($this->clientId, $this->opUser->getUserId(), $privilege);
    }
    
    private function processListSetting() {
        
        $this->applyPreProcessingSteps();
        
        $this->list->paramsMapping($this->retrieveParam());
        
        return $this->applyPostProcessingSteps();
    }
    
    private function retrieveParam(): array {
        
        return $this->param ??= $this->dto->getParam();
    }
    
    
    private function applyPreProcessingSteps() {
        
        //开启es缓存
        $this->list->setEnableListSearch(true);

        $fieldList = array_map(function ($field) {
            if (isset($field['object_name']) && $field['object_name'] == ObjConstant::OBJ_CUSTOMER && !\str_contains($field['field'], 'customer.')) {
                $field['field'] = 'customer.'.$field['field'];
            }
            return $field;
        }, $this->fieldList);

        $this->list->getFormatter()->listCompanyBySetting($this->dto->swarm_id ?: $this->dto->temp_swarm_id, $this->swarmScene, array_column($fieldList, 'field'));
        
        $this->runProcessSetting($this->definePreProcessingSettings());
    }
    
    private function applyPostProcessingSteps() {
        
        return $this->runProcessSetting($this->definePostProcessingSettings());
    }
    
    private function runProcessSetting(array $setting) {
        
        foreach ($setting as $key => $item) {
            
            if (empty($item['action'])) {
                
                continue;
            }
            
            $condition = empty($item['condition']) ? !empty($this->dto->{$key}) : (is_callable($item['condition']) && $item['condition']());
            
            if ($condition && $result = (is_callable($item['action']) ? $item['action']() : $this?->{$item['action']}())) {
                
                return $result;
            }
        }
    }
    
    
    private function executeFind() {
        
        $this->list->setOrder($this->dto->sort_type);
        
        $this->list->setOrderBy($this->dto->sort_field);
        
        $this->list->setLimit($this->dto->pageSize, $this->dto->curPage);

        $this->list->setScenario($this->dto->scenario);
        
        $total = null;
        
        if (is_numeric($this->dto->sort_field)) {
            
            // 自定义字段进行排序，如果查询结果集超出10000条记录时，需要降级成按order_time进行排序，不然会引发查询性能问题
            $total = $this->list->count();
            
            // 兼容旧版排序
            if ($total > $this->list->getOrderLimit() && $this->dto->swarm_id) {
                
                $this->list->setOrderBy(list\CompanyList::DEFAULT_SORT_FIELD);
            }
        }
        
        $count = is_null($total) ? $this->list->count() : $total;
        
        // 公海数量过多时根据company_id排序性能过差
        if ($this->isPrivate || ($count < 20000) || !in_array($this->dto->sort_field, ['order_time', 'public_time'])) {
            
            $this->list->setOrderBy(array_values(array_unique(array_merge((array)$this->dto->sort_field, ['company_id']))));
        }

        $api = \common\library\setting\item\Api::swarm($this->clientId, $this->viewingUserId);
        return [
            'list'                       => $this->layoutData(),
            'totalItem'                  => $count,
            'search_info'                => $this->list?->getSearchInfo(),
            'transaction_order_currency' => \common\library\performance_v2\Helper::getCurrencyFromDefaultOrderPerformanceRule($this->clientId),
            'field_list'                 => $api->getHeaderFieldList($this->fieldList, $this->dto->layout_flag ?? 0),
        ];
    }

    public function layoutData()
    {
        $listData = $this->list->find();

        $layoutApi = new \common\library\layout\LayoutApi($this->clientId, ObjConstant::OBJ_COMPANY, LayoutConstants::LAYOUT_PAGE_TYPE_LIST, \Constants::CLIENT_TYPE_WEB, $this->getBusinessType());
        $layoutApi->setBusinessScene( PrivilegeFieldV2::SCENE_OF_VIEW);
        return $layoutApi->listControlAction($this->opUser->getUserId(), $listData);
    }
    
    
    /**
     * 如果是工作台跳转需要特殊处理切user_id不设置
     *
     * @return void
     */
    private function setScenario() {
        
        if (in_array($this->dto->scenario, [list\CompanyList::SCENARIO_STATISTIC_CUSTOMER_ADD, list\CompanyList::SCENARIO_STATISTIC_FOLLOW_CUSTOMER])) {
            
            $canViewAll = \common\library\privilege_v3\Helper::getPermissionScope($this->clientId, $this->opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW) == PrivilegeConstants::PRIVILEGE_SCOPE_DIRECTOR;
            $filteredUserIds = \common\library\privilege_v3\Helper::filterPermissionScopeUser($this->clientId, $this->opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW, $this->dto->user_id);
            if (!$canViewAll && empty($filteredUserIds)) {
                if ($this->dto->show_all) {
                    $filteredUserIds = \common\library\privilege_v3\Helper::getPermissionScopeUser($this->clientId, $this->opUser->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_COMPANY_VIEW);
                } else {
                    $this->list->setCompanyIds([]);
                }
            }
            
            $this->list = \common\library\customer_v2\Helper::handleStatisticScene($this->dto->scenario, $this->dto->scenario_data, $this->list, $this->clientId, $filteredUserIds);
            
            $this->dto->user_num = [];
            
            $this->dto->user_id = [];
            
        } else {
            
            $this->list->setUserId($this->dto->user_id ?: null);
            $this->list->setUserNum($this->dto->user_num);
        }
    }
    
    /**
     * ......
     * EDM 有个筛选整合了进来，只返回邮箱联系人列表 （1：邮箱，2：whatsapp）
     *
     * @return array
     */
    private function setGetContractList() {
        
        $list = $this->list;
        
        $list->setAlias('tc'); // 提前设置别名
        $alias = $list->getFormattedAlias();
        
        $list->setOrderBy(null);
        $list->setOrder(null); // 商机后来被改了，加了 distinct 关键字，不能排序
        $list->setFields(["{$alias}company_id", "{$alias}name"]);
        $list->setLimit(20000);
        
        $data = $list->find();
        $companyIds = array_column($data, 'company_id');
        
        if (empty($companyIds)) {
            return ['data' => [], 'total' => 0];
        }
        
        $list = new \common\library\customer_v3\customer\CustomerList($this->opUser->getClientId());
        $list->setMainCustomerFlag($this->dto->main_customer_flag);
        $list->setCompanyId($companyIds);
        if ($this->dto->send_mail_day > 0) {
            $list->setSendMailDayAndHadSendMail($this->dto->send_mail_day, $this->dto->had_send_mail);
        }
        if ($this->dto->get_contract_list == 2) { //1：邮箱，2：whatsapp
            $list->setFilterEmptyTel(true);
            $list->getFormatter()->snsInfoSetting();
            $list->setContact(['type' => 'whatsapp']);
            $list->setLimit(10000);
            $data = $list->find();
        } else {
            $list->getFormatter()->edmAddressInfoSetting();
            $list->setFilterEmptyEmail(true);
            $list->setLimit(10000);
            $data = $list->find();
            
            $validator = new CEmailValidator();
            $pattern = $validator->pattern;
            
            foreach ($data as $i => $item) {
                if (!preg_match($pattern, $item['email'])) {
                    unset($data[$i]);
                }
            }
        }
        
        $data = array_values($data);
        
        $total = count($data);
        
        return compact('data', 'total');
    }
    
    private function definePreProcessingSettings() {
        
        
        return [
            
            'show_field_key' => [
                'condition' => function () {
                    
                    return (in_array($this->dto->show_field_key, [\common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD,
                                                                  \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD_WEB,
                        ]) || $this->dto->user_num == [0]);
                    
                },
                'action'    => function () {
                    
                    $this->list->getFormatter()->setFieldFunctionalId(PrivilegeConstants::FUNCTIONAL_COMPANY_POOL);
                    
                    $this->list->getFormatter()->setFieldUserId($this->viewingUserId);;
                },
            ],
            'sub_user_id'    => [
                'condition' => function () {
                    
                    return ($this->dto->sub_user_id && $this->dto->sub_user_id != $this->opUser->getUserId());
                },
                'action'    => function () {
                    
                    $this->list->setOpUserId($this->opUser->getUserId());
                    $this->list->getFormatter()->setLoginUserId($this->opUser->getUserId());
                },
            ],
            'scenario'       => [
                'condition' => '',
                'action'    => 'setScenario',
            ],
        ];
        
    }
    
    private function definePostProcessingSettings() {
        
        return [
            
            'keyword'           => [
                'condition' => function () {
                    
                    return (!empty($this->dto->keyword) && !empty($this->dto->swarm_id) && !empty($this->dto->sort_field) && $this->dto->sort_scene != self::SORT_SCENE_SEARCH);
                },
                'action'    => function () {
                    
                    $this->list->setSortBySearchScore(false);
                },
            ],
            'temp_swarm_id'     => [
                'action' => function () {
                    
                    $this->list->getFormatter()->listCompanyCustomer($this->isPrivate ? ItemSettingConstant::ITEM_TYPE_SWARM : ItemSettingConstant::ITEM_TYPE_SWARM_PUBLIC);
                },
            ],
            'sort_field'        => [
                'condition' => function () {
                    
                    return $this->dto->sort_field == 'mail_time';
                },
                'action'    => function () {
                    
                    $this->list->getFormatter()->setShowLastMail(true);
                    $this->list->setHasMailTime(true);
                },
            ],
            'user_id'           => [
                'condition' => function () {
                    
                    sort($this->dto->user_num);
                    
                    return $this->dto->user_id == [$this->opUser->getUserId()] && $this->dto->user_num == [1, 2];
                },
                'action'    => function () {
                    
                    $this->list->getFormatter()->setShowCompanyMark(true);
                    $this->list->getFormatter()->setShowCustomerContactMark(true);
                },
            ],
            'get_count'         => [
                'action' => function () {
                    
                    return $this->list->count();
                },
            ],
            'show_field_key'    => [
                'condition' => function () {
                    
                    return $this->dto->show_field_key == \common\library\setting\user\UserSetting::PUBLIC_COMPANY_LIST_FIELD;
                },
                'action'    => function () {
                    
                    $client = \common\library\account\Client::getClient($this->clientId);
                    
                    $attrs = $client->getExtentAttributes([\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]);
                    
                    if (!$attrs[\common\library\account\Client::EXTERNAL_KEY_CUSTOMER_SHOW_PUBLIC_CONTACT]) {
                        
                        $this->list->getFormatter()->setHideCustomerInfo(true);
                    }
                },
            ],
            'get_contract_list' => [
                'action' => 'setGetContractList',
            ],
        ];
    }
}
