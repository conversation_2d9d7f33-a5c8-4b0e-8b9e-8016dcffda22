<?php

namespace common\library\customer_v3\customer\orm;


use common\components\BaseObject;
use common\library\ai\classify\ai_field_data\CustomerAIFieldData;
use common\library\alibaba\customer\CustomerSyncHelper;
use common\library\custom_field\company_field\duplicate\FieldUniqueValidator;
use common\library\custom_field\CustomFieldService;
use common\library\customer_v3\common\orm\BaseSingleObject;
use common\library\customer_v3\common\strategy\StrategyProcessTrait;
use common\library\customer_v3\common\strategy\VersionStrategy;
use common\library\customer_v3\company\orm\CompanyFilter;
use common\library\customer_v3\customer\strategy\ArchiveStrategy;
use common\library\customer_v3\customer\strategy\EmailRelationStrategy;
use common\library\customer_v3\customer\strategy\FeedBizSetterStrategy;
use common\library\customer_v3\customer\strategy\MailSyncStrategy;
use common\library\email_identity\sync\CustomerSync;
use common\library\history\customer\CustomerCompare;
use common\library\history\customer\CustomerEditCompare;
use common\library\lead\Helper;
use common\library\privilege_v3\field\PrivilegeFieldService;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\report\error\ErrorReport;
use common\library\server\es_search\SearchQueueService;
use common\library\setting\library\config\ConfigApi;
use common\library\setting\library\config\ConfigConstant;
use common\library\swarm\SwarmService;
use common\library\trail\TrailConstants;
use common\library\version\Constant;
use common\models\client\CompanyHistoryPg;
use common\vendor\xiaoman\orm\src\database\data\LowerEqual;
use Constants;
use protobuf\CRMCommon\PBGenderType;
use protobuf\CRMCommon\PBPostGrade;


/**
 *
 *
 * @property string            $customer_id
 * @property string            $client_id
 * @property string            $email
 * @property array             $user_id
 * @property integer           $is_archive
 * @property string            $company_id
 * @property integer           $main_customer_flag
 * @property string            $name
 * @property integer           $post_grade
 * @property string            $post
 * @property string            $tel
 * @property string            $tel_area_code
 * @property string            $tel_full
 * @property string            $birth
 * @property integer           $gender
 * @property string            $remark
 * @property string            $contact
 * @property string            $external_field_data
 * @property string            $group_id
 * @property string            $tag
 * @property string            $user_data
 * @property string            $create_time
 * @property string            $order_time
 * @property string            $archive_time
 * @property string            $update_time
 * @property string            $edm_time
 * @property string            $send_mail_time
 * @property string            $receive_mail_time
 * @property string            $mail_time
 * @property array             $image_list //直接赋值表示 字段数据更新
 * @property array             $full_tel_list
 * @property array             $tel_list
 * @property integer           $email_id
 * @property integer           $order_rank
 * @property integer           $growth_level
 * @property integer           $suspected_invalid_email_flag
 * @property integer           $forbidden_flag
 * @property integer           $reach_count
 * @property integer           $reach_status
 * @property string            $reach_status_time
 * @property integer           $reach_success_count
 * @property integer           $reach_relate_id
 * @property CustomerFormatter $_formatter
 * @method CustomerFormatter getFormatter()
 * @method CustomerOperator getOperator()
 */
class Customer extends BaseSingleObject {
    
    
    use InitCustomerMetadata;
    use StrategyProcessTrait;
    
    
    const GENDER_TYPE_UNKNOWN = 0;
    const GENDER_TYPE_MAN     = 1;
    const GENDER_TYPE_WOMAN   = 2;
    
    const POST_GRADE_UNKNOWN = 0;
    const POST_GRADE_COMMON = 1;
    const POST_GRADE_MIDDLE = 2;
    const POST_GRADE_SENIOR = 3;
    
    const IS_ARCHIVE_NOT = 0;
    const IS_ARCHIVE_YES = 1;
    
    //已禁用标识 0：未禁用 1：已禁用
    const FORBIDDEN_FLAG_NOT = 0;
    const FORBIDDEN_FLAG_YES = 1;
    
    //疑似失效标识 0：未失效 1：已失效
    const SUSPECTED_INVALID_FLAG_NOT = 0;
    const SUSPECTED_INVALID_FLAG_YES = 1;
    
    const POST_GRADE_NAME = [
        self::POST_GRADE_COMMON => '普通职员',
        self::POST_GRADE_MIDDLE => '中层管理者',
        self::POST_GRADE_SENIOR => '高层管理者',
    ];
    
    const GENDER_TYPE_TO_PB_MAP = [
        self::GENDER_TYPE_UNKNOWN => PBGenderType::GENDER_TYPE_UNKNOWN,
        self::GENDER_TYPE_MAN => PBGenderType::GENDER_TYPE_MAN,
        self::GENDER_TYPE_WOMAN => PBGenderType::GENDER_TYPE_WOMAN,
    ];
    
    const POST_GRADE_TO_PB_MAP = [
        self::POST_GRADE_UNKNOWN => PBPostGrade::POST_GRADE_NONE,
        self::POST_GRADE_COMMON => PBPostGrade::POST_GRADE_COMMON,
        self::POST_GRADE_MIDDLE => PBPostGrade::POST_GRADE_MIDDLE,
        self::POST_GRADE_SENIOR => PBPostGrade::POST_GRADE_SENIOR,
    ];
    
    const TYPE_MINE      = 1; // 我的客户
    const TYPE_COLLEAGUE = 2; // 同事的客户
    const TYPE_PUBLIC    = 3; // 公海客户
    
    //触达状态
    const REACH_STATUS_DEFAULT     = 0;//默认
    const MAIL_REACH_STATUS_SENDED = 101;//邮件送达
    const MAIL_REACH_STATUS_OPEN   = 102;//邮件打开
    
    const EDM_REACH_STATUS_SENDED = 201;//EDM送达
    const EDM_REACH_STATUS_OPEN   = 202;//EDM打开
    
    //添加状态需要同步
    const REACH_STATUS_NAME = [
        self::MAIL_REACH_STATUS_SENDED => '邮件:未打开',
        self::MAIL_REACH_STATUS_OPEN   => '邮件:已打开',
        
        self::EDM_REACH_STATUS_SENDED => 'EDM:未打开',
        self::EDM_REACH_STATUS_OPEN   => 'EDM:已打开',
    ];
    
    //图片数量限制
    const IMAGE_LIMIT = 10;
    
    //排他锁
    const PREFIX_OF_LOCK = 'crm:customer:lock';
    
    protected bool $formatAttributesForSave = true;
    
    protected bool $cleanFilterAfterLoad = true;
    
    protected static $_hasSingleTel = true;
    
    protected $_clientId;
    protected $_isValidated         = false;
    protected $_historyTypeRecover  = false;
    protected $_historyTypeMerge    = false;
    protected $_operatorUserId;
    protected $_allowDuplicateTel   = true;
    protected $_allowDuplicateEmail = false;
    protected $_updateSearchIndex   = true;
    protected $_isChangeProfile;
    protected $sourceCustomerId;
    protected $requiredEmail        = true; //批量操作customer时从外部置入
    protected $skipEmailValidate    = false;
    
    protected $_fieldEditType = CustomerAIFieldData::FIELD_EDIT_TYPE_BY_USER;//字段更新类型
    
    protected $_fieldEditReferId;//当更新时需要将相应referId记录到操作历史
    protected $_fieldEditReferType;//referId 对于的类型
    
    protected $_skipDuplicateCheck;//跳过判重检测，客户合并场景使用

//   company处理过了
    protected $uneditableEmailFlag = null;
    
    protected $customerParamIndex = null;
    protected $poolId;
    
    /**
     * Customer constructor.
     *
     * @param int|null $id
     * @param int|null $clientId
     */
    public function __construct($clientId, $id = null) {
        
        $this->_clientId = $clientId;
        
        $this->_formatter = new CustomerFormatter($this->getMetadata(), $clientId);
        
        if ($id) {
            $this->loadById($id);
        }
        
        $this->setSkipPrivilegeField(false); // 保持旧逻辑，默认为false
        
        parent::__construct($this->_clientId);
        
    }
    
    public static function getModuleType() {
        
        return Constants::TYPE_CUSTOMER;
    }
    
    public function beforeCreate() {
        
        //$this->handlePrivilegeFields();
        $this->handlePrivilegeFieldsByWriteBack();
        
        $this->formatExternalData();
        
        if (!$this->_isValidated)
            $this->validate();
        
        if (empty($this->_attributes['company_id']))
            throw new \RuntimeException(\Yii::t('customer', 'Company id could not be empty'), \ErrorCode::CODE_COMPANY_CUSTOMER_COMPANY_ID_EMPTY);
        
        //不仅仅 前端传入数据需要处理，数据导入等业务也可能会调用 此处需要对image_list的数据格式过滤
        $this->_attributes['image_list'] = empty($this->_attributes['image_list']) ? [] : $this->_attributes['image_list'];
        
        //新邮箱需要检查锁
        if ($this->isLock()) {
            $e = new \ProcessException('新建联系人检查邮箱获取锁失败');
            $e->setModule(ErrorReport::PHP_CRM_WARNING_NOTICE);
            ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), $this->user_id);
            
            throw new \RuntimeException(\Yii::t('customer', 'email request already exists'), \ErrorCode::CODE_COMPANY_CUSTOMER_EXISTS_EMAIL);
        }
        
        $this->lock();
        
        $date = date('Y-m-d H:i:s');
        $this->_attributes['update_time'] = $date;
        
        //机器翻译 查看Map 反向 V2
        if (!empty($this->_attributes['external_field_data'])) {
            $this->_attributes['external_field_data'] = \common\library\translate\Helper::translateReverseExtField($this->client_id, \CONSTANTS::TYPE_CUSTOMER,$this->_attributes['external_field_data']);
        }
        
        $this->_attributes['is_archive'] = $this->_attributes['is_archive'] ?? 1;
        $this->_attributes['post_grade'] = $this->_attributes['post_grade'] ?? 0;
        $this->_attributes['create_time'] = $this->_attributes['create_time'] ?? $date;
        $this->_attributes['user_data'] = $this->_attributes['user_data'] ?? [];
        $this->_attributes['user_id'] = $this->_attributes['user_id'] ?? [];
        $this->_attributes['contact'] = $this->_attributes['contact'] ?? [];
        $this->_attributes['external_field_data'] = $this->_attributes['external_field_data'] ?? [];
        $this->_attributes['tag'] = $this->_attributes['tag'] ?? [];
        $this->_attributes['image_list'] = $this->_attributes['image_list'] ?? [];
        $this->_attributes['gender'] = $this->_attributes['gender'] ?? 0;
        
        $this->_attributes['archive_time'] = $this->_attributes['create_time'] ?? $date;
        $this->_attributes['order_time'] = $this->_attributes['create_time'] ?? $date;
        
        
        //图片数量检测
        if (!empty($this->_attributes['image_list'])) {
            if (count($this->_attributes['image_list']) > self::IMAGE_LIMIT) {
                throw new \RuntimeException(\Yii::t('customer', 'Contact pictures can only be saved up to {num}', ['{num}' => self::IMAGE_LIMIT]), \ErrorCode::CODE_COMPANY_CUSTOMER_IMAGE_LIMIT);
            }
        }
        
        // 清空这里面的数据
        if (static::$_hasSingleTel) {
            $this->_attributes['tel'] = '';
            $this->_attributes['tel_area_code'] = '';
            $this->_attributes['tel_full'] = '';
        }
        
        
        $this->_attributes['client_id'] = $this->_clientId;
        $this->_attributes['gender'] = intval($this->_attributes['gender']);
        $this->_attributes['post_grade'] = intval($this->_attributes['post_grade']);

//        todo:???
//        unset($this->_attributes['reach_status'], $this->_attributes['reach_count'], $this->_attributes['reach_status_time'], $this->_attributes['reach_success_count']);
        
        return parent::beforeCreate(); // TODO: Change the autogenerated stub
    }
    
    public function beforeUpdate() {
        
        
        //$this->handlePrivilegeFields();
        $this->handlePrivilegeFieldsByWriteBack();
        
        $this->formatExternalData();
        
        if (!$this->_isValidated)
            $this->validate();
        
        if ($this->_attributes['is_archive'] && empty($this->_attributes['company_id']))
            throw new \RuntimeException(\Yii::t('customer', 'Company id could not be empty'), \ErrorCode::CODE_COMPANY_CUSTOMER_COMPANY_ID_EMPTY);
        
        //不仅仅 前端传入数据需要处理，数据导入等业务也可能会调用 此处需要对image_list的数据格式过滤
        $this->_attributes['image_list'] = empty($this->_attributes['image_list']) ? [] : $this->_attributes['image_list'];
        
        //新邮箱需要检查锁
        if ($this->_attributes['email'] != $this->_oldAttributes['email']) {
            if ($this->isLock()) {
                $e = new \ProcessException('新建联系人检查邮箱获取锁失败');
                $e->setModule(ErrorReport::PHP_CRM_WARNING_NOTICE);
                ErrorReport::phpError(new \CExceptionEvent(null, $e), $e->getTrace(), $this->user_id);
                
                throw new \RuntimeException(\Yii::t('customer', 'email request already exists'), \ErrorCode::CODE_COMPANY_CUSTOMER_EXISTS_EMAIL);
            }
            
            $this->lock();
        }
        
        $date = date('Y-m-d H:i:s');
        $this->_attributes['update_time'] = $date;
        if ($this->_attributes['is_archive'] && !$this->_oldAttributes['is_archive']) {
            $this->_attributes['archive_time'] = $date;
        }
        
        
        //图片数量检测
        if (!empty($this->_attributes['image_list'])) {
            if (count($this->_attributes['image_list']) > self::IMAGE_LIMIT) {
                throw new \RuntimeException(\Yii::t('customer', 'Contact pictures can only be saved up to {num}', ['{num}' => self::IMAGE_LIMIT]), \ErrorCode::CODE_COMPANY_CUSTOMER_IMAGE_LIMIT);
            }
        }
        
        // 清空这里面的数据
        if (static::$_hasSingleTel) {
            $this->_attributes['tel'] = '';
            $this->_attributes['tel_area_code'] = '';
            $this->_attributes['tel_full'] = '';
        }
        
        
        $this->_attributes['gender'] = intval($this->_attributes['gender']);
        $this->_attributes['post_grade'] = intval($this->_attributes['post_grade']);

        if (is_null($this->_attributes['birth'])) {
            $this->_attributes['birth'] = '';
        }
        
//        todo:???
//        unset($this->_attributes['reach_status'], $this->_attributes['reach_count'], $this->_attributes['reach_status_time'], $this->_attributes['reach_success_count']);
        
        return parent::beforeUpdate(); // TODO: Change the autogenerated stub
    }
    
    public function beforeDelete() {
        
        return parent::beforeDelete(); // TODO: Change the autogenerated stub
    }
    
    public function afterCreate() {
        
        parent::afterCreate(); // TODO: Change the autogenerated stub
        
        $this->processTask($this->getAfterCreateProcessTaskList());
    }
    
    public function afterUpdate() {
        
        parent::afterUpdate(); // TODO: Change the autogenerated stub
        
        $this->processTask($this->getAfterUpdateProcessTaskList());
        
        $this->unLock();
    }
    
    public function afterDelete() {
        
        parent::afterDelete(); // TODO: Change the autogenerated stub
    }
    
    
    /**
     * @param mixed $sourceCustomerId
     */
    public function setSourceCustomerId($sourceCustomerId) {
        
        $this->sourceCustomerId = $sourceCustomerId;
    }
    
    /**
     * @return mixed
     */
    public function getSourceCustomerId() {
        
        return $this->sourceCustomerId;
    }
    
    /**
     * @return mixed
     */
    public function getIsChangeProfile() {
        
        return $this->_isChangeProfile;
    }
    
    
    public function setOperatorUserId($userId) {
        
        $this->_operatorUserId = $userId;
        $this->getFormatter()->setFieldUserId($userId);
    }
    
    public function setHistoryTypeRecover($flag) {
        
        $this->_historyTypeRecover = $flag;
    }
    
    public function setHistoryTypeMerge($flag) {
        
        $this->_historyTypeMerge = $flag;
    }
    
    public function setRequiredEmail(bool $requiredEmail) {
        
        $this->requiredEmail = $requiredEmail;
    }
    
    public function setSkipEmailValidate(bool $skipEmailValidate) {
        
        $this->skipEmailValidate = $skipEmailValidate;
    }
    
    
    public function setPoolId($poolId) {
        
        $this->poolId = $poolId;
    }
    
    /**
     * @return null
     */
    public function getCustomerParamIndex() {
        
        return $this->customerParamIndex;
    }
    
    /**
     * @param null $customerParamIndex
     */
    public function setCustomerParamIndex($customerParamIndex): void {
        
        $this->customerParamIndex = $customerParamIndex;
    }
    
    public function isExist() {
        
        return !$this->isNew() && $this->is_archive;
    }
    
    private function getAvailablePool($poolId = null, int $userId = null) {
        
        $poolId = array_filter((array)$poolId, 'is_numeric');
        
        $user = empty($userId) ? \User::getLoginUser() : \User::getUserObject($userId);;
        
        if (!$user->isEmpty() && empty($poolId)) {
            
            $poolId = array_column(\common\library\customer\pool\Helper::getCustomerPoolList(0, 0, 0, 0, 0, $user->getUserId()), 'pool_id');
        }
        
        return $poolId;
    }
    
    public function loadById($customerId, $throwException = false) {
        
        $this->load(['client_id' => $this->_clientId, 'customer_id' => $customerId], $throwException);
        
        //每次加载，默认false
        $this->_isChangeProfile = false;
        
        return $this;
    }
    
    public function loadByEmail($email, $companyId = 0, $throwException = false) {
        
        !empty($email) && $this->load(array_filter((['email' => new LowerEqual($email), 'company_id' => $companyId])), $throwException);
        
        return $this;
    }
    
    public function loadByEmailToPool($email, $companyId = 0, $poolId = null, $userId = null, $throwException = false) {
        
        if (empty($email)) {
            
            return $this;
        }
        
        if (!empty($companyId)) {
            
            return $this->loadByEmail($email, $companyId, $throwException);
        }
        
        $companyFilter = new CompanyFilter($this->_clientId);
        
        $companyFilter->pool_id = $this->getAvailablePool($poolId, $userId);
        
        
        $filter = new CustomerFilter($this->_clientId);
        
        $filter->initJoin();
        
        $filter->innerJoin($companyFilter);
        
        $filter->on('company_id', 'company_id');
        
        
        $data = $filter->wheres(array_filter([
            'email'      => new LowerEqual($email),
            'company_id' => $companyId,
        ]))->rawData();
        
        $this->setModel(current($data), $throwException);
        
        $this->filter = null;
        
        return $this;
    }
    
    public function loadByTel($tel, $companyId = 0, $throwException = false) {
        
        $tel = is_array($tel) ? array_filter($tel) : $tel;
        if (empty($tel)) {
            $this->setModel(null);
            return $this;
        }
        
        $condition = [];
        
        if ($companyId > 0) {
            $condition['company_id'] = $companyId;
        }
        
        $filter = new CustomerFilter($this->_clientId);
        $filter->limit(1);
        $filter->order('create_time', 'asc');
        $filter->wheres($condition);
        
        if (is_array($tel)) {
            $filter->rawWhere(' AND full_tel_list && ARRAY[\'' . implode("','", $tel) . '\']::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[]');
        } else {
            $filter->rawWhere(' AND full_tel_list @> ARRAY[\'' . $tel . '\']::text[] and is_archive=1 AND full_tel_list <> \'{}\'::text[]');
        }
        
        $data = $filter->rawData();
        $this->setModel(current($data), $throwException);
        
        return $this;
    }
    
    public function loadByTelToPool($tel, $companyId = 0, $poolId = null, $userId = null, $throwException = false) {
        
        $user = empty($userId) ? \User::getLoginUser() : \User::getUserObject($userId);
        $tel = is_array($tel) ? array_filter($tel) : $tel;
        
        if (empty($tel)) {
            $this->setModel(null);
            return $this;
        }
        
        $condition = [];
        
        if ($companyId > 0) {
            $condition['company_id'] = $companyId;
        }
        
        $filter = new CustomerFilter($this->_clientId);
        $filter->limit(1);
        $filter->order('create_time', 'asc');
        $filter->wheres($condition);
        
        $companyFilter = new CompanyFilter($this->_clientId);
        
        $filter->initJoin();
        $filter->innerJoin($companyFilter)->on('company_id', 'company_id');
        $filter->select($this->getMetadata()->getAttributesKeys());
        
        if (is_array($tel)) {
            $filter->rawWhere(' AND tbl_customer.full_tel_list && ARRAY[\'' . implode("','", $tel) . '\']::text[] and tbl_customer.is_archive=1 AND tbl_customer.full_tel_list <> \'{}\'::text[]');
        } else {
            $filter->rawWhere(' AND tbl_customer.full_tel_list @> ARRAY[\'' . $tel . '\']::text[] and tbl_customer.is_archive=1 AND tbl_customer.full_tel_list <> \'{}\'::text[]');
        }
        
        if (empty($companyId) && !$user->isEmpty()) {
            $poolId = $poolId ?? array_column(\common\library\customer\pool\Helper::getCustomerPoolList(0, 0, 0, 0, 0, $user->getUserId()), 'pool_id');
            $poolId = is_array($poolId) ? $poolId : [$poolId];
            $companyFilter->wheres(['pool_id' => $poolId]);
        }
        
        $data = $filter->rawData();
        $this->setModel(current($data), $throwException);
        
        return $this;
    }
    
    /**
     * @param bool $allowTelDuplicate
     */
    public function setAllowDuplicateTel(bool $allowDuplicateTel) {
        
        $this->_allowDuplicateTel = $allowDuplicateTel;
    }
    
    public function setFieldEditType($type) {
        
        $this->_fieldEditType = $type;
    }
    
    public function setFieldEditRefer($referId, $referType) {
        
        $this->_fieldEditReferId = $referId;
        $this->_fieldEditReferType = $referType;
    }
    
    public function setSkipDuplicateCheck(bool $bool) {
        
        $this->_skipDuplicateCheck = $bool;
    }
    
    
    public function validate() {
        
        $this->_operatorUserId = $this->_operatorUserId ?? \User::getLoginUser()->getUserId();
        $duplicateRuleType = \common\library\customer\pool\Helper::getCheckPoolDuplicateSwitch($this->_clientId);
        if ($duplicateRuleType && !isset($this->poolId)) {
            $this->poolId = current(\common\library\customer\pool\Helper::getUserLastPoolMap($this->_clientId, [$this->_operatorUserId ?? 0], empty($this->_attributes['user_id'] ?? [])));
        }
        
        $clientId = $this->_clientId;

//        联系人强制四选一
        if (empty($this->_attributes['email']) && empty($this->_attributes['name']) && empty($this->_attributes['tel_list']) && empty($this->_attributes['contact'])) {
            throw new \RuntimeException(\Yii::t('field', 'Please write at least one of the nicknames, email addresses, contact numbers, and social platforms'), \ErrorCode::CODE_COMPANY_CUSTOMER_REQUIRED);
        }
        
        if ($this->_fieldEditType == CustomerAIFieldData::FIELD_EDIT_TYPE_BY_ALIBABA_CUSTOMER_SYNC) {
            // 多店铺字段必填策略
            $multiStoreFieldTactic = (new ConfigApi($clientId, \Constants::TYPE_CUSTOMER))->getCustomerSyncSettingByKey(ConfigConstant::CUSTOMER_MULTI_STORE_FIELD_TACTIC);
            $requiredList = \common\library\alibaba\customer\CustomerSyncHelper::getMultiStoreRequiredField($clientId, $multiStoreFieldTactic);
            $this->requiredEmail = $requiredList['customer.email']['require'] ?? 0;
        } else {
            $this->requiredEmail = CustomFieldService::getClientCustomerRequire($clientId);
        }
        
        $this->_attributes['email'] = strtolower(trim($this->_attributes['email'] ?? ''));

//		客户通邮箱脏数据
        if (!empty($this->_attributes['email']) && !filter_var($this->_attributes['email'], FILTER_VALIDATE_EMAIL)) {
            
            throw new \RuntimeException(\Yii::t('edm', 'email address is illegal'));
        }
        
        
        if (\common\library\customer\blacklist\Helper::match($clientId, $this->_attributes['email']))
            throw new \RuntimeException($this->_attributes['email'] . "在建档黑名单中", \ErrorCode::CODE_COMPANY_CUSTOMER_BACK_LIST);
        
        if (!$this->isNew()) {
            if ($this->uneditableEmailFlag || ($this->uneditableEmailFlag === null && strcasecmp($this->_oldAttributes['email'], $this->email) && !$this->canEditEmail())) {
                throw new \RuntimeException("不能修改已经存在的customer的email", \ErrorCode::CODE_COMPANY_CUSTOMER_EDIT_EMAIL);
            }
        }

//		contact格式校验
        if (!empty($this->_attributes['contact'])) {
            
            array_walk($this->_attributes['contact'], function ($item) {
                
                if (empty($item['type']) || empty($item['value']) || is_array($item['type']) || is_array($item['value'])) {
                    
                    throw new \RuntimeException('社交平台格式错误');
                }
            });
        }
        
        if (static::$_hasSingleTel) {
            $this->_attributes['tel'] = trim($this->_attributes['tel'] ?? '');
            $this->_attributes['tel_area_code'] = trim($this->_attributes['tel_area_code'] ?? '');
            $this->_attributes['tel_full'] = $this->_attributes['tel_area_code'] . $this->_attributes['tel'];
            
            if (!empty($this->_attributes['tel']) && empty(\Util::numericString($this->_attributes['tel']))) {
                throw new \RuntimeException('座机号码格式错误');
            }
        }
        
        // 处理电话
        $this->_attributes['tel_list'] = $this->_attributes['tel_list'] ?? [];
        // 兼容前端提交错误的情况
        if (!is_array($this->_attributes['tel_list'])) {
            \LogUtil::warning("tel_list value type error . " . var_export($this->_attributes['tel_list'], true));
            $this->_attributes['tel_list'] = empty($this->_attributes['tel_list']) ? [] : [['', $this->_attributes['tel_list']]];
        }
        
        $telList = [];
        $fullTelList = [];
        foreach ($this->_attributes['tel_list'] as $elem) {
            $areaCode = \Util::numericString($elem[0] ?? $elem['tel_area_code'] ?? '');
            $tel = trim($elem[1] ?? $elem['tel'] ?? '');
            
            if (empty($areaCode) && empty($tel))
                continue;
            
            $fullTel = $areaCode . \Util::numericString($tel);
            
            $telList[] = [$areaCode, $tel];
            $fullTel !== '' && $fullTelList[] = $fullTel;
        }
        
        $this->_attributes['tel_list'] = $telList;
        $this->_attributes['full_tel_list'] = $fullTelList;
        if (count($this->_attributes['tel_list']) > 10)
            throw new \RuntimeException(\Yii::t('customer', 'No more than {num} contact numbers', ['{num}' => 10]), \ErrorCode::CODE_COMPANY_CUSTOMER_LIMIT_TEL);
        
        if ($this->isNew() || !empty($this->_attributes['is_archive'])) {
            if (!$this->_skipDuplicateCheck)//客户合并场景会跳过检测
            {
                
                (new FieldUniqueValidator($this->_clientId, \Constants::TYPE_CUSTOMER, $this->_attributes['company_id'] ?? 0))
                    ->setPreventOnly(true)
                    ->setPoolId($this->poolId)
                    ->setAttributes(array_replace($this->external_field_data ?: [], $this->_attributes))
                    ->validate();
            }
        }
        
        if ((!isset($this->_attributes['is_archive']) || $this->_attributes['is_archive']) && ($this->requiredEmail && !$this->skipEmailValidate) && empty($this->_attributes['email']))
            throw new \RuntimeException(\Yii::t('customer', 'Email could not be empty'), \ErrorCode::CODE_COMPANY_CUSTOMER_EMAIL_EMPTY);
        
        // 这个别乱移 一定要放在最后面
        $this->_isValidated = true;
    }
    
    public function skipDuplicateField(array $data):array
    {
        if (empty($data)) {
            return [];
        }
        $skipField = [];
        
        $referIds = $this->isNew() ? [] : [$this->customer_id];
        $attributes = $this->getRawAttributes();
        
        //特殊处理电话
        $data['full_tel_list'] = Helper::telListFormatFullTelList($data['tel_list'] ?? []);
        
        $messages = (new FieldUniqueValidator($this->getClientId(), \Constants::TYPE_CUSTOMER, $referIds))
            ->setMessageOnly(true)
            ->setPreventOnly(true)
            ->setDuplicateLeadSettingFlag(true)
            ->setAttributes(array_replace($attributes, $data))
            ->setFilterMatchRuleMode(\common\library\custom_field\company_field\duplicate\MatchRules::FILTER_MATCH_RULE_MODE_ALL)
            ->validate();
        
        foreach ($messages as $message) {
            if (!empty($data[$message['field']])) {
                if ($message['field'] == 'tel_list') {
                    unset($data['full_tel_list']);
                }
                $skipField['field'] = $message['field'];
                $skipField['value'] = $data[$message['field']];
                unset($data[$message['field']]);
            }
        }
        return [$data,$skipField];
    }
    
    /**
     * todo
     *
     * @param bool $allowRemoveMain
     * @param bool $sync
     * @return bool
     */
    public function remove($allowRemoveMain = false, $sync = true) {
        
        if (!$allowRemoveMain && $this->_attributes['main_customer_flag'])
            throw new \RuntimeException(\Yii::t('customer', 'Cannot delete primary contact'), \ErrorCode::CODE_COMPANY_CUSTOMER_CANT_DELETE_MAIN_CUSTOMER);
        
        $companyId = $this->_attributes['company_id'];
        
        $this->_attributes['is_archive'] = 0;
        $this->_attributes['company_id'] = 0;
        $this->_attributes['main_customer_flag'] = 0;
        $this->_attributes['user_id'] = [];
        $this->_isValidated = true;
        
        $result = $this->update();
        
        if ($this->_updateSearchIndex && !$allowRemoveMain) {
            SearchQueueService::pushCompanyQueue($this->_operatorUserId, $this->client_id, [$companyId],
                \Constants::SEARCH_INDEX_TYPE_UPDATE);
        }
        
        if ($result && $sync) {
            (new CustomerSync($this->_clientId))->setFindEmail([$this->email])->sync();
        }
        
        // 更新客户表联系人数量
        if ($result) {
            $company = new \common\library\customer_v3\company\orm\Company($this->_clientId, $companyId);
            $company->getOperator()->resetCustomerCount();
        }
        
        //解除阿里客户联系人关联关系
        CustomerSyncHelper::batchUnbindCustomerId($this->_clientId, [$this->customer_id]);
        
        // 507
        \common\library\queue_v2\QueueService::dispatch(new \common\library\queue_v2\job\TipsPushTodoJob([
            'feed_type_id'     => '*******',
            'action'           => 'remove',
            'feed_type'        => 'contacts_invalid',
            'company_id'       => $companyId,
            'customer_id'      => [$this->_attributes['customer_id']],
            'emails'           => [$this->_oldAttributes['email']],
            'client_id'        => $this->client_id,
            'user_id'          => [],
            'operator_user_id' => $this->_operatorUserId,
        ]));
        
        (new SwarmService($this->_clientId))->refreshByRefer($companyId);
        
        return $result;
    }
    
    /**
     * 返回是否可以编辑成新的联系人邮箱
     * 仅当新建联系人 和 编辑已存在的联系人没有产生邮箱动态时可以编辑邮箱
     *
     * @return bool
     */
    public function canEditEmail() {
        
        if ($this->isNew() || empty($this->_oldAttributes['email']) || !\common\library\trail\Helper::hasCompanyTrail($this->_clientId, $this->company_id, $this->customer_id, [TrailConstants::MODULE_MAIL, TrailConstants::MODULE_EDM])) return true;
        return false;
    }
    
    public function getPrivilegeFieldFunctionalId() {
        
        return (empty($this->user_id) || $this->user_id == '{}') ? PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : PrivilegeConstants::FUNCTIONAL_CUSTOMER;
    }
    
    public function getPrivilegeFieldReferType() {
        
        return \Constants::TYPE_CUSTOMER;
    }
    
    public function getClientId() {
        
        return $this->_clientId;
    }
    
    public function getPrivilegeFieldUserId() {
        
        return $this->_operatorUserId;
    }
    
    private function getCacheKey($email) {
        
        return self::PREFIX_OF_LOCK . ':' . $this->_clientId . ':' . md5($email);
    }
    
    public function lock() {
        
        if (empty($this->_attributes['email'])) {
            return false;
        }
        $email = strtolower($this->_attributes['email']);
        $key = $this->getCacheKey($email);
        return \Yii::app()->redis->set($key, $email, 60);
    }
    
    public function unLock() {
        
        if (empty($this->_attributes['email'])) {
            return false;
        }
        $email = strtolower($this->_attributes['email']);
        $key = $this->getCacheKey($email);
        return \Yii::app()->redis->delete($key);
    }
    
    public function isLock() {
        
        if (empty($this->_attributes['email'])) {
            return false;
        }
        $email = strtolower($this->_attributes['email']);
        $key = $this->getCacheKey($email);
        $data = \Yii::app()->redis->get($key);
        if ($data && $data == $email) {
            return true;
        }
        
        return false;
    }
    
    
    /**
     * @param null $uneditableEmailFlag
     */
    public function setUneditableEmailFlag($uneditableEmailFlag): void {
        
        $this->uneditableEmailFlag = $uneditableEmailFlag;
    }
    
    private function getAfterCreateProcessTaskList() {
        
        return [
            'archive'              => [
                'task'              => ArchiveStrategy::class,
                'required_field'    => ['company_id'],
                'required_property' => ['sourceCustomerId'],
            ],
            'processHistoryAfterCreate',
            'mail_sync'            => [
                'task'  => MailSyncStrategy::class,
                'extra' => [
                    'email' => $this->_attributes['email'],
                ],
            ],
            'email_relation'       => [
                'task'              => EmailRelationStrategy::class,
                'required_field'    => ['email', 'company_id', 'is_archive'],
                'required_property' => ['_isNew', '_historyTypeRecover'],
            ],
            'version'              => [
                'task'  => VersionStrategy::class,
                'extra' => [
                    'primary_key'   => 'company_id',
                    'type'          => Constant::COMPANY_MODULE_ADD_CUSTOMER,
                ],
            ],
            'feed_biz_better' => [
                'task'           => FeedBizSetterStrategy::class,
                'required_field' => ['email', 'company_id'],
                'extra'          => [
                    'flag'    => ($this->_attributes['company_id'] != 0 && empty($this->_oldAttributes['company_id'])),
                    'user_id' => $this->_operatorUserId ?: \User::getLoginUser()->getUserId(),
                ],
            ],
            'unLock',
        ];
    }
    
    private function getAfterUpdateProcessTaskList() {
        
        return [
            'archive'              => [
                'task'              => ArchiveStrategy::class,
                'required_field'    => ['company_id'],
                'required_property' => ['sourceCustomerId'],
            ],
            'processHistoryAfterUpdate',
            'mail_sync'            => [
                'task'  => MailSyncStrategy::class,
                'extra' => [
                    'flag'  => ($this->_attributes['company_id'] && $this->_attributes['email'] != $this->_oldAttributes['email']),
                    'email' => [$this->_attributes['email'], $this->_oldAttributes['email']],
                ],
            ],
            'email_relation'       => [
                'task'              => EmailRelationStrategy::class,
                'required_field'    => ['email', 'company_id', 'is_archive'],
                'required_property' => ['_isNew', '_historyTypeRecover'],
            ],
            'version'              => [
                'task'  => VersionStrategy::class,
                'extra' => [
                    'primary_key'   => 'company_id',
                    'type'          => Constant::COMPANY_MODULE_ADD_CUSTOMER,
                ],
            ],
            'FeedBizSetterService' => [
                'task'           => FeedBizSetterStrategy::class,
                'required_field' => ['email', 'company_id'],
                'extra'          => [
                    'flag'    => ($this->_attributes['company_id'] != 0 && empty($this->_oldAttributes['company_id'])),
                    'user_id' => $this->_operatorUserId ?: \User::getLoginUser()->getUserId(),
                ],
            ],
            'unLock',
        ];
    }
    
    private function processHistoryAfterCreate() {
        
        $extraData = [
            'company_id'  => $this->_attributes['company_id'],
            'customer_id' => $this->_attributes['customer_id'],
        ];
        
        if ($this->_fieldEditReferId) {
            $extraData['refer_id'] = $this->_fieldEditReferId;
            $extraData['refer_type'] = $this->_fieldEditReferType;
            $extraData['user_id'] = $this->_operatorUserId;
        }
        
        $history = null;
        $compareReturn = null;
        if ($this->_attributes['is_archive'] && $this->_attributes['company_id']) {
            
            $history = new CustomerCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType(CompanyHistoryPg::TYPE_NEW_CUSTOMER);
            $history->setData($this->_attributes, []);
            $compareReturn = $history->build($this->_operatorUserId);
        }
        
        if ($history && !empty($compareResult = $history->getProductDiffData())) {
            $compareFields = array_column($compareResult, 'id');
            $aiFieldDataClass = new CustomerAIFieldData($this->_clientId, $this->customer_id);
            $aiFieldDataClass->setFieldDataByType($compareFields, $this->_fieldEditType);
            $aiFieldDataClass->save();
            //对比差异，有变更
            if ($compareReturn) {
                $this->_isChangeProfile = true;
            }
        }
    }
    
    private function processHistoryAfterUpdate() {
        
        $extraData = [
            'company_id'  => $this->_attributes['company_id'],
            'customer_id' => $this->_attributes['customer_id'],
        ];
        
        if ($this->_fieldEditReferId) {
            $extraData['refer_id'] = $this->_fieldEditReferId;
            $extraData['refer_type'] = $this->_fieldEditReferType;
            $extraData['user_id'] = $this->_operatorUserId;
        }
        
        $history = null;
        $compareReturn = null;
        
        if ((!$this->_oldAttributes['is_archive'] && !$this->_historyTypeRecover)
            && $this->_attributes['is_archive'] && $this->_attributes['company_id']
        ) {
            $history = new CustomerCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType(CompanyHistoryPg::TYPE_NEW_CUSTOMER);
            $history->setData($this->_attributes, []);
            $compareReturn = $history->build($this->_operatorUserId);
            
        } elseif ($this->_historyTypeRecover && !$this->isNew() && $this->_attributes['is_archive'] && !$this->_oldAttributes['is_archive']) {
            $history = new CustomerCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType(CompanyHistoryPg::TYPE_CUSTOMER_RECOVER);
            $history->setData($this->_attributes, []);
            $compareReturn = $history->build($this->_operatorUserId);
        } else if ($this->_attributes['company_id']) {
            $historyType = $this->_historyTypeMerge ? CompanyHistoryPg::TYPE_MERGE_CUSTOMER_INFO : CompanyHistoryPg::TYPE_EDIT_CUSTOMER;
            
            $history = new CustomerEditCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType($historyType);
            $history->setData($this->_attributes, $this->_oldAttributes);
            $compareReturn = $history->build($this->_operatorUserId);
            
            
            // 只关心编辑联系人, 合并联系人的逻辑不要在这里做, 在 CompanyMerge.php
            if ($historyType === CompanyHistoryPg::TYPE_EDIT_CUSTOMER && $this->_attributes['email'] != $this->_oldAttributes['email']) {
                // 一定要先删后增顺序, 因为 updateCustomerEmailRelation 内部会更新 tbl_customer.email_id, 如果先增后删, 那么字段最终会被错误抹去
                // 先删除旧的关系
                if ($this->_oldAttributes['email']) {
                    \common\library\email\Helper::updateCustomerEmailRelation($this->client_id, $this->customer_id, $this->_oldAttributes['email'], false);
                }
                // 再增加新的关系
                if ($this->_attributes['email']) {
                    \common\library\email\Helper::updateCustomerEmailRelation($this->client_id, $this->customer_id, $this->_attributes['email'], true);
                }
            }
        } else if (!$this->isNew() && !$this->_attributes['is_archive'] && $this->_oldAttributes['is_archive']) {
            $extraData['company_id'] = $this->_oldAttributes['company_id'];
            
            $history = new CustomerCompare($this->_clientId);
            $history->setEditInfo($this->_fieldEditType);
            $history->setExtraData($extraData);
            $history->setType(CompanyHistoryPg::TYPE_DELETE_CUSTOMER);
            $history->setData($this->_attributes, []);
            $compareReturn = $history->build($this->_operatorUserId);
        }
        
        if ($history && !empty($compareResult = $history->getProductDiffData())) {
            $compareFields = array_column($compareResult, 'id');
            $aiFieldDataClass = new CustomerAIFieldData($this->_clientId, $this->customer_id);
            $aiFieldDataClass->setFieldDataByType($compareFields, $this->_fieldEditType);
            $aiFieldDataClass->save();
            //对比差异，有变更
            if ($compareReturn) {
                $this->_isChangeProfile = true;
            }
        }
    }

    public function loadEmailsByCompanyIds($companyIds) {
        $filter = $this->getFilter();
        $filter->company_id = $companyIds;
        $filter->is_archive = BaseObject::ENABLE_FLAG_TRUE;
        $filter->select(['email']);
        return array_unique(array_filter(array_column($filter->find()->getAttributes(), 'email')));
    }
    
}
