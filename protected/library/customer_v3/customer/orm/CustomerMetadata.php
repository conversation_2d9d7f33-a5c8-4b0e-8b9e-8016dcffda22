<?php

namespace common\library\customer_v3\customer\orm;

use common\library\object\field\util\FieldTransferUtil;
use common\library\object\object_define\Constant;
use common\library\object\traits\BizObjectClass;
use xiaoman\orm\metadata\Metadata;
use xiaoman\orm\metadata\MetadataV2;

class CustomerMetadata extends MetadataV2 {
    
    
    use BizObjectClass;
    
    protected $columns = [
        'customer_id'                  => [
            'type'     => 'bigint',
            'name'     => 'customer_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'client_id'                    => [
            'type'     => 'bigint',
            'name'     => 'client_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'email'                        => [
            'type'     => 'varchar',
            'name'     => 'email',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'user_id'                      => [
            'type'     => 'array',
            'name'     => 'user_id',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'is_archive'                   => [
            'type'     => 'integer',
            'name'     => 'is_archive',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'company_id'                   => [
            'type'     => 'bigint',
            'name'     => 'company_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'main_customer_flag'           => [
            'type'     => 'integer',
            'name'     => 'main_customer_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => 0,
        ],
        'name'                         => [
            'type'     => 'varchar',
            'name'     => 'name',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'post_grade'                   => [
            'type'     => 'integer',
            'name'     => 'post_grade',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'post'                         => [
            'type'     => 'varchar',
            'name'     => 'post',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'tel'                          => [
            'type'     => 'varchar',
            'name'     => 'tel',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'tel_area_code'                => [
            'type'     => 'varchar',
            'name'     => 'tel_area_code',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'tel_full'                     => [
            'type'     => 'varchar',
            'name'     => 'tel_full',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'birth'                        => [
            'type'     => 'varchar',
            'name'     => 'birth',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'gender'                       => [
            'type'     => 'integer',
            'name'     => 'gender',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'remark'                       => [
            'type'     => 'text',
            'name'     => 'remark',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'contact'                      => [
            'type'     => 'jsonb',
            'name'     => 'contact',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'external_field_data'          => [
            'type'     => 'jsonb',
            'name'     => 'external_field_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'group_id'                     => [
            'type'     => 'bigint',
            'name'     => 'group_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'tag'                          => [
            'type'     => 'jsonb',
            'name'     => 'tag',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'user_data'                    => [
            'type'     => 'jsonb',
            'name'     => 'user_data',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'create_time'                  => [
            'type'     => 'timestamp',
            'name'     => 'create_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'order_time'                   => [
            'type'     => 'timestamp',
            'name'     => 'order_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'archive_time'                 => [
            'type'     => 'timestamp',
            'name'     => 'archive_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'update_time'                  => [
            'type'     => 'timestamp',
            'name'     => 'update_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'edm_time'                     => [
            'type'     => 'timestamp',
            'name'     => 'edm_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => '1970-01-01 00:00:00',
        ],
        'send_mail_time'               => [
            'type'     => 'timestamp',
            'name'     => 'send_mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => '1970-01-01 00:00:00',
        ],
        'receive_mail_time'            => [
            'type'     => 'timestamp',
            'name'     => 'receive_mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => '1970-01-01 00:00:00',
        ],
        'mail_time'                    => [
            'type'     => 'timestamp',
            'name'     => 'mail_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => '1970-01-01 00:00:00',
        ],
        'image_list'                   => [
            'type'     => 'array',
            'name'     => 'image_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'full_tel_list'                => [
            'type'     => 'string_array',
            'name'     => 'full_tel_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'tel_list'                     => [
            'type'     => 'jsonb',
            'name'     => 'tel_list',
            'nullable' => 0,
            'php_type' => 'array',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'email_id'                     => [
            'type'     => 'bigint',
            'name'     => 'email_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'order_rank'                   => [
            'type'     => 'integer',
            'name'     => 'order_rank',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'growth_level'                 => [
            'type'     => 'integer',
            'name'     => 'growth_level',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'suspected_invalid_email_flag' => [
            'type'     => 'smallint',
            'name'     => 'suspected_invalid_email_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'forbidden_flag'               => [
            'type'     => 'smallint',
            'name'     => 'forbidden_flag',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'reach_status'                 => [
            'type'     => 'smallint',
            'name'     => 'reach_status',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'reach_status_time'            => [
            'type'     => 'timestamp',
            'name'     => 'reach_status_time',
            'nullable' => 0,
            'php_type' => 'string',
            'filter'   => ['enable' => true, 'batch' => true,],
            'default'  => '1970-01-01 00:00:00',
        ],
        'reach_count'                  => [
            'type'     => 'bigint',
            'name'     => 'reach_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'reach_success_count'          => [
            'type'     => 'bigint',
            'name'     => 'reach_success_count',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
        'reach_relate_id'              => [
            'type'     => 'bigint',
            'name'     => 'reach_relate_id',
            'nullable' => 0,
            'php_type' => 'int',
            'filter'   => ['enable' => true, 'batch' => true,],
        ],
    ];
    
    public static function table() {
        
        return 'tbl_customer';
    }
    
    /**
     *
     * @return string
     */
    public static function objectName() {
        
        return Constant::OBJ_CUSTOMER;
    }
    
    public static function dataSource() {
        
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }
    
    
    public static function singeObject() {
        
        return Customer::class;
    }
    
    public static function batchObject() {
        
        return BatchCustomer::class;
    }
    
    public static function operator() {
        
        return CustomerOperator::class;
    }
    
    public static function formatter() {
        
        return CustomerFormatter::class;
    }
    
    public static function objectIdKey() {
        
        return 'customer_id';
    }
    
    public static function filter() {
        
        return CustomerFilter::class;
    }
    
    
    public static function fieldTransfer() {
        
        return FieldTransferUtil::class;
    }


//    public static function autoLoadExtendData(): bool {
//
//        return true;
//    }

//    todo
    public static function defaultJoinSetting(): array {
        
        return [
            'tbl_customer|tbl_company' => [
                ['customer_id', 'id'],
                ['client_id', 'client_id'],
                ['object_name', static::objectName(), 'tbl_company'],//只有三个参数表示赋值模式,第三个参数表示来自的仓储
            ],
        
        ];
    }
    
    public static function getModuleType() {
        
        return \Constants::TYPE_CUSTOMER;
    }
    
    // 删除标志位字段
    public static function validFlagKey() {
        
        return 'is_archive';
    }
    
    // 当删除标志位的值等于 validFlagValue() 的返回值时，表示数据有效
    public static function validFlagValue() {
        
        return \Constants::ENABLE_FLAG_TRUE;
    }
    
    public static function pluginConfig() {
        
        return [
            'singleObjectAttributeSetValidate',
        ];
    }
}