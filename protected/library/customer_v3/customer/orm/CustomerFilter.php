<?php

namespace common\library\customer_v3\customer\orm;

use common\library\customer_v3\common\orm\BaseFilter;

/**
 * Class CustomerFilter
 *
 * @package  common\library\customer_v3\customer
 *
 * @property string  $customer_id
 * @property string  $client_id
 * @property string  $email
 * @property array   $user_id
 * @property integer $is_archive
 * @property string  $company_id
 * @property integer $main_customer_flag
 * @property string  $name
 * @property integer $post_grade
 * @property string  $post
 * @property string  $tel
 * @property string  $tel_area_code
 * @property string  $tel_full
 * @property string  $birth
 * @property integer $gender
 * @property string  $remark
 * @property string  $contact
 * @property string  $external_field_data
 * @property string  $group_id
 * @property string  $tag
 * @property string  $user_data
 * @property string  $create_time
 * @property string  $order_time
 * @property string  $archive_time
 * @property string  $update_time
 * @property string  $edm_time
 * @property string  $send_mail_time
 * @property string  $receive_mail_time
 * @property string  $mail_time
 * @property array   $image_list //直接赋值表示 字段数据更新
 * @property array   $full_tel_list
 * @property array   $tel_list
 * @property integer $email_id
 * @property integer $order_rank
 * @property integer $growth_level
 * @property integer $suspected_invalid_email_flag
 * @property integer $forbidden_flag
 * @property integer $reach_count
 * @property integer $reach_status
 * @property string  $reach_status_time
 * @property integer $reach_success_count
 * @property integer $reach_relate_id
 * @method CustomerMetadata getMetadata()
 * @method Customer find()
 */
class CustomerFilter extends BaseFilter {
    
    
    use InitCustomerMetadata;
}