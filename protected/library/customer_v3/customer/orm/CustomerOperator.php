<?php

namespace common\library\customer_v3\customer\orm;

use xiaoman\orm\common\OperatorV2;

class CustomerOperator extends OperatorV2 {
    
    
    /**
     * @var \common\library\customer_v3\customer\orm\Customer $object
     */
    protected $object;
    /**
     * @var BatchCustomer| $object
     */
    protected $batchObject;
    
    protected $hasDiff;
    
    const TASK_LIST = [
        'history' => [
            'task_class' => '',
        ],
    ];

//    public function create() {
//
//    }
//
//    public function edit() {
//
//    }
//
//    public function delete() {
//
//    }
}