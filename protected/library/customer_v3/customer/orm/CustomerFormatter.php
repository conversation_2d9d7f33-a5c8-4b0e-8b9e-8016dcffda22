<?php

namespace common\library\customer_v3\customer\orm;

use common\library\alibaba\customer\AlibabaCustomerRelationList;
use common\library\custom_field\company_field\CustomerField;
use common\library\custom_field\CustomFieldService;
use common\library\custom_field\FieldList;
use common\library\customer\Helper;
use common\library\customer_v3\common\orm\BaseFormatter;
use common\library\object\object_define\Constant as objConstant;
use common\library\orm\pipeline\formatter\FieldV2FormatTask;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\setting\library\common\GenderMetadata;
use common\library\setting\library\common\PostGradeMetadata;

/**
 * Class CompanyFormatter
 *
 * @package common\library\customer_v3\customer
 *
 * @method displayFieldFormatter(bool|FieldV2FormatTask $flag)
 * @method displayListInfo(bool $flag)
 * @method displayInfoGroup(bool $flag)
 * @method displayCloudStat(bool $flag)
 * @method displayLastMail(bool $flag)
 * @method displayCompanyInfo(bool $flag)
 * @method displayCompanyName(bool $flag)
 * @method displayExternalFieldData(bool $flag)
 * @method displayContact(bool $flag)
 * @method displayGenderName(bool $flag)
 * @method displayPostGradeName(bool $flag)
 * @method displayExcludeBoundAliBuyer(bool $flag)
 * @method displayHideInfo(bool $flag)
 * @method displayFilterDisableFields(bool $flag)
 * @method displayTelList(bool $flag)
 * @method displayAlibabaChatInfo(bool $flag)
 * @method displayUnEditEmail(bool $flag)
 * @method displaySetNullByZero(bool $flag)
 * @method displayFlattenCompanyFields(bool $flag)
 *
 */
class CustomerFormatter extends BaseFormatter {
    
    
    const MAPPING_SETTING = [
        
        'field_formatter'            => [
            'task_class' => FieldV2FormatTask::class,
        ],
        'list_info'                  => [
            'mapping_function' => 'mapListInfo',
        ],
        'flatten_company_fields'       => [
            'mapping_function' => 'mapFlattenCompanyFields',
        ],
        'company_info'               => [
            'build_function'   => 'buildCompanyInfo',
            'mapping_function' => 'mapCompanyInfo',
        ],
        'company_name'               => [
            'build_function'   => 'buildCompanyName',
            'mapping_function' => 'mapCompanyName',
        ],
        'external_field_data'        => [
            'build_function'   => 'buildExternalFieldData',
            'mapping_function' => 'mapExternalFieldData',
        ],
        'contact'                    => [
            'mapping_function' => 'mapContact',
        ],
        'gender_name'                => [
            'mapping_function' => 'mapGenderName',
        ],
        'post_grade_name'            => [
            'mapping_function' => 'mapPostGradeName',
        ],
        'info_group'                 => [
            'mapping_function' => 'mapInfoGroup',
        ],
        'cloud_stat'                 => [
            'mapping_function' => 'mapCloudStat',
        ],
        'last_mail'                  => [
            'build_function'   => 'buildLastMail',
            'mapping_function' => 'mapLastMail',
        ],
        'exclude_bound_ali_buyer'    => [
            'build_function' => 'buildExcludeBoundAliBuyer',
            'mapping_function' => 'mapExcludeBoundAliBuyer',
        ],
        'tel_list'                   => [
            'mapping_function' => 'mapTelList',
        ],
        'alibaba_chat_info'          => [
            'build_function'   => 'buildAlibabaChatInfo',
            'mapping_function' => 'mapAlibabaChatInfo',
        ],
        // un_edit_email 只在单个客户的查询场景下显示
        'un_edit_email'              => [
            'build_function'   => 'buildUnEditEmail',
            'mapping_function' => 'mapUnEditEmail',
        ],
        'set_null_by_zero'           => [
            'mapping_function' => 'mapSetNullByZero',
        ],
        'filter_disable_fields'      => [
            'build_function'   => 'buildFilterDisableFields',
            'mapping_function' => 'mapFilterDisableFields',
        ],
        'hide_info'                  => [
            'mapping_function' => 'mapHideInfo',
        ],
    ];
    
    const FIELD_FORMAT_MAP = [
        'company_name'    => [
            'field' => [],
            'func'  => 'displayCompanyName',
        ],
        'contact'         => [
            'field' => ['contact'],
            'func'  => 'displayCompanyName',
        ],
        'external_field_data'     => [
            'field' => ['external_field_data'],
            'func'  => 'displayExternalFieldData',
        ],
        'gender_name'     => [
            'field' => ['gender'],
            'func'  => 'displayGenderName',
        ],
        'post_grade_name' => [
            'field' => ['post_grade'],
            'func'  => 'displayPostGradeName',
        ],
//        'tel_list'        => [
//            'field' => ['tel', 'tel_area_code', 'tel_list'],
//            'func'  => 'displayTelList',
//        ],
//        'full_tel_list'   => [
//            'field' => ['full_tel', 'full_tel_list'],
//            'func'  => 'displayFullTelList',
//        ],
    ];
    
    
    protected $clientId;
    protected $showLastMail = false;
    protected $trailUserId;
    //勿直接调用 用getCompanyFieldFormatter()按需加载
    protected $fieldFormatter;
    protected $showCompanyInfo;
    protected $flattenCompanyFields = [];
    protected $excludeBoundAliBuyer;
    
    protected $userId;
    
    protected $filterDisableFields = false;
    
    
    public function __construct($metadata, $clientId) {
        
        parent::__construct($metadata, $clientId);
        
        $this->clientId = $clientId;
        
        $this->displayListInfo(true);
    }
    
    public function listSetting() {
        
        $this->displayFields($this->metadata->getMappedAttributeKeys());
        $this->setShowComponent(true);
        $this->functionFieldInfoSetting();
        $this->displayAlibabaChatInfo(true);
        $this->displaySetNullByZero(true);
        $this->setIsLayoutFormatter(true);
        $this->displayListInfo(false);
    }

    public function subObjectLayoutSetting() {
        $this->listSetting();
    }
    
    public function functionFieldInfoSetting() {
        
        $this->fieldSetting();
        $fieldFormatterTask = new  FieldV2FormatTask($this->clientId);
        $fieldFormatterTask->setFieldInfo($this->fieldSetting);
        $fieldFormatterTask->setObjName(objConstant::OBJ_CUSTOMER);
        $fieldFormatterTask->setDisplayFields($this->displayFields);
        $this->displayFieldFormatter($fieldFormatterTask);
    }
    
    public function getFieldReferType() {
        
        return \Constants::TYPE_CUSTOMER;
    }
    
    public function setPipelinePrepareData($key, array $data) {
        
        $this->_taskData[$key] = $data;
        
        return $this;
    }
    
    public function setUserId($userId) {
        
        $this->userId = $userId;
        $this->setFieldUserId($userId);
    }
    
    
    protected function mapListInfo(&$result, $key, $data) {
        
        $result = array_merge($result, $this->produceBaseInfo($key, $data));
    }
    
    public function setShowCloudStat($flag) {
        
        $this->displayCloudStat($flag);
    }
    
    public function mapCloudStat(&$result, $key, $data) {
        
        $result[$key] = $this->getPipelinePrepareData($key)[$data['email'] ?? ''] ?? ['active' => 0, 'popular' => 0];
    }
    
    public function setShowInfoGroup($flag) {
        
        $this->displayInfoGroup($flag);
    }
    
    public function mapInfoGroup(&$result, $key, $data) {
        
        $this->getFieldFormatter()->setShowInfoData(false);
        
        $result['fields'] = $this->getFieldFormatter()->format($data);
    }
    
    
    /**
     * @param mixed $showCompanyInfo
     */
    public function setShowCompanyInfo($showCompanyInfo) {
        
        $this->displayCompanyInfo($showCompanyInfo);
    }
    
    public function buildCompanyInfo($key, $data) {
        
        if (!\User::isLogin()) {
            
            return [];
        }
        
        $loginUser = \User::getLoginUser();
        
        $user_id = $loginUser->getUserId();
        
        $companyIds = array_filter(array_column($data, 'company_id'));
        
        if (empty($companyIds)) {
            
            return [];
        }
        
        $companyListObj = new \common\library\customer_v3\company\list\CompanyList($user_id);
        $companyListObj->getFormatter()->setFilterDisableFields($this->filterDisableFields);
        $companyListObj->setCompanyIds($companyIds);
        $companyListObj->setSkipPrivilege(true);
        $companyListObj->getFormatter()->displayFields([
            'name',
            'short_name',
            'company_id',
            'archive_type',
            'owner',
            'serial_id',
            'order_time',
            'tel_full',
            'homepage',
            'fax',
            'address',
            'scope_user_ids',
        ]);
        
        $result = $companyListObj->find();
        
        if ($this->filterDisableFields) {

//            todo：之前兼容过了，应该可以去掉
            $this->setPipelinePrepareData('filter_disable_fields', $companyListObj->getFormatter()->getPrivilegeFieldStats());
        }
        
        return array_column($result, null, 'company_id');
    }
    
    public function mapCompanyInfo(&$result, $key, $data) {
        
        $result[$key] = $this->getPipelinePrepareData($key)[$data['company_id']] ?? [];
    }
    
    
    /**
     * @param $key
     * @param $data
     * @return array
     * @throws \ProcessException
     */
    public function buildCompanyName($key, $data) {
        
        if (!\User::isLogin()) {
            
            return [];
        }
        
        $user_id = \User::getLoginUser()->getUserId();
        
        $companyIds = array_filter(array_column($data, 'company_id'));
        
        if (empty($companyIds)) {
            
            return [];
        }
        
        $companyListObj = new \common\library\customer_v3\company\list\CompanyList($user_id);
        $companyListObj->getFormatter()->setFilterDisableFields($this->filterDisableFields);
        $companyListObj->setCompanyIds($companyIds);
        $companyListObj->setSkipPrivilege(true);
        $companyListObj->setFields(['company_id', 'name as company_name']);
        $result = $companyListObj->find();
        
        
        if ($this->filterDisableFields) {
            
            $this->setPipelinePrepareData('filter_disable_fields', $companyListObj->getFormatter()->getPrivilegeFieldStats());
        }
        
        return array_column($result, 'company_name', 'company_id');
    }
    
    
    public function mapCompanyName(&$result, $key, $data) {
        
        $result[$key] = $this->getPipelinePrepareData($key)[$data['company_id']] ?? '';
    }
    
    public function mapContact(&$result, $key, $data) {
        
        $result[$key] = array_values(array_filter($data[$key], function ($item) {
            
            return (!empty($item['type']) && !empty($item['value']) && !is_array($item['type']) && !is_array($item['value']));
        }));
    }
    
    public function mapGenderName(&$result, $key, $data) {
        
        $result[$key] = GenderMetadata::getExtraDataMap(\Constants::TYPE_COMPANY)[$data['gender']] ?? GenderMetadata::getExtraDataMap(\Constants::TYPE_COMPANY)[\common\library\customer_v3\customer\orm\Customer::GENDER_TYPE_UNKNOWN];
    }
    
    
    public function mapPostGradeName(&$result, $key, $data) {
        
        $result[$key] = PostGradeMetadata::getExtraDataMap(\Constants::TYPE_COMPANY)[$data['post_grade']] ?? '';
    }

    public function buildExternalFieldData($key, $data)
    {
        return [
            'fileInfo' => $this->getIsLayoutFormatter() ? [] : $this->buildCustomFieldFiles($key, $data), // 历史接口需要格式化自定义图片、附件
        ];
    }

    public function buildCustomFieldFiles($key, $data)
    {
        // 自定义图片、附件格式化
        $filterFieldIds = [];
        $fileFieldTypes = [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH];
        $customFields = Helper::getExternalFieldSetting($this->clientId, \Constants::TYPE_CUSTOMER);
        foreach ($customFields as $field) {
            $customFieldType = $field['field_type'];
            $upstreamFieldType = $field['relation_origin_field_type'] ?? 0;
            if (in_array($customFieldType, $fileFieldTypes) || in_array($upstreamFieldType, $fileFieldTypes)) {
                $filterFieldIds[] = $field['id'];
            }
        }

        // 部分查询设置了查询字段setFields，未包括external_field_data字段，然后直接formatter->result()获取结果
        $externalFieldDatas = array_map(function ($elem) {
            return isset($elem['external_field_data']) && is_array($elem['external_field_data']) ? $elem['external_field_data'] : json_decode($elem['external_field_data'] ?? '{}', true);
        }, $data);

        $filterFieldIds = array_unique($filterFieldIds);
        $externalFileIds = [];
        foreach ($externalFieldDatas as $id => $data) {
            foreach ($filterFieldIds as $id) {
                if (!empty($data[$id])) {
                    $externalFileIds = array_unique(array_merge(array_column($data[$id], 'file_id'),$externalFileIds));
                }
            }
        }

        $fileInfoMap = [];
        $objArray = \UploadFile::findByIds($externalFileIds);
        foreach ($objArray as $obj) {
            $upload = new \AliyunUpload();
            $upload->loadByObject($obj);
            $preview_url = $upload->getPreview();
            $fileInfoMap[$obj->file_id] = [
                'file_id' => $upload->getFileId(),
                'file_url' => $upload->getFileUrl(),
                'download_url' => $upload->generatePresignedUrl(),
                'preview_url' => $preview_url,
                'file_preview_url' => $preview_url,//前端需要这个字段，老组件无法修改，影响范围很大
                'file_name' => $upload->getFileName(),
                'file_ext' => $upload->getFileExt(),
                'file_size' => $upload->getFileSize(),
            ];
        }
        return $fileInfoMap;
    }
    
    public function mapExternalFieldData(&$result, $key, $data) {
        
        $efd = $data['external_field_data'] ?? [];
        
        $externalFieldData = [];
        
        foreach (Helper::getExternalFieldSetting($this->clientId, \Constants::TYPE_CUSTOMER, $this->getFieldUserId()) as &$eField) {
            
            if (array_key_exists($eField['id'], $efd)) {
                
                $eField['value'] = $efd[$eField['id']];
            }

            if (!$this->getIsLayoutFormatter() && in_array($eField['field_type'], [CustomFieldService::FIELD_TYPE_IMAGE, CustomFieldService::FIELD_TYPE_ATTACH])) {
                $fileInfo = [];
                if (!empty($eField['value']) && is_array($eField['value'])) {
                    $fileIds = array_column($eField['value'], 'file_id');
                    foreach ($fileIds as $fileId) {
                        $file = $this->getPipelinePrepareData($key)['fileInfo'][$fileId] ?? [];
                        if (!$file) {
                            continue;
                        }
                        $fileInfo[$fileId] = $file;
                    }
                }
                $eField['value'] = array_values($fileInfo);
            }
            
            $externalFieldData[] = $eField;
        }
        
        $result[$key] = $externalFieldData;
    }
    
    /**
     * 排除绑定了阿里买家ID的联系人
     *
     * @return void
     */
    public function excludeBoundAliBuyer($flag) {
        
        $this->excludeBoundAliBuyer = $flag;
        
        $this->displayExcludeBoundAliBuyer($flag);
    }
    
    
    public function buildExcludeBoundAliBuyer($key, $data) {
        
        $customerIds = array_column($data, 'customer_id');
        
        $list = new AlibabaCustomerRelationList($this->clientId);
        $list->setCustomerId($customerIds);
        $list->existsBuyerAccountId(true);
        $list->setFields(['customer_id']);
        
        return array_column($list->find(), 'customer_id', 'customer_id');
    }
    
    public function mapExcludeBoundAliBuyer(&$result, $key, $data)
    {
        // 仅占位，防止报错
    }
    
    
    /**
     * @param bool $hideInfo
     */
    public function setHideInfo(bool $hideInfo) {
        
        $this->displayHideInfo($hideInfo);
    }
    
    public function mapHideInfo(&$result, $key, $data) {
        
        $hideValue = '***';
        
        $hideData = [];
        foreach ($data as $field => $item) {
            
            if (in_array($field, ['company_id', 'customer_id', 'main_customer_flag', 'growth_level', 'suspected_invalid_email_flag', 'forbidden_flag'])) {
                continue;
            }
            
            switch ($field) {
                case 'external_field_data':
                    foreach ($item as $k => $externalItem) {
                        if (!is_array($externalItem)) {
                            continue;
                        }
                        $externalItem['value'] = $hideValue;
                        $hideData[$field][$k] = $externalItem;
                    }
                    break;
                case 'tel_list':
                    foreach ($item as $k => $telItem) {
                        $hideData[$field][$k] = [$hideValue, $hideValue];
                    }
                    break;
                case 'contact':
                    $item = $item ?: [];
                    foreach ($item as $k => $contactItem) {
                        $contactItem['value'] = $hideValue;
                        $hideData[$field][$k] = $contactItem;
                    }
                    break;
                default:
                    if (is_array($item)) {
                    
                    } else {
                        $hideData[$field] = $hideValue;
                    }
            }
        }
        
        $result = array_replace_recursive($result, $hideData);
    }
    
    /**
     * @param bool $filterDisableFields
     */
    public function setFilterDisableFields(bool $filterDisableFields): void {
        
        $this->displayFilterDisableFields($filterDisableFields);
    }
    
    public function buildFilterDisableFields($key, $data) {
        
        return $this->getPipelinePrepareData($key) ?: array_filter(array_column($this->getPrivilegeFieldStats(), 'disable', 'refer_type'))[$this->getFieldReferType()] ?? [];
    }
    
    public function buildAlibabaChatInfo($key, $data) {
        
        $companyIds = array_filter(array_unique(array_column($data, 'company_id')));
        $alibabaMap = \common\library\alibaba\trade\Helper::formatTradeByCompanyIds($this->clientId, \User::getLoginUser()->getUserId(), $companyIds);
        return $alibabaMap ?? [];
    }
    
    public function mapAlibabaChatInfo(&$result, $key, $data) {
        
        $result['alibaba_info'] = $this->getPipelinePrepareData($key)[$result['company_id']] ?? [];
    }
    
    public function buildUnEditEmail($key, $data) {
        
        $companyIds = array_values(array_filter(array_unique(array_column($data, 'company_id')))) ?? [];
        $customerIds = array_filter(array_unique(array_column($data, 'customer_id')));
        if (count($companyIds) != 1) {
            // un_edit_email 限制只能在单个客户的查询场景下显示
            return [];
        }
        return Helper::getUneditableEmailByCompany($this->clientId, current($companyIds), $customerIds);
    }
    
    public function mapUnEditEmail(&$result, $key, $data) {
        
        $unEditEmailMap = $this->getPipelinePrepareData($key);
        if (!empty($unEditEmailMap) && in_array($result['customer_id'], $unEditEmailMap)) {
            $result['un_edit_email_flag'] = true;
        } else {
            $result['un_edit_email_flag'] = false;
        }
    }
    
    public function mapSetNullByZero(&$result, $key, $data) {
        
        $setNullByZeroField = [
            'post_grade',
        ];
        foreach ($setNullByZeroField as $field) {
            if (isset($result[$field]) && ($result[$field] === 0 || $result[$field] === '0')) {
                $result[$field] = null;
            }
        }
    }
    
    public function mapFilterDisableFields(&$result, $key, $data) {
        
        $filterCustomerFields = array_intersect($this->getPipelinePrepareData($key), FieldList::SPECIFIC_DISABLE_FIELD_MAP[\Constants::TYPE_CUSTOMER] ?? []);
        
        $result = array_diff_key($result, array_flip($filterCustomerFields));
    }
    
    /**
     *
     * @param $map
     * @return void
     */
    public function setCompanyNameMap($map) {
        
        $this->setPipelinePrepareData('company_name', $map);
    }
    
    public function setCompanyInfoMap($map)
    {
        $this->setPipelinePrepareData('company_info', $map);
    }
    
    public function setUserLastMail($userId) {
        
        $this->trailUserId = $userId;
        $this->showLastMail = $userId ? 1 : 0;
    }
    
    public function flattenCompanyFields(array $companyField)
    {
        $this->flattenCompanyFields = $companyField;
        $this->displayFlattenCompanyFields(true);
    }
    
    public function mapFlattenCompanyFields(&$result, $key, $data)
    {
        $companyMap = $this->getPipelinePrepareData('company_info');
        if (empty($this->flattenCompanyFields) || empty($companyMap)) {
            return;
        }
        
        $company = $companyMap[$data['company_id']] ?? [];
        
        foreach ($this->flattenCompanyFields as $k => $field) {
            if (is_numeric($k)) {
                $result[$field] = $company[$field] ?? '';
            } else {
                $result[$field] = $company[$k] ?? '';
            }
        }
    }
    
    public function getFieldFormatter() {
        
        if (empty($this->fieldFormatter)) {
            $this->initFieldPrivilegeForFormatter([]);
            $this->fieldFormatter = new CustomerField($this->clientId);
            $this->fieldFormatter->setFieldUserId($this->getFieldUserId());
            $this->fieldFormatter->setFieldFunctionalId($this->getFieldFunctionalId());
        }
        return $this->fieldFormatter;
    }
    
    public function strip($data) {
        
        if (empty($data)) {
            
            return [];
        }
        
        $data['contact'] = array_values(array_filter($data['contact'], function ($item) {
            
            return (!empty($item['type']) && !empty($item['value']) && !is_array($item['type']) && !is_array($item['value']));
        }));
        
        return $data;
    }
    
    public function baseInfoSetting() {
        
        $this->displayFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'post',
            'main_customer_flag',
        ]);
    }
    
    public function mainCustomerInfoSetting() {
        
        $this->displayFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'post',
            'contact',
            'main_customer_flag',
            'growth_level',
            'suspected_invalid_email_flag',
            'forbidden_flag',
        ]);
    }
    
    
    public function detailInfoSetting() {
        
        $this->displayFields(['customer_id', 'company_id', 'name', 'email', 'main_customer_flag', 'growth_level', 'suspected_invalid_email_flag', 'forbidden_flag']);
        $this->setShowInfoGroup(true);
    }
    
    public function appBaseInfoSetting() {
        
        $this->displayFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'birth',
            'tel_list',
            'post',
            'post_grade',
            'post_grade_name',
            'remark',
            'gender',
            'gender_name',
            'external_field_data',
            'contact',
            'image_list',
            'main_customer_flag',
            'growth_level',
            'suspected_invalid_email_flag',
            'forbidden_flag',
        ]);
        $this->displayListInfo(true);
    }
    
    public function appDetailInfoSetting() {
        
        $this->displayFields(['customer_id', 'company_id', 'name', 'email', 'main_customer_flag', 'growth_level']);
        $this->setShowInfoGroup(true);
    }
    
    public function cardInfoSetting() {
        
        $this->displayFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'tel_list',
            'post',
            'contact',
            'main_customer_flag',
            'remark',
        ]);
    }
    
    public function defaultSetting() {
        
        $this->displayListInfo(true);
    }
    
    protected function produceBaseInfo($key, $data) {
        
        $result = [];
        
        $specifyFields = $this->displayFields ?? array_keys($data);
        
        foreach ($specifyFields as $field) {
            switch ($field) {
                case 'tel_list':
                    if (!empty($data['tel']) || !empty($data['tel_area_code'])) {
                        $data['tel_list'][] = [$data['tel_area_code'], $data['tel']];
                    }
                    $result['tel_list'] = $data['tel_list'];
                    break;
                case 'full_tel_list':
                    if (!empty($data['tel_full'])) {
                        $data['full_tel_list'][] = $data['tel_full'];
                    }
                    $result['full_tel_list'] = $data['full_tel_list'];
                    break;
                case 'contact':
                    $result['contact'] = is_array($data['contact']) ? $data[$field] : (json_decode($data['contact'], true) ?: []);
                    break;
                default:
                    $result[$field] = $data[$field];
            }
        }
        
        return $result;
    }
    
    
    public function ApiBaseInfoSetting() {
        
        $this->displayFields([
            'customer_id',
            'company_id',
            'name',
            'email',
            'gender',
            'remark',
            'birth',
            'tel_list',
            'post_grade',
            'post',
            'image_list',
            'contact',
            'main_customer_flag',
            'external_field_data',
        ]);
    }
    
    public function edmAddressInfoSetting() {
        
        $this->displayFields(['customer_id', 'name', 'email', 'company_id',]);
    }
    
    public function snsInfoSetting() {
        
        $this->displayFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact']);
    }
    
    public function snsListSetting() {
        
        $this->displayFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact', 'user_id']);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['short_name', 'scope_user_ids']);
    }
    
    public function wecomListSetting() {
        
        $this->displayFields(['customer_id', 'name', 'order_time', 'create_time', 'email', 'company_id', 'company_name', 'tel_list', 'full_tel_list', 'contact', 'main_customer_flag', 'user_id']);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['name' => 'company_name', 'tel_full', 'company_id', 'homepage', 'fax', 'address', 'owner', 'short_name', 'scope_user_ids']);
    }
    
    public function tmRecommendSetting() {
        
        $this->displayFields([
            'customer_id', 'company_id', 'name', 'email', 'tel_list', 'full_tel_list', 'contact', 'create_time', 'user_id', 'tag', 'image_list', 'user_data',
        ]);
        $this->setShowCompanyInfo(true);
        $this->flattenCompanyFields(['name' => 'company_name', 'tel_full', 'company_id', 'homepage', 'fax', 'address', 'owner', 'short_name']);
        $this->excludeBoundAliBuyer(true);
    }
    
    
    protected function constructResultList(array &$result, ?array $item) {
        
        if ($this->needRemove($item)) {
            
            return null;
        }
        
        parent::constructResultList($result, $item);
        
        if (isset($item['main_customer_flag']) && $item['main_customer_flag']) {
            
            array_unshift($result, array_pop($result));
        }
    }
    
    
    private function needRemove(?array $item) {
        
        if ($this->excludeBoundAliBuyer && ($this->getPipelinePrepareData('exclude_bound_ali_buyer')[$item['customer_id']] ?? false)) {
            
            return true;
        }
        
        return false;
    }
    
    
    public function mapTelList(&$result, $key, $data) {
        
        foreach ($result['tel_list'] ?? [] as $key => $item) {
            $telFull = [
                'tel_area_code' => isset($item[0]) && strlen($item[0]) > 0 ? '+' . $item[0] : '',
                'tel'           => $item[1] ?? '',
            ];
            $result['tel_list'][$key] = $telFull;
        }
    }
    
    function getFieldFormatMap(): array {
    
        return self::FIELD_FORMAT_MAP;
    }
    
    function getPrimaryKey() {
        
        return $this->getMetadata()->objectIdKey();
    }
    
    function buildUserInfo() {
        // TODO: Implement buildUserInfo() method.
    }
    
    public function getFunctionalIdByData($data) {
        
        return empty($data['user_id']) ? PrivilegeConstants::FUNCTIONAL_COMPANY_POOL : PrivilegeConstants::FUNCTIONAL_CUSTOMER;
    }
    
    protected function getDisplaySettingByField($privilegeDisableFieldIds): array {
        
        return [];
    }
}
