<?php

namespace common\library\customer_v3\customer\orm;

use xiaoman\orm\common\BatchObjectV2;

/**
 *
 * @property CustomerFormatter $_formatter
 * @method CustomerOperator getOperator()
 * @method CustomerFormatter getFormatter()
 */
class BatchCustomer extends BatchObjectV2 {
    
    
    use InitCustomerMetadata;
    
    protected Customer $customer;
    
    /**
     * @param Customer $customer
     */
    public function setCustomer(Customer $customer): void {
        
        $this->customer = $customer;
    }
    
    public function setFormatter(CustomerFormatter $formatter) {
        
        $this->_formatter = $formatter;
    }
    
    
}