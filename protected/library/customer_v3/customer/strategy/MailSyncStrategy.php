<?php

namespace common\library\customer_v3\customer\strategy;

use common\library\customer_v3\common\strategy\BaseStrategy;
use common\library\email_identity\sync\CustomerSync;

class MailSyncStrategy extends BaseStrategy {
    
    
    public function canAsync(): bool {
        
        return true;
    }
    
    public function process() {
        
        !empty($this->extraData['email']) && (new CustomerSync($this->clientId))->setFindEmail($this->extraData['email'])->sync();
        
        !empty($this->extraData['company_id']) && (new CustomerSync($this->clientId))->setFindCompanyId($this->extraData['company_id'])->sync();
    }
}