<?php

namespace common\library\customer_v3\customer\strategy;

use common\library\customer_v3\common\strategy\BaseStrategy;
use common\library\lead\LeadCustomer;
use common\library\report\error\ErrorReport;

class ArchiveStrategy extends BaseStrategy {
    
    
    public function canAsync(): bool {
        
        return false;
    }
    
    public function check(): bool {
        
        return !empty($this->extraData['sourceCustomerId']) && parent::check(); // TODO: Change the autogenerated stub
    }
    
    public function process() {
        
        !\User::isLogin() && \User::setLoginUserById($this->opUserId);
        
        $leadCustomer = new LeadCustomer($this->clientId, $this->extraData['sourceCustomerId']);
        
        if ($leadCustomer->isExist()) {
            
            $leadCustomer->archive($this->newData['company_id'], current($this->objectId));
        }
    }
}