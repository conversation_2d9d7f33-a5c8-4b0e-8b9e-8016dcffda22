<?php

/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2021/08/13 12:05:35
 */

namespace common\library\platform_product;

use common\components\BaseObject;
use common\library\platform_product\sku\PlatformSkuAPI;
use xiaoman\orm\common\Operator;

/**
 * @method PlatformProductFilter getFilter()
 */
class PlatformProductOperator extends Operator
{
    public const TASK_LIST = [
        'mark_skus_delete' => [
            'require_fields' => ['platform_product_id'],
            'method'         => 'markSkusDelete',
        ],
    ];

    /**
     * 标记为疑似删除
     * @return int
     */
    public function markThirdDelete()
    {
        try{
            $updateData = [
                'update_time'  => date('Y-m-d H:i:s'),
                'third_delete' => PlatformProductConstants::THIRD_DELETED,
            ];

            return $this->standardProcess($updateData, ['mark_skus_delete']);
        }catch(\Throwable $e){
            // 同步阿里产品时疑似删除日志打点
            \LogUtil::error("markThirdDelete error:".$e->getMessage()." | File:".$e->getFile()." | Line:".$e->getLine().' | platform_product_id:'.json_encode($this->object->getAttributes(['platform_product_id'])));
        }
    }

    /**
     * 将sku标记为疑似删除
     * @param array $data
     * @param array $setting
     * @return int
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    protected function markSkusDelete(array $data, array $setting): int
    {
        $skuApi     = new PlatformSkuAPI($this->clientId);
        $productIds = array_column($data, 'platform_product_id');
        $filter     = $skuApi->buildFilter([
            'platform_product_id' => $productIds,
            'third_delete'        => PlatformProductConstants::THIRD_NOT_DELETED,
        ]);

        $batch = $filter->find();
        $this->object->getDomainHandler() && $batch->setDomainHandler($this->object->getDomainHandler());
        return $batch->getOperator()->markThirdDeleted();
    }

    // 删除平台spu，该方法目前只会在删除平台sku时被动触发
    public function delete(){
        if (!$this->object->getListAttributes()) {
            return 0;
        }

        $user = $this->object->getDomainHandler();
        $userId = $user ? $user->getUserId() : 0;
        $now = date('Y-m-d H:i:s');

        $updateData = [
            'enable_flag' => BaseObject::ENABLE_FLAG_FALSE,
            'delete_time' => $now,
            'delete_user' => $userId,
            'update_time' => $now,
        ];
        return $this->execute($updateData);
    }
}
