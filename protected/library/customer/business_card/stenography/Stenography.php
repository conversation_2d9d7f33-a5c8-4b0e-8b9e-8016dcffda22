<?php

namespace common\library\customer\business_card\stenography;

use common\components\BaseObject;
use PgActiveRecord;

/**
 * Class Stenography
 * @package common\library\customer\business_card\stenography
 * @property string $business_card_id
 * @property string $stenography_id
 * @property integer $client_id
 * @property integer $user_id
 * @property string $data
 * @property integer $enable_flag
 * @property string $create_time
 * @property string $update_time
 * @method StenographyFormatter getFormatter()

 */
class Stenography extends BaseObject
{

    protected $id;

    /**
     * @return \Stenography;
     */
    public function getModelClass()
    {
        return \Stenography::class;
    }


    public function __construct($clientId, $id = 0)
    {
        $this->client_id = $clientId;
        $this->_formatter = new StenographyFormatter($clientId);
        if ($id) {
            $this->loadById($id);
        }
    }


    public function getId()
    {
        return $this->id;
    }


    public function loadById($id)
    {
        $this->_getFormatter = true;
        $model = \Stenography::model()->find('stenography_id=:stenography_id', [':stenography_id' => $id]);
        if ($model) {
            $this->setModel($model);
        } else {
            throw new \RuntimeException("stenography_id ($id) not exist");
        }
        return $this;
    }


    public function isExist()
    {
        return !$this->isNew();
    }


    public function beforeSave()
    {
        if ($this->isNew()) {
            $this->stenography_id = PgActiveRecord::produceAutoIncrementId();
            $this->create_time = date('Y-m-d H:i:s');
            $this->update_time = date('Y-m-d H:i:s');
        }else {
            $this->update_time = date('Y-m-d H:i:s');
        }
        return true;
    }


    public function afterSave()
    {
        parent::afterSave();
    }


}