<?php

namespace common\library\oms_v2\other_inbound_invoice;

use common\library\object\object_define\Constant;
use xiaoman\orm\metadata\MetadataV2;
use xiaoman\orm\metadata\Metadata;
use common\library\object\traits\BizObjectClass;
use common\library\object\field\util\FieldTransferUtil;
        

class OtherInboundInvoiceMetadata extends MetadataV2
{
    use BizObjectClass;
    protected $columns = [
    'inbound_invoice_id' => [
        'type' => 'int',
        'name' => 'inbound_invoice_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'client_id' => [
        'type' => 'int',
        'name' => 'client_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'type' => [
        'type' => 'int',
        'name' => 'type',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'serial_id' => [
        'type' => 'string',
        'name' => 'serial_id',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'warehouse_invoice_time' => [
        'type' => 'dateTime',
        'name' => 'warehouse_invoice_time',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'refer_id' => [
        'type' => 'int',
        'name' => 'refer_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'refer_type' => [
        'type' => 'int',
        'name' => 'refer_type',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'status' => [
        'type' => 'int',
        'name' => 'status',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'delete_flag' => [
        'type' => 'int',
        'name' => 'delete_flag',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'handler' => [
        'type' => 'array',
        'name' => 'handler',
        'nullable' => '0',
        'php_type' => 'array',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'create_time' => [
        'type' => 'dateTime',
        'name' => 'create_time',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'create_user' => [
        'type' => 'int',
        'name' => 'create_user',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'update_time' => [
        'type' => 'dateTime',
        'name' => 'update_time',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'update_user' => [
        'type' => 'int',
        'name' => 'update_user',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'remark' => [
        'type' => 'string',
        'name' => 'remark',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'attachment' => [
        'type' => 'jsonb',
        'name' => 'attachment',
        'nullable' => '0',
        'php_type' => 'array',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'external_field_data' => [
        'type' => 'jsonb',
        'name' => 'external_field_data',
        'nullable' => '0',
        'php_type' => 'array',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'cause_id' => [
        'type' => 'int',
        'name' => 'cause_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'discount_rate' => [
        'type' => 'float',
        'name' => 'discount_rate',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'discount' => [
        'type' => 'float',
        'name' => 'discount',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_total_amount' => [
        'type' => 'float',
        'name' => 'product_total_amount',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_total_count' => [
        'type' => 'float',
        'name' => 'product_total_count',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'invoice_warehouse_id' => [
        'type' => 'int',
        'name' => 'invoice_warehouse_id',
        'nullable' => '0',
        'php_type' => 'int',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'exchange_rate' => [
        'type' => 'float',
        'name' => 'exchange_rate',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'exchange_rate_usd' => [
        'type' => 'float',
        'name' => 'exchange_rate_usd',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'currency' => [
        'type' => 'string',
        'name' => 'currency',
        'nullable' => '0',
        'php_type' => 'string',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_total_amount_rmb' => [
        'type' => 'float',
        'name' => 'product_total_amount_rmb',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_total_amount_usd' => [
        'type' => 'float',
        'name' => 'product_total_amount_usd',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'parts_total_count' => [
        'type' => 'float',
        'name' => 'parts_total_count',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ],
    'product_total_count_no_parts' => [
        'type' => 'float',
        'name' => 'product_total_count_no_parts',
        'nullable' => '0',
        'php_type' => 'float',
        'filter' => [
            'enable' => true,
            'batch' => true
        ]
    ]
];

    public static function table()
    {
        return 'tbl_inbound_invoice';
    }

    public static function dataSource()
    {
        return Metadata::DATA_SOURCE_POSTGRESQL_CLIENT;
    }

    public static function singeObject()
    {
        return OtherInboundInvoice::class;
    }

    public static function batchObject()
    {
        return BatchOtherInboundInvoice::class;
    }

    public static function objectIdKey()
    {
        return 'inbound_invoice_id';
    }

    public static function filter()
    {
        return OtherInboundInvoiceFilter::class;
    }

    public static function formatter()
    {
        return OtherInboundInvoiceFormatter::class;
    }

    public static function operator()
    {
        return OtherInboundInvoiceOperator::class;
    }
    
    public static function getModuleType()
    {
        return \Constants::TYPE_OTHER_INBOUND_INVOICE;
    }
    
    public static function fieldTransfer()
    {
        return FieldTransferUtil::class;
    }
    
    public static function objectName()
    {
        return Constant::OBJ_OTHER_INBOUND_INVOICE;
    }
    
    public static function autoLoadExtendData(): bool
    {
        return  true;
    }

    public static function defaultJoinSetting():array
    {
        return [
            'tbl_inbound_invoice|tbl_extend' => [
                ['inbound_invoice_id','id'],
                ['client_id','client_id'],
                ['object_name',static::objectName(),'tbl_extend']//只有三个参数表示赋值模式,第三个参数表示来自的仓储
            ]

        ];
    }
}