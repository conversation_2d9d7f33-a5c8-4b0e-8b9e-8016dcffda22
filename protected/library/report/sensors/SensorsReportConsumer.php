<?php

/**
 * 神策平台数据上报
 * Created by PhpStorm.
 * User: bing
 * Date: 2020/12/21
 * Time: 14:49
 */

namespace common\library\report\sensors;

use common\library\report\error\ErrorReport;
use Swoole\Process\Pool;
use common\library\server\Consumer;


class SensorsReportConsumer extends Consumer
{

    const SENSORS_EVENT_KEY = 'crm:sensors_event_key';

    const SENSORS_EVENT_RETRY_KEY = 'crm:sensors_event_retry_key';

    const SERVER_URL = 'https://datasink-sensorsdata.xiaoman.cn/sa';

    const MAX_RETRY_NEXT_TIME = 5;//延时时间，单位s

    const MAX_RETRY_COUNT = 100;//最大尝试次数

    /**@var \Predis\Client*/
    protected $redis;

    protected $mainWorkerNum;

    protected $retryWorkerNum;

    protected $sensorsEventKey;

    protected $retrySensorsEventKey;

    /**
     * 默认公共属性值
     * @var array
     */
    protected $defaultSuperProperties = [
        'origin_platform' => 'php' //神策定义
    ];


    public function __construct(int $mainWorkerNum = 3, int $retryWorkerNum = 2)
    {
        $this->mainWorkerNum = $mainWorkerNum;
        $this->retryWorkerNum = $retryWorkerNum;
        $workerNum = $mainWorkerNum + $retryWorkerNum;
        parent::__construct($workerNum);
    }

    protected function init()
    {
        $this->sensorsEventKey = self::SENSORS_EVENT_KEY;
        $this->retrySensorsEventKey = self::SENSORS_EVENT_RETRY_KEY;
    }

    public function onWorkerStart(Pool $pool, int $workerId)
    {
        ini_set('default_socket_timeout', -1);
        $this->redis = \RedisService::queuePersistent();
        PHP_OS == 'Linux' && swoole_set_process_name($this->workerName($workerId));
        \LogUtil::info(static::serverName() . ' worker ' . $workerId . ' start');

        if($workerId >= $this->mainWorkerNum) {
            $this->retryWorker($pool, $workerId);
        }else {
            $this->worker($pool, $workerId);
        }
    }

    protected function worker(Pool $pool, int $workerId)
    {
        while (true)
        {
            $ret = $this->redis->brpop([$this->sensorsEventKey], 0);
            if($ret)
            {
                try
                {
                    $eventData = json_decode($ret[1], true);
                    if(is_array($eventData)) {
                        $clientId = $eventData['client_id'];
                        $userId = $eventData['user_id'] ?? 0;
                        $event = $eventData['event'] ?? '';
                        $params = $eventData['params'] ?? [];
                        if(empty($event)) {
                            \LogUtil::info("clientId={$clientId}, Error:Not Exist Event={$event}");
                            continue;
                        }
                        if($userId && $event)
                        {
                            $result = $this->track($clientId, $userId, $event, $params);
                            // 失败处理
                            if(isset($result['ret_code']) && $result['ret_code'] != 200)
                            {
                                $this->failHandle($eventData);
                                $e = new \ProcessException('神策上报接口异常');
                                ErrorReport::phpError(new \CExceptionEvent(null,$e), $e->getTrace());
                                \LogUtil::error("sensors api error code: {$result['ret_code']} {$result['ret_content']}");
                            }
                        }else {
                            \LogUtil::info("clientId={$clientId}, Error:userId={$userId},event={$event} ");
                        }
                    }
                }catch (\Throwable $e)
                {
                    $code = $e->getCode();
                    $errorMsg = $e->getMessage();
                    \LogUtil::error("Error:code={$code},clientId={$clientId},userId={$userId},event={$event},workerId={$workerId},errorMsg={$errorMsg}");
                }
            }
        }
    }

    protected function retryWorker($pool, $workerId)
    {
        while (true) {
            try {
                $ret = $this->redis->zrangebyscore($this->retrySensorsEventKey, 0, time(), [ 'limit' => [0,1]]);
                if(!$ret)
                {
                    sleep(self::MAX_RETRY_NEXT_TIME);
                    continue;
                }
                $member = $ret[0];
                if(!$member)
                {
                    sleep(self::MAX_RETRY_NEXT_TIME);
                    continue;
                }

                $this->redis->eval($this->getLuaScript(), 2, $this->sensorsEventKey, $this->retrySensorsEventKey, $member);

            }catch (\Exception $e) {
                $code = $e->getCode();
                $errorMsg = $e->getMessage();
                \LogUtil::error("retryWorker error, code={$code}, errorMsg={$errorMsg}");
            }

        }

    }

    protected function getLuaScript()
    {
        $lua = <<<LUA
local sensorsEventKey = KEYS[1];
local retrySensorsEventKey = KEYS[2];
local member = ARGV[1];
local ret1 = redis.call("ZREM", retrySensorsEventKey, member);
local ret2 = redis.call("LPUSH", sensorsEventKey, member);
if ret2 then
    return 1;
else 
    return 0;
end
LUA;
        return $lua;
    }

    protected function failHandle(array $eventData)
    {
        if(!isset($eventData['retry_count'])) {
            $eventData['retry_count'] = 0;
        }
        ++$eventData['retry_count'];

        if($eventData['retry_count'] > static::MAX_RETRY_COUNT)
        {
            \LogUtil::error("retry count > ".static::MAX_RETRY_COUNT."[event data]=".json_encode($eventData));
            return;
        }

        $eventData['next_until_time'] = time() + self::MAX_RETRY_NEXT_TIME;

        $this->redis->zadd($this->retrySensorsEventKey, [json_encode($eventData)=> $eventData['next_until_time']]);

    }

    /**
     * @param $clientId
     * @param $userId
     * @param $event
     * @param array $params
     * @param array $superProperties
     */
    protected function track($clientId, $userId, $event, array $params, array $superProperties = [])
    {
        if(empty($params))
        {
            return;
        }

        $consumer = new \BatchConsumer($this->getParseServerUrl(), $max_size = 50, $request_timeout = 1000, $response_info = true);
        $saObject = new \SensorsAnalytics($consumer);

        $saObject->track($userId, true, $event, $params);

        $superProperties = array_merge($this->defaultSuperProperties, $superProperties);
        $saObject->register_super_properties($superProperties);

        $properties = [
            'client_id' => $clientId
        ];
        //设置用户属性
        $saObject->profile_set_once($userId, true, $properties);

        return $saObject->flush();
    }

    /**
     * @return string
     */
    protected function getParseServerUrl()
    {
        $env = \Yii::app()->params['env'];
        $serverUrl = self::SERVER_URL.'?project=production';
        if(in_array($env, ['test'])) {
            $serverUrl = self::SERVER_URL.'?project=default';
        }
        return $serverUrl;
    }

}
