<?php
namespace common\library\report\dingtalk;


class DingTalkGitlab
{
    const REDIS_GITLAB_USER_LIST_KEY = 'gitlab:user:list';

    protected $content;

    protected $logPrefix = 'DingTalkGitlab';

    const GITLAB_TOKEN = "********************";
    const ACTION_MAP = [
        'open'        => '提交了PR，需要你审查',
        'update'       => '补充了新代码到PR，需要你审查',
        'reopen'      => '再次提交了PR，需要你审查',
        'close'      => '拒绝了你提交的PR',
        'merge'        => '合并了你提交的PR',
        'approved'      => '同意了你提交的PR',
        'unapproved'    => '撤消了同意你提交的PR',
        'COMMENTED'    => '新增了对PR代码的评论'
    ];
    const COMMENT_ACTION_MAP = [
        'DiscussionNote' => '回复了对PR代码的评论',
        ''   => '新增了对PR代码的评论',
        'DiffNote'   => '新增了对PR代码的评论',
    ];

    const URL_GET_GITLAB_USER = 'https://gitlab.xiaoman.cc/api/v4/users?per_page=100000';//获取git成员列表
    const URL_GET_GITLAB_MR_INFO = 'https://gitlab.xiaoman.cc/api/v4/projects/:pid/merge_requests/:iid';//获取mr详情

    protected $action;
    protected $prUrl;
    protected $title;
    protected $fromRepo;
    protected $formBranch;
    protected $toRepo;
    protected $toBranch;
    protected $params;

    protected $reviewUserList;
    protected $authorUser = [];
    protected $requestUser = [];
    protected $sendUserList = [];

    public function initParams($params)
    {
        $this->params = $params;
        $this->checkParams();

        $this->action     = $params['PULL_REQUEST_ACTION'];
        $this->prUrl      = $params['PULL_REQUEST_URL'];
        $this->title      = $params['PULL_REQUEST_TITLE'];
        $this->fromRepo   = $params['PULL_REQUEST_FROM_REPO_SLUG'];
        $this->formBranch = $params['PULL_REQUEST_FROM_BRANCH'];
        $this->toRepo     = $params['PULL_REQUEST_TO_REPO_SLUG'];
        $this->toBranch   = $params['PULL_REQUEST_TO_BRANCH'];

        $this->logPrefix = "[{$this->title}][$this->action]:";
        $this->initReviewUserList();
        $this->initAuthorUser();
        $this->initRequestUser();
        $this->initSendUserList();
        $this->initMessageContent();

    }

    public function formatParams($httpBody = '') {
        if (!$httpBody){
            throw new \RuntimeException('gitlab回调参数异常！');
        }
        $params =  json_decode($httpBody, true);

        $gitUserList = $this->getGitlabUser();
        \LogUtil::info('gitUserList='.json_encode($gitUserList,JSON_UNESCAPED_UNICODE));

        switch ($params['object_kind']) {
            case 'note':  // 评论
                $PULL_REQUEST_ACTION = 'COMMENTED';
                $PULL_REQUEST_COMMENT_TEXT = $params['object_attributes']['note'];
                $PULL_REQUEST_COMMENT_ACTION = $params['object_attributes']['type'];  //type为null表示评论  为DiscussionNote表示回复
                $objectAttributes = $params['merge_request'];
                break;
            case 'merge_request': // 合并
                $PULL_REQUEST_ACTION = $params['object_attributes']['action'] ?? 'open';//open reopen update close'
                $objectAttributes = $params['object_attributes'];
                break;
            default:
                throw new \RuntimeException($params['object_kind'].'操作不做处理！');
        }

        $pid = $params['project']['id'];
        $iid = $objectAttributes['iid'];
        $mrInfo = $this->getMrDetail($pid,$iid);
        \LogUtil::info('mrinfo='.json_encode($mrInfo,JSON_UNESCAPED_UNICODE));
        if (!$mrInfo){
            throw new \RuntimeException('获取mr详情失败！');
        }

        $arrReviewerEmails = [];
        foreach ($mrInfo['reviewers'] as $reviewer){
            if (isset($gitUserList[$reviewer['id']])){
                $arrReviewerEmails[] = $gitUserList[$reviewer['id']];
            }
        }
        $PULL_REQUEST_REVIEWERS_EMAIL = implode(',',array_unique($arrReviewerEmails));
        \LogUtil::info('reviews='.$PULL_REQUEST_REVIEWERS_EMAIL);

        //获取MR请求发起人邮箱
        $authorId = $mrInfo['author']['id'];
        $authorEmail = $gitUserList[$authorId] ?? '';
        $PULL_REQUEST_AUTHOR_EMAIL = $authorEmail;

        $PULL_REQUEST_FROM_BRANCH = $objectAttributes['source_branch'];
        $PULL_REQUEST_FROM_REPO_SLUG = $objectAttributes['source']['path_with_namespace'];
        $PULL_REQUEST_TITLE = $objectAttributes['title'];
        $PULL_REQUEST_TO_BRANCH = $objectAttributes['target_branch'];
        $PULL_REQUEST_TO_REPO_SLUG = $objectAttributes['target']['path_with_namespace'];
        $PULL_REQUEST_URL = $mrInfo['web_url'];
        $PULL_REQUEST_USER_EMAIL_ADDRESS = $params['user']['email']; //操作人邮箱
        $PULL_REQUEST_USER_NAME = $params['user']['username']; //操作人名称

        return [
            'PULL_REQUEST_ACTION' => $PULL_REQUEST_ACTION,
            'PULL_REQUEST_AUTHOR_EMAIL' => $PULL_REQUEST_AUTHOR_EMAIL,
            'PULL_REQUEST_COMMENT_ACTION' => $PULL_REQUEST_COMMENT_ACTION ?? Null,
            'PULL_REQUEST_COMMENT_TEXT' => $PULL_REQUEST_COMMENT_TEXT ?? NULL,
            'PULL_REQUEST_FROM_BRANCH' => $PULL_REQUEST_FROM_BRANCH,
            'PULL_REQUEST_FROM_REPO_SLUG' => $PULL_REQUEST_FROM_REPO_SLUG,
            'PULL_REQUEST_REVIEWERS_EMAIL' => $PULL_REQUEST_REVIEWERS_EMAIL,
            'PULL_REQUEST_TITLE' => $PULL_REQUEST_TITLE,
            'PULL_REQUEST_TO_BRANCH' => $PULL_REQUEST_TO_BRANCH,
            'PULL_REQUEST_TO_REPO_SLUG' => $PULL_REQUEST_TO_REPO_SLUG,
            'PULL_REQUEST_URL' => $PULL_REQUEST_URL,
            'PULL_REQUEST_USER_EMAIL_ADDRESS' => $PULL_REQUEST_USER_EMAIL_ADDRESS,
            'PULL_REQUEST_USER_NAME' => $PULL_REQUEST_USER_NAME,
        ];
    }

    /**
     * @param $users
     * 设置git用户信息缓存
     */
    private function _setGitUserCache($users){
        $cache = \RedisService::getInstance('redis');
        $key   = DingTalk::XIAOMAN_DINGTALK_PREFIX.'user';

        //考虑到用户信息可能存在变更的情况并且该接口调用频率不高，故每次都重新设置缓存
        foreach ($users as $user){
            $idx = 'gitlab_'.$user['id'];
            $cache->hset($key,$idx,json_encode($user,JSON_UNESCAPED_UNICODE));
        }
    }

    public function checkParams()
    {
        if (empty($this->params['PULL_REQUEST_AUTHOR_EMAIL'])) {
            \LogUtil::info($this->logPrefix
                . 'user not exist : PULL_REQUEST_AUTHOR_EMAIL:'
                . $this->params['PULL_REQUEST_AUTHOR_EMAIL']);

            throw new \RuntimeException('author用户不存在');
        }
        if (empty($this->params['PULL_REQUEST_USER_EMAIL_ADDRESS'])) {
            \LogUtil::info($this->logPrefix
                . 'user not exist : PULL_REQUEST_USER_EMAIL_ADDRESS:'
                . $this->params['PULL_REQUEST_USER_EMAIL_ADDRESS']);

            throw new \RuntimeException('user_emails用户不存在');
        }
    }

    public function initReviewUserList()
    {
        $this->reviewUserList = array_map(function ($item)
        {
            $value = [];
            list( $value['name'], $value['email']) = \EmailUtil::getPreFixAndEmail($item);
            return $value;
        },explode(',',$this->params['PULL_REQUEST_REVIEWERS_EMAIL']??''));
    }

    public function initAuthorUser()
    {
        list( $this->authorUser['name'], $this->authorUser['email']) =
            \EmailUtil::getPreFixAndEmail($this->params['PULL_REQUEST_AUTHOR_EMAIL'] ?? '');
    }

    public function initRequestUser()
    {
        list( $this->requestUser['name'], $this->requestUser['email']) =
            \EmailUtil::getPreFixAndEmail($this->params['PULL_REQUEST_USER_EMAIL_ADDRESS'] ?? '');

        if (!$this->requestUser['name']){
            $this->requestUser['name'] = $this->params['PULL_REQUEST_USER_NAME'] ?? '';
        }
        \LogUtil::info('requestUser='.json_encode($this->requestUser,JSON_UNESCAPED_UNICODE));
    }

    public function initSendUserList()
    {
        $action = $this->action;
        $requestUser = $this->requestUser;
        $authorUser = $this->authorUser;
        $reviewUserList = $this->reviewUserList;

        if(
            $action =='COMMENTED'
        ) {
            //如果是作者回复的评论,就发给review者
            if( $requestUser['email'] == $authorUser['email'])
            {
                $this->sendUserList = $reviewUserList;
            }else{
                $this->sendUserList[] = $authorUser;
            }
        }elseif(
            $action == 'open'
            || $action == 'update'
            || $action == 'reopen'
        ) {
            $this->sendUserList = $reviewUserList;
        } elseif(
            $action == 'close'
            || $action == 'merge'
            || $action == 'approved'
            || $action == 'unapproved'
        ) {
            $this->sendUserList[] = $authorUser;
        } else {
            \LogUtil::info("[git]: {$action} action 错误");
            throw new \RuntimeException('参数错误');
        }
    }

    public function initMessageContent($content='')
    {
        $action = $this->action;
        $params = $this->params;
        $requestUser = $this->requestUser;
        $actionMap = self::ACTION_MAP;
        $commentActionMap = self::COMMENT_ACTION_MAP;

        $messageBody = " \n 标题: {$this->title} \n 合并:【{$this->fromRepo}】{$this->formBranch} -> 【{$this->toRepo}】{$this->toBranch}";

        if ($action == 'COMMENTED') {
            $commentAction = $params['PULL_REQUEST_COMMENT_ACTION'];
            $comment = $params['PULL_REQUEST_COMMENT_TEXT'];
            $actionTitle = $commentActionMap[$commentAction] ?? $actionMap[$action];
            $this->content = "【git】{$requestUser['name']} {$actionTitle} {$messageBody} \n 评论: {$comment} \n 详情: {$this->prUrl}";
        } elseif (
            $action == 'open'
            || $action == 'update'
            || $action == 'reopen'
            || $action == 'close'
            || $action == 'merge'
            || $action == 'approved'
            || $action == 'unapproved'
        ) {
            $this->content = "【git】:{$requestUser['name']} {$actionMap[$action]} {$messageBody} \n 详情: {$this->prUrl}";
        } else {
            \LogUtil::info("【git】: {$action} action 错误");
            throw new \RuntimeException('参数错误');
        }
    }

    protected function notifyRobots(): array
    {
        $sqlList = "select * from tbl_git_robot_settings where project=:project and (branch = '' or :branch REGEXP branch)";
        $robotList = \Yii::app()->prometheus_db->createCommand($sqlList)->queryAll(true, [':project'=>$this->toRepo, ':branch'=>$this->toBranch]);

        $ret = [];
        foreach ($robotList as $robot) {
            $ret[] = [
                'access_token' => $robot['access_token'],
                'access_secret' => $robot['access_secret'],
            ];
        }
        return $ret;
    }

    /**
     * @return array
     */
    public function notifyContentWithTargets() : array
    {
        $emails = [];
        foreach ($this->sendUserList as $item) {
            //不给操作者发送
            if ($item['email'] == $this->requestUser['email']) {
                \LogUtil::info("$this->logPrefix 发送者和操作者是同一人,不发送同事: email:{$item['email']}");
                continue;
            }
            //邮箱为空，跳过
            if (empty($item['email'])) {
                continue;
            }
            $emails[] = $item['email'];
        }
        return ['emails' => $emails, 'robots' => $this->notifyRobots(), 'content' => $this->content];
    }

    public function getGitlabUser(){

        $redis = \RedisService::cache();
        $gitlabUserList = json_decode($redis->get(self::REDIS_GITLAB_USER_LIST_KEY), true);

        if ($gitlabUserList)
            return $gitlabUserList;

        $page = 1;
        $userList = [];
        $max = 10;
        //防止 gitlab 返回数据结构调整或异常导致死循环
        while ($page < $max){
            $util = new \HttpUtil();
            //此接口需要管理员权限，故用
            $util::setHeader(['Authorization: Bearer '.self::GITLAB_TOKEN]);
            $result = $util::doGet(self::URL_GET_GITLAB_USER.'&page='.$page ,5);
            \LogUtil::info(__FUNCTION__ . ' gitlab request result : ' .$result);
            if (!$result){
                \LogUtil::error('get gitlab users failed!');
                break;
            }

            $user = json_decode($result,true);
            if (!$user) {
                break;
            }
            $userList = array_merge_recursive($userList,$user);
            $page++;
        }

        $gitlabUserList = array_column($userList,'email','id');

        // 不为空的情况下再缓存
        !empty($gitlabUserList) && (count($gitlabUserList) > 180) && $redis->setex(self::REDIS_GITLAB_USER_LIST_KEY, 60 * 60, json_encode($gitlabUserList));

        return $gitlabUserList;
    }

    
    //获取MR请求详细信息
    public function getMrDetail($pid,$iid)
    {
        $url = self::URL_GET_GITLAB_MR_INFO;
        $url = str_replace(':pid',$pid,$url);
        $url = str_replace(':iid',$iid,$url);
        \LogUtil::error('url='.$url);
        $util = new \HttpUtil();
        $util::setHeader(['Authorization: Bearer '.self::GITLAB_TOKEN]);
        $result = $util::doGet($url);
        if (!$result){
            \LogUtil::error('get gitlab mr request failed!iid='.$iid);
            return [];
        }
        return json_decode($result,true) ?? [];
    }
}
