<?php
/**
 * Created by PhpStorm.
 * User: Tony
 * Date: 18/8/1
 * Time: 下午4:38
 */

namespace common\library\trail;

use common\library\opportunity\Opportunity;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\second_privilege\SecondPrivilegePermission;

/**
 * Class OpportunityDynamicList
 * @package common\library\trail
 * @method  DynamicTrailFormatter getFormatter
 */
class OpportunityDynamicList extends DynamicListAbstract
{
    protected $skipPrivilege = false;

    public function __construct(int $clientId = 0, int $opportunityId = 0)
    {
        parent::__construct($clientId);
        $this->setOpportunityId($opportunityId);
        $this->getFormatter()->setShowOpportunityInfo(true);
    }

    public function buildParams($esSearch = true)
    {
        if (!$this->skipPrivilege) {
            $opportunity = new Opportunity($this->client_id, $this->opportunity_id);
            $opportunity->setUserId($this->operatorUserId);
            if (!$opportunity->canViewTrail()) {
                return ['0=1', []];
            }
        }

        list($sql, $params) = parent::buildParams($esSearch);
        $sql .= ' AND opportunity_id > 0';
        return [$sql, $params];
    }

    public function renderLastTrail()
    {
        $this->setFormatFlag(true);
        $alias = empty($this->alias) ? '' : $this->alias . '.';
        $this->setFields("distinct on ({$alias}opportunity_id) {$alias}*");
    }

    public function setSkipPrivilege($flag)
    {
        $this->skipPrivilege = $flag;
    }
}