<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/10/30
 * Time: 11:29 AM
 */

namespace common\library\statistics\decorator\field_refer;


use common\library\invoice\OrderList;
use common\library\statistics\Locator;

class OrderRefer extends AbstractFieldRefer
{

    public function getResult()
    {
        $userId = Locator::instance()->getUserId();

        $referValues = array_values(array_unique(array_filter($this->referValues)));

        if( empty($referValues) )
            return [];

        $orderList = new OrderList($userId);
        $orderList->setSkipPermissionCheck(true);
        $orderList->setEnableFlag(null);    //删除的也显示
        $orderList->setOrderIds($referValues);
        $orderList->setFields(['order_id', 'name', 'enable_flag']);
        $list = $orderList->find();
        $map = [];
        foreach ( $list as $item)
        {
            $map[$item['order_id']] = $item['enable_flag']? $item['name']: ($item['name'].'【'.\Yii::t('report', '已删除').'】');
        }

        return $map;

    }
}
