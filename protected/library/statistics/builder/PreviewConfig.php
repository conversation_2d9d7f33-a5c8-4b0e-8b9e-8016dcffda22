<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/10/16
 * Time: 11:49 PM
 */

namespace common\library\statistics\builder;


class PreviewConfig
{
    protected $userId;
    protected $reportKey;

    protected $name;
    protected $title;
    protected $description;
    protected $config;
    protected $category;

    const PREVIEW_TIME = 6000 ; //临时调大一点测试
    /**
     * PreviewConfig constructor.
     * @param $userId
     * @param $reportKey
     */
    public function __construct($userId, $reportKey='')
    {
        $this->userId = $userId;
        $this->reportKey = $reportKey;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @param mixed $title
     */
    public function setTitle($title): void
    {
        $this->title = $title;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description): void
    {
        $this->description = $description;
    }

    /**
     * @param mixed $config
     */
    public function setConfig(array $config): void
    {
        $this->config = $config;
    }

    /**
     * @param mixed $category
     */
    public function setCategory($category): void
    {
        $this->category = $category;
    }


    private function productReportKey()
    {
        $this->reportKey = BuilderConstant::PREVIEW_CACHE_KEY_PREFIX.$this->userId.':'.date('YmdHis');
        return $this->reportKey;
    }


    public function saveData()
    {
        $name = $this->name?: \Yii::t('report','预览报表').date('YmdHis');
        $this->productReportKey();
        $data = [
            'name' => $name,
            'title' => $this->title?:$name,
            'description' => $this->description,
            'category' => $this->category,
            'config' => $this->config
        ];


        $redis  = \RedisService::sf();
        $redis->set($this->reportKey, json_encode($data), 'Ex', self::PREVIEW_TIME);

        return $this->reportKey;
    }


    public function getData()
    {
        $redis  = \RedisService::sf();
        $data = $redis->get($this->reportKey);

        return json_decode($data, true)?:[];
    }


}
