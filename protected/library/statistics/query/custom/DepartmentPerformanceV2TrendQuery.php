<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2019/4/15
 * Time: 下午3:27
 */

namespace common\library\statistics\query\custom;


use common\library\department\DepartmentPermission;
use common\library\performance_v2\goal\PerformanceV2Goal;
use common\library\performance_v2\goal_config\NewPerformanceV2ProcessService;
use common\library\performance_v2\goal_config\PerformanceV2GoalConfig;
use common\library\performance_v2\goal_config\PerformanceV2ProcessService;
use common\library\performance_v2\Helper;
use common\library\performance_v2\PerformanceV2Constant;
use common\library\performance_v2\record\PerformanceV2RecordList;
use common\library\performance_v2\rule\PerformanceV2Rule;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\statistics\data_adapter\AbstractDataAdapter;
use common\library\statistics\data_adapter\Arr;
use common\library\statistics\Locator;


class DepartmentPerformanceV2TrendQuery extends AbstractPerformanceV2CustomQuery
{

    /**
     * @return AbstractDataAdapter
     */
    public function query()
    {
        $locator = Locator::instance();
        $clientId = $locator->getClientId();
        $loginUserId = $locator->getUserId();
        $params = $locator->getReportConfig()->getQueryConfig()->getParams(1);
        $startDate = $this->startDate;
        $endDate = $this->endDate;

        //无效参数user_ids就置为空，走查看范围的最大值
        if ($params['common.visible']->getType() == 'select_visible_department_id') {
            $departmentId = $params['common.visible']->getValue();
        } else {
            $departmentId = [];
        }

        $scopeDepId = (new DepartmentPermission($locator->getClientId(), $locator->getUserId()))->permission(PrivilegeConstants::PRIVILEGE_CRM_PERFORMANCE_VIEW)->departmentIds();

        // 未分配部门直接返回空值
        if (!is_array($departmentId) && $departmentId < 0) {
            return new Arr([]);
        }

        //可见范围为我的企业，但是没有可查看的部门id
        if ($departmentId == 0 && empty($scopeDepId)) {
            return new Arr([]);
        }

        //默认没传部门id，没有可查看的部门id
        if (is_array($departmentId) && empty($departmentId) && empty($scopeDepId)) {
            return new Arr([]);
        }

        //没传部门id， 可查看部门id有值，则取可查看部门最小值
        if (is_array($departmentId) && empty($departmentId) && !empty($scopeDepId)) {
            $departmentId = min($scopeDepId);
        }

        if ($this->ruleId == 0 || (!$this->noReferFlag && empty($this->referObjectIds))) {
            return new Arr([]);
        }

        $departmentId  = is_array($departmentId) ? current($departmentId) : $departmentId;
        $departmentIds = [$departmentId];


        $ruleConfig          = (new PerformanceV2GoalConfig($clientId, $this->ruleId))->loadFirstItemByRuleId($this->ruleId);
        $ruleInfo            = new PerformanceV2Rule($clientId,$this->ruleId);
        $calculateRule       = strtolower($ruleInfo->calculate_rule);
        $distinctWithOwnerId = \common\library\performance_v2\Helper::checkNeedDistinctWithOwnerId($ruleInfo->getAttributes());
        $recordModel         = new PerformanceV2RecordList($clientId, $loginUserId);

        if($ruleInfo->performance_type == \PerformanceV2Goals::TARGET_PROCESS_GOAL)
        {
            // 重构后代码

            $performanceV2ProcessService = new NewPerformanceV2ProcessService($clientId, $loginUserId);
            $performanceV2ProcessService->setRuleIds([$this->ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($departmentId);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            if (!$this->noReferFlag) {
                $performanceV2ProcessService->setReferObjectIds($this->referObjectIds);
            }
            $processResult = $performanceV2ProcessService->getNewProcessRuleFinishDetail();

            $data = [];
            foreach (($processResult['list'] ?? []) as $item) {
                // 特殊情况处理 周 显示处理 xxxx-xx-xx~xxxx-xx-xx/21 需要把第几周显示在后面
                $showRawYear = $item['show_raw_year'] ?? true;
                if (($processResult['configTimeType'] ?? '') == \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK) {
                    $showRawYear = false;
                }
                $data[] = [
                    'department_performance.department_id'           => $item['department_id'] ?? 0,
                    'department_performance.cycle'                   => $item['cycle'] ?? '',
                    'department_performance.year'                    => $item['year'] ?? '',
                    'department_performance.cycle_num'               => $item['cycle_num'] ?? '',
                    'department_performance.finish_amount'           => $item['indicator_value'] ?? 0.00,
                    'department_performance.last_year_finish_amount' => $item['constrast_indicator_value'] ?? 0.00,
                    'department_performance.last_month_finish_amount'=> $item['ringrate_indicator_value'] ?? 0.00,
                    'show_raw_year'                                  => $showRawYear,
                ];
            }
            unset($processResult, $processTotal);

            // 重构前代码
            /*
            empty($ruleConfig->time_granularity) && $ruleConfig->time_granularity = \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;
            $timeType = PerformanceV2Constant::TIMEGRANULARITY_TO_TIME_TYPE_MAP[$ruleConfig->time_granularity] ?? \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK;

            $departments   = \common\library\department\Helper::getChildrenIds($clientId, $departmentId);
            $departments[] = $departmentId;
            $departments   = array_filter($departments);// 过滤掉部门id=0

            $performanceV2ProcessService = new PerformanceV2ProcessService($clientId, $loginUserId);
            $performanceV2ProcessService->setFilterDisplaySetting(false);
            $performanceV2ProcessService->setRuleIds([$this->ruleId]);
            $performanceV2ProcessService->setStartDate($startDate);
            $performanceV2ProcessService->setReferId($departments);
            $performanceV2ProcessService->setEndDate($endDate);
            $performanceV2ProcessService->setScope(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            $performanceV2ProcessService->setShowGoals(1);
            $performanceV2ProcessService->setShowByEachTimeType(true);
            $performanceV2ProcessService->setTimeType($timeType);
            $performanceV2ProcessService->setShowRingRatio(true);
            $performanceV2ProcessService->setShowIndicatorValue(1);
            $performanceV2ProcessService->setShowConstrast(1);
            $performanceV2ProcessService->setUserField('user_id,client_id,email,nickname');
            $performanceV2ProcessService->setIndicatorField('client_id,rule_id,owner_id,record_type,sum(indicator_value) as indicator_value,account_date');
            $processGoalList    = $performanceV2ProcessService->getProcessRuleList();
            $processGoalList    = empty($processGoalList) ? [] : array_pop($processGoalList);
            $referTimeTypeList  = $processGoalList[$performanceV2ProcessService->getScopeName()] ?? [];
            $data               = [];

            switch ($ruleConfig->time_granularity) {
                // 天
                case \PerformanceV2GoalsConfig::TIME_GRANULARITY_DAY:
                    foreach ($referTimeTypeList as $year => $monthList) {
                        foreach ($monthList as $month => $dayList) {
                            foreach ($dayList as $dayIndex => $referList) {
                                if (strtotime($dayIndex) < strtotime($startDate)) {
                                    continue;
                                }
                                foreach ($referList as $referId => $referInfo) {
                                    $timeStart = date("Y-m-d", strtotime($referInfo['time_start']));
                                    $detail    = [
                                        'department_performance.department_id'            => $referId,
                                        'department_performance.year'                     => $timeStart,
                                        'department_performance.cycle'                    => 0,
                                        'department_performance.cycle_num'                => $dayIndex,
                                        'department_performance.finish_amount'            => $referInfo['indicator_value'],
                                        'department_performance.last_year_finish_amount'  => $referInfo['constrast_indicator_value'],
                                        'department_performance.last_month_finish_amount' => $referInfo['ringrate_indicator_value'],
                                    ];
                                    $data[] = $detail;
                                }
                            }
                        }
                    }

                    break;

                // 周
                case \PerformanceV2GoalsConfig::TIME_GRANULARITY_WEEK:
                    foreach ($referTimeTypeList as $year => $weekList) {
                        foreach ($weekList as $weekIndex => $referList) {
                            foreach ($referList as $referId => $referInfo) {
                                $timeStart = date("Y-m-d", strtotime($referInfo['time_start']));
                                $timeEnd   = date("Y-m-d", strtotime($referInfo['time_end']));
                                $detail    = [
                                    'department_performance.department_id'            => $referId,
                                    'department_performance.year'                     => "{$timeStart}~{$timeEnd}",
                                    'department_performance.cycle'                    => 1,
                                    'department_performance.cycle_num'                => $weekIndex,
                                    'department_performance.finish_amount'            => $referInfo['indicator_value'],
                                    'department_performance.last_year_finish_amount'  => $referInfo['constrast_indicator_value'],
                                    'department_performance.last_month_finish_amount' => $referInfo['ringrate_indicator_value'],
                                ];
                                $data[] = $detail;
                            }
                        }
                    }
                    break;

                // 月
                case \PerformanceV2GoalsConfig::TIME_GRANULARITY_MONTH:
                    foreach ($referTimeTypeList as $year => $monthList) {
                        foreach ($monthList as $month => $referList) {
                            foreach ($referList as $referId => $referInfo) {
                                $detail = [
                                    'department_performance.department_id'            => $referId,
                                    'department_performance.year'                     => $year . '/' .(($month < 10) ? "0{$month}" : $month),
                                    'department_performance.cycle'                    => 1, //一月份作为拆分
                                    'department_performance.cycle_num'                => $month,
                                    'department_performance.finish_amount'            => $referInfo['indicator_value'],
                                    'department_performance.last_year_finish_amount'  => $referInfo['constrast_indicator_value'],
                                    'department_performance.last_month_finish_amount' => $referInfo['ringrate_indicator_value'],
                                ];
                                $data[] = $detail;
                            }
                        }
                    }
                    break;

            }
            */
            return new Arr($data);
        }

        $recordList = new PerformanceV2RecordList($clientId, $loginUserId);
        $recordList->setSkipOwnerCheck(true);
        $recordList->setRuleId($this->ruleId);

        $targetField = Helper::getTargetField($clientId, $this->ruleId);
        // 公式统计可能没有设置department_agg_type,默认给它为空
        $targetField['department_agg_type'] = $targetField['department_agg_type'] ?? '';
        if ($targetField['department_agg_type'] == PerformanceV2Constant::DEPARTMENT_AGG_TYPE_USER) {

            if ($departmentId != 0) {
                $department = new \common\library\department\Department($clientId, $departmentId);
                $departmentUserIds = $department->getMemberList(true);
                $recordList->setOwnerId($departmentUserIds);
                $recordModel->setOwnerId($departmentUserIds);
                $recordModel->setRecordType(PerformanceV2Constant::SETTING_GOAL_SCOPE_USER);
            }
            $recordList->setRecordType(PerformanceV2Constant::RECORD_TYPE_USER);
        } else {
            $departments = \common\library\department\Helper::getChildrenIds($clientId, $departmentId);
            $departments[] = $departmentId;
            $departmentIds = array_merge($departmentIds, $departments);
            $recordList->setOwnerId($departments);
            $recordList->setRecordType(PerformanceV2Constant::RECORD_TYPE_DEPARTMENT);
            $recordModel->setRecordType(PerformanceV2Constant::SETTING_GOAL_SCOPE_GROUP);
            $recordModel->setOwnerId($departmentIds);
        }

        $recordModel->setRuleId($this->ruleId);
        $recordModel->setStartAccountDate($startDate);
        $recordModel->setEndAccountDate($endDate);


        if($distinctWithOwnerId){
            $recordModel->setDistinctField('client_id,rule_id,refer_id,owner_id');
        } else {
            $recordModel->setDistinctField('client_id,rule_id,refer_id');
        }

        if($calculateRule == 'count'){
            $recordModel->setFields(' count(1) as cnt ');
        } else {
            $recordModel->setFields(' sum(indicator_value) as indicator_value ');
        }
        if (!$this->noReferFlag) {
            $recordModel->setReferId($this->referObjectIds);
        }

        $totalAmount = $recordModel->distinctRecordCount();

        $recordList->setStartAccountDate(date('Y-m-d', strtotime( $startDate)));
        $recordList->setEndAccountDate(date('Y-m-d', strtotime($endDate)));
        $recordList->setAssociateLastYear(true);
        $recordList->setGroupBy(['year', 'cycle_num']);
        $recordList->setFields(["extract(month from account_date) as cycle_num","extract(year from account_date) as year","sum(indicator_value) as sum_amount"]);
        if (!$this->noReferFlag) {
            $recordList->setReferId($this->referObjectIds);
        }

        // 首先根据refer_id,owner_id来区分
        $recordList->setDistinctField('client_id,rule_id,refer_id,owner_id');
        $departmentPerformances = $recordList->getIndicatorValueByDistinctFields();

        // 无权限查看所有
        if (!count($departmentPerformances)) {
            return new Arr([]);
        }

        $departmentPerformancesMap = [];
        foreach ($departmentPerformances as $userPerformance) {
            $key = implode('_', [
                $departmentId,
                $userPerformance['year'],
                $userPerformance['cycle_num'],
            ]);
            $departmentPerformancesMap[$key] = $userPerformance['sum_amount'];
        }

        $data = [];
        $cycleList = PerformanceV2Goal::getCycleListByStartAndEndDate($startDate, $endDate);
        foreach ($cycleList as $cycleInfo) {
            $detail = [
                'department_performance.department_id'            => $departmentId,
                'department_performance.year'                     => $cycleInfo['year'],
                'department_performance.cycle'                    => 1,
                'department_performance.cycle_num'                => $cycleInfo['cycle_num'],
                'department_performance.finish_amount'            => '0',
                'department_performance.last_year_finish_amount'  => '0',
                'department_performance.last_month_finish_amount' => '0',
            ];
            $finishKey = implode('_', [
                $departmentId,
                $cycleInfo['year'],
                $cycleInfo['cycle_num'],
            ]);
            $lastYearKey = implode('_', [
                $departmentId,
                $cycleInfo['year'] - 1,
                $cycleInfo['cycle_num'],
            ]);
            $lastCycleKey = implode('_', [
                $departmentId,
                $cycleInfo['cycle_num'] > 1 ? $cycleInfo['year'] : $cycleInfo['year'] - 1,
                $cycleInfo['cycle_num'] > 1 ? $cycleInfo['cycle_num'] - 1 : 12,
            ]);

            $detail['department_performance.finish_amount'] = round($departmentPerformancesMap[$finishKey] ?? 0, 2);
            $detail['department_performance.last_year_finish_amount'] = round($departmentPerformancesMap[$lastYearKey] ?? 0, 2);
            $detail['department_performance.last_month_finish_amount'] = round($departmentPerformancesMap[$lastCycleKey] ?? 0, 2);
            $data[] = $detail;
        }

        $summates = $locator->getReportConfig()->getFormatConfig()->getSummate();
        foreach ($summates as $summate) {
            if ($summate->getMethod() == 'sum'
                && $summate->getField() == 'department_performance.finish_amount') {
                $summate->setTotal(round($totalAmount, 2));
            }
        }

        $result = new Arr($data);
        $result->setCount($totalAmount);
        return $result;
    }
}
