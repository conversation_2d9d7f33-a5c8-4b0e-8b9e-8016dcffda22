reportObject: opportunity:company
attributes: # 后续可能保存在数据库中
  key: khsj6
  title: '客户销售金额情况'
  desc: '统计客户商机的销售金额'
  name: '客户销售金额情况'
  type: group
  relevance:
    - key: kh11
      name: 客户
    - key: khsj2
      name: 客户类型
    - key: khsj3
      name: 客户来源
    - key: khsj4
      name: 客户星级
    - key: khsj5
      name: 客户分组
    - key: khsj6
      name: 国家地区
    - key: khsj7
      name: 来源店铺
  exchangeRelevance:
    - key: khdd6
      tab_key: kh10
      type: order
      type_name: 基于销售订单
    - key: khsj6
      tab_key: kh10
      type: opportunity
      type_name: 基于商机

fieldConfig:
  renderFields:
    company.country:
      type: string
      show: true
    opportunity.amount:
      show: true
    company.company_id:
      type: int

  queryFields: [company.name,company.country,company.company_id,company.name,company.serial_id,company.archive_time,company.user_id,company.client_id,opportunity.opportunity_id,opportunity.company_id,opportunity.amount,opportunity.currency,opportunity.exchange_rate,opportunity.exchange_rate_usd,cash_collection.cash_collection_id,cash_collection.amount,cash_collection.currency,cash_collection.exchange_rate,cash_collection.exchange_rate_usd,cash_collection.order_id,cash_collection.collect_status,cash_collection.opportunity_id]

queryConfig:
  variable:
    - field:  common.visible
      comment: 查看范围
      type: select_visible_user_id
      multiple: 1
      value: []

    - field: opportunity.account_date
      comment: 结束日期
      type: date
      value:
        start:
        end:
      multiple: 0
      period: y
      default: 1
      continuous: 1

    - field: opportunity.flow_id
      type: 3
      value:
      multiple: 1

    - field: opportunity.stage
      type: 3
      value:
      multiple: 1

    - field: opportunity.type
      type: 3
      value:
      multiple: 1

    - field: opportunity.origin_list
      type: 7
      value:
      multiple: 1

    - field: opportunity.fail_type
      type: 3
      value:
      multiple: 1

    - field: opportunity.currency
      type: 3
      value:
      multiple: 1

    - field: opportunity.create_time
      type: date
      value:
        start:
        end:
      continuous: 1

    - field: company.owner_type
      type: 3
      value:
      continuous: 1

    - field: company.country
      type: 3
      value:
      continuous: 1

    - field: company.origin_list
      type: 7
      value:
      continuous: 1

    - field: company.star
      type: 3
      value:
      continuous: 1

    - field: company.group_id
      type: 3
      value:
      continuous: 1

    - field: company.pool_id
      type: 3
      value:
      continuous: 1

    - field: company.trail_status
      type: 3
      value:
      continuous: 1

    - field: company.serial_id
      type: 1
      value:
      continuous: 1

    - field: company.name
      type: 1
      value:
      continuous: 1

    - field: company.biz_type
      type: 3
      value:
      continuous: 1

    - field: company.scale_id
      type: 3
      value:
      continuous: 1

    - field: company.archive_time
      type: date
      value:
        start:
        end:
      continuous: 1

    - field: company.order_time
      type: date
      value:
        start:
        end:
      continuous: 1


    - field: common.select_regional_scope
      comment: 区域粒度
      type: select
      options:
        - label: 按洲（一级）
          value: 1
        - label: 按区域（二级）
          value: 2
        - label: 按国家（三级）
          value: 3
      value: 2
      multiple: 0

  flow:
    set:
      - node:
          - object: opportunity
            type: base
            limit:
              nodeLimit:
              setLimit:
              filter:
                outer:
                  - field: start_account_date
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_account_date
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                  - field: owner
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: flow_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: stage
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: origin_list
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: fail_type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: currency
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: start_create_time
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_create_time
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                  - field: visible.user_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND
                inner:
            merge:
              mode:
                connect:
                  parentNodeId:
                  currentNodeId:

          - object: company
            type: refer
            limit:
              nodeLimit:
                id: company_id
                range: opportunity.company_id
              setLimit:
              filter:
                outer:
                  - field: owner_type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: country
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: origin_list
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: star
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: group_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: pool_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: trail_status
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: serial_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: name
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: biz_type
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: scale_id
                    operation: eq
                    referenceValue: :variable
                    logic: AND

                  - field: start_archive_time
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_archive_time
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                  - field: start_order_time
                    operation: gt
                    referenceValue: :variable
                    logic: AND

                  - field: end_order_time
                    operation: lt
                    referenceValue: :variable
                    logic: AND

                inner:
            merge:
              mode: intersection
              connect:
                parentNodeId: opportunity.company_id
                currentNodeId: company.company_id
          - object: cash_collection
            type: refer
            limit:
              nodeLimit:
                id: opportunity_id
                range: opportunity.opportunity_id
              setLimit:
              filter:
                outer:
                inner:
            merge:
              mode: union
              connect:
                parentNodeId: opportunity.opportunity_id
                currentNodeId: cash_collection.opportunity_id

formatConfig:
  decoratorConfig:
    - type: field
      field: company.country
      field_type: country
      extra:
        format: select

    - type: field
      field: opportunity.amount
      field_type: currency
      extra:
        currencyField: opportunity.currency # 币种关联字段
        currency:  # 币种
        exchangeRate:  # 汇率
          CNY:  # 折人民币汇率数值*100
          USD:  # 折美元汇率数值*100
        exchangeRateField:  # 汇率关联字段
          CNY: opportunity.exchange_rate # 折人民币汇率字段
          USD: opportunity.exchange_rate_usd # 折美元汇率字段

    - type: field
      field: cash_collection.amount
      field_type: currency
      extra:
        currencyField: cash_collection.currency # 币种关联字段
        currency:  # 币种
        exchangeRate:  # 汇率
          CNY:  # 折人民币汇率数值*100
          USD:  # 折美元汇率数值*100
        exchangeRateField:  # 汇率关联字段
          CNY: cash_collection.exchange_rate # 折人民币汇率字段
          USD: cash_collection.exchange_rate_usd # 折美元汇率字段

  groupConfig:
    - type: row # 组行 , 组列
      field: company.country
      referId: company.company_id

  orderConfig:
    - key: company.country
      field: sum-opportunity.amount
      order: desc

  summateConfig:
    - method: row
      title: 客户数
      field: company.company_id
      unique: company.company_id
      unique_strict: true
      referListId: company.company_id
      referList: companyList
    - method: sum #
      field: opportunity.amount
      unique: opportunity.opportunity_id
      unique_strict: true
    - method: sumPercent # 总和占比
      title: 总和占比
      field: opportunity.amount
    - method: row # 计数
      title: '商机数'
      field: opportunity.opportunity_id
      referListId: opportunity.opportunity_id
      referList: opportunityList
      unique: opportunity.opportunity_id
      unique_strict: true
    - method: row
      title: 回款单数
      field: cash_collection.cash_collection_id
      unique: cash_collection.cash_collection_id
      unique_strict: true
      referListId: cash_collection.cash_collection_id
      referList: cashCollectionList
    - method: sum
      title: 回款金额
      field: cash_collection.amount
    - method: row
      title: 已生效回款单数
      field: cash_collection.cash_collection_id
      unique: cash_collection.cash_collection_id
      unique_strict: true
      referListId: cash_collection.cash_collection_id
      referList: cashCollectionList
      alias: cash_collection.cash_collection_count
      filter:
        cash_collection.collect_status: 1
    - method: sum
      title: 已生效回款金额
      field: cash_collection.amount
      alias: cash_collection.collect_amount
      filter:
        cash_collection.collect_status: 1
chartConfig:
  chartList:
    - chartType: 'pie' #柱状图：vertical-bar，条形图：horizontal-bar 饼图: pie
      group:
        - company.country
      summaries:
        - sum-opportunity.amount
      option: []

eventConfig:
  - event: onInit
    handler: SelectAdvance
  - event: onInit
    handler: CheckModuleAdvance






