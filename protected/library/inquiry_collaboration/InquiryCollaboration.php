<?php

namespace common\library\inquiry_collaboration;

use common\components\BaseObject;
use common\library\APIConstant;
use common\library\approval_flow\ApplyForm;
use common\library\approval_flow\traits\OperateTriggerApproval;
use common\library\approval_flow\traits\TraitApprovalFlow;
use common\library\history\base\Builder;
use common\library\inquiry_collaboration\history\InquiryCollaborationSetting;
use common\library\inquiry_collaboration\notify\InquiryCollaborationCreateNotificationNotify;
use common\library\inquiry_collaboration\notify\InquiryCollaborationEditNotificationNotify;
use common\library\inquiry_collaboration_product\BatchInquiryCollaborationProduct;
use common\library\inquiry_collaboration_product\InquiryCollaborationProduct;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductFilter;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductMetadata;
use common\library\inquiry_collaboration_product\InquiryCollaborationProductOperator;
use common\library\invoice\Invoice;
use common\library\lock\TraitReferLock;
use common\library\object\field\service\FunctionFieldService;
use common\library\object\field\updator\calculator\SyncCalculator;
use common\library\object\object_define\Constant as ObjConstant;
use common\library\oms\common\OmsConstant;
use common\library\oms\product_transfer\dynamic\builder\TransferInvoiceDynamicFactory;
use common\library\oms\product_transfer\dynamic\TransferInvoiceDynamicConstant;
use common\library\oms\product_transfer\ProductTransfer;
use common\library\oms\traits\ExchangeRateTrait;
use common\library\oms\traits\InvoiceEditTrait;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\Helper;
use common\library\schedule\Schedule;
use common\library\serial\GenerateService;
use common\library\server\es_search\SearchQueueService;
use common\library\workflow\trigger\SingleObjectTriggerTrait;
use Constants;
use ErrorCode;
use User;
use xiaoman\orm\common\ObjectField;
use xiaoman\orm\common\SingleObjectV2;
use xiaoman\orm\exception\OrmException;


/**
 * class InquiryCollaboration
 * @package common\library\inquiry_collaboration
 * @property int inquiry_collaboration_id
 * @property string inquiry_collaboration_no
 * @property int client_id
 * @property string inquiry_collaboration_name
 * @property int status
 * @property float progress
 * @property mixed handler
 * @property mixed inquiry_deadline
 * @property int create_schedule
 * @property string remark
 * @property mixed finish_time
 * @property mixed attachments
 * @property string currency
 * @property float exchange_rate
 * @property float exchange_rate_usd
 * @property float exchange_rate_cny
 * @property string shipment_port
 * @property string target_port
 * @property string transport_mode
 * @property string price_contract
 * @property int customer_id
 * @property int lead_id
 * @property int opportunity_id
 * @property string country
 * @property string customer_phone
 * @property string customer_email
 * @property string customer_name
 * @property mixed social_media
 * @property mixed source
 * @property mixed inquiry_source
 * @property mixed external_field_data
 * @property int enable_flag
 * @property int create_user
 * @property mixed create_time
 * @property int update_user
 * @property mixed update_time
 * @property float product_total_amount
 * @property float product_total_amount_rmb
 * @property float product_total_amount_usd
 * @property float product_total_count
 * @property string scope_user_ids 权限归属人
 *
 * @method InquiryCollaborationOperator getOperator()
 * @method InquiryCollaborationFormatter getFormatter()
 */
class InquiryCollaboration extends SingleObjectV2 {
    use InitInquiryCollaborationMetadata;
    use ExchangeRateTrait;
    use OperateTriggerApproval;
    use TraitReferLock;
    use TraitApprovalFlow;
    use SingleObjectTriggerTrait;
    use InvoiceEditTrait;

    public static array $status_map = [
            OmsConstant::PRODUCT_TRANSFER_STATUS_NOT_ACCEPTED ,
            OmsConstant::PRODUCT_TRANSFER_STATUS_ACCEPTED ,
            OmsConstant::PRODUCT_TRANSFER_STATUS_TO_HANDLE ,
            OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH,
            OmsConstant::PRODUCT_TRANSFER_STATUS_REJECTED ,
    ];

    const NULL_NOT_DEFAULT_FILED = ['target_price',"purchase_quote_cny","purchase_quote",
        "vat_rate","tax_refund_rate","count"];


    private ?array $record_list = null;

    private InquiryCollaboration $oldInquiryCollaboration;

    /** @var BatchInquiryCollaborationProduct */
    private  $batchCreateInquiryCollaborationProduct;
    /** @var BatchInquiryCollaborationProduct */
    private $batchUpdateInquiryCollaborationProduct;
    /**  @var BatchInquiryCollaborationProduct */
    private $batchDeleteInquiryCollaborationProduct;

    private array $addInquiryProductIds;
    private array $updateInquiryProductIds;
    private array $deleteInquiryProductIds;

    public function getWorkflowReferType()
    {
        return \Constants::TYPE_INQUIRY_COLLABORATION;
    }

    /**
     * @return array
     */
    public function getRecordList() {
        return $this->record_list;
    }

    /**
     * @param array $record_list
     */
    public function setRecordList(array $record_list): void {
        $this->record_list = $record_list;
    }



    public static function getMetadataClass() {
        return InquiryCollaborationMetadata::class;
    }

    public function __construct(int $clientId, int $inquiry_collaboration_id = null) {
        parent::__construct($clientId);
        if (!empty($inquiry_collaboration_id)) {
            $this->loadById($inquiry_collaboration_id);
        }
    }

    public function loadById($inquiryCollaborationId)
    {
        $this->load(['client_id' => $this->_clientId, 'inquiry_collaboration_id' => $inquiryCollaborationId, 'enable_flag' => Constants::ENABLE_FLAG_TRUE]);
    }

    public function bindApplyForm(ApplyForm $applyForm)
    {
        $content = $applyForm->getContent();
        $attributeKeys = $this->getMetadata()->getAttributesKeys();
        $externalFieldData = $this->external_field_data ?: [];
        $attributes = [];
        $productList = null;
        foreach ($content as $attribute => $value) {
            if ($attribute == 'product_list') {
                $productList = $value['new'];
                continue;
            }
            if (!in_array($attribute, $attributeKeys)) {
                continue;
            }
            if (is_numeric($attribute)) {
                $externalFieldData[$attribute] = $value['new'];
            }
            $attributes[$attribute] = $value['new'];
        }
        $this->bindAttrbuties($attributes);

        if (is_null($productList)) {
            $this->loadRecordList();
        } else {
            $this->setRecordList($productList);
            $this->refreshProductList();
        }
        $this->external_field_data = $externalFieldData;
        $this->getFormatter()->displayApprovalProductList($this->getRecordList());
    }


    protected function afterDelete() {
        $inquiryCollaborationProduct = new InquiryCollaborationProduct($this->client_id);
        $inquiryCollaborationProduct->inquiry_collaboration_id = $this->inquiry_collaboration_id;
        $inquiryCollaborationProduct->delete();
        parent::afterDelete();
    }


    protected function beforeUpdate() {
        if (is_array($this->country) && isset($this->country['country'])) {
            $this->country = $this->country['country'];
        }

        $this->update_user =$this->getDomainHandler()->getUserId();
        $this->update_time = xm_function_now();
        if(empty($this->finish_time)){
            $this->finish_time = '1970-01-01';
        }
        $this->inquiry_source =   $this->inquiry_source?? [];
        $this->attachments = array_values($this->attachments ?? []);
        $this->oldInquiryCollaboration = $this;
        $this->calculateFunctionalFields();
        $this->refreshProductList();
        return parent::beforeUpdate();
    }

    /**
     * @throws OrmException
     * @throws \Exception
     */
    protected function afterUpdate() {

        if (!is_null($this->batchCreateInquiryCollaborationProduct)) {
            $this->batchCreateInquiryCollaborationProduct->getOperator()->create();
        }

        if (!is_null($this->batchUpdateInquiryCollaborationProduct)) {
            $this->batchUpdateInquiryCollaborationProduct->getOperator()->update();
        }

        if (!is_null($this->batchDeleteInquiryCollaborationProduct)) {
            $this->batchDeleteInquiryCollaborationProduct->getOperator()->delete();
        }

        $this->updateIndex();
        $notify = new InquiryCollaborationEditNotificationNotify($this->getDomainHandler()->getUserId(),$this,$this->oldInquiryCollaboration);
        $notify->prepare()->push();
        InquiryHelper::updateProcess($this->client_id,array($this->inquiry_collaboration_id));
//        \common\library\oms\inquiry_feedback\Helper::batchRefreshUsers([$this->inquiry_collaboration_id], $this->handler);
        parent::afterUpdate();
    }

    public function beforeCreate(): bool {
        $this->status = isset($this->status) ? $this->status : OmsConstant::PRODUCT_TRANSFER_STATUS_NOT_ACCEPTED;
        if ($this->inquiry_collaboration_no) {
            $this->checkNoRules($this->inquiry_collaboration_no);
        } else {
            $this->inquiry_collaboration_no = $this->getUniqueNo();
        }
        $this->attachments = array_values($this->attachments ?? []);
        $this->inquiry_source =   $this->inquiry_source?? [];
        $this->inquiry_collaboration_id = \ProjectActiveRecord::produceAutoIncrementId();
        $this->create_user = $this->getDomainHandler()->getUserId();
        $this->update_user =$this->getDomainHandler()->getUserId();
        $this->create_time = xm_function_now();
        $this->update_time = xm_function_now();
        $this->calculateFunctionalFields();
        $this->refreshProductList();
        return parent::beforeCreate();
    }

    public function calculateFunctionalFields()
    {
        if (empty($this->record_list)) {
            return;
        }
        FunctionFieldService::specialFunctionFieldHandle($this->_attributes, $this->_clientId, ObjConstant::OBJ_INQUIRY_COLLABORATION);
        $syncCalculator = new SyncCalculator($this->_clientId, ObjConstant::OBJ_INQUIRY_COLLABORATION);
        $syncCalculator->setMainData($this->_attributes);
        $syncCalculator->setSubData(ObjConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT, $this->record_list);
        $syncCalculator->calculate();

        $this->_attributes = $syncCalculator->getMainData();
        $this->record_list = $syncCalculator->getSubData(ObjConstant::OBJ_INQUIRY_COLLABORATION_PRODUCT);
    }
    /**
     * @throws OrmException
     * @throws \Exception
     */
    public function afterCreate(): void {
        if (!is_null($this->batchCreateInquiryCollaborationProduct)) {
            $this->batchCreateInquiryCollaborationProduct->getOperator()->create();
        }
        $this->triggerCreateApproval();
        $this->operationRecordSave();
        $this->createTransfer();
        $this->scheduleSave();
        $this->updateIndex();
        InquiryHelper::updateProcess($this->client_id,array($this->inquiry_collaboration_id));
        $notify = new InquiryCollaborationCreateNotificationNotify($this->getDomainHandler()->getUserId(),$this);
        $notify->prepare()->push();
    }



    private function getUniqueNo(): string {
        $generate = GenerateService::getInstance($this->_clientId, \Constants::TYPE_INQUIRY_COLLABORATION);
        $count = 0;
        $inquiry_collaboration_no = '';
        do {
            $inquiry_collaboration_no = $generate->generate();
            if ($this->checkExistForNo($inquiry_collaboration_no)) {
                \LogUtil::error("重复编号 {$inquiry_collaboration_no}  {$this->client_id}  {$this->inquiry_collaboration_id} keysInfo:" . json_encode($generate->getNumberGeneratorKey()));
                $inquiry_collaboration_no = '';
            }
            $count++;
        } while (empty($inquiry_collaboration_no) && $count < 5);
        if (empty($inquiry_collaboration_no)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Duplicate inquiry Number,there is a problem with the custom rule Settings'));
        }
        return $inquiry_collaboration_no;
    }


    /**
     * @throws OrmException
     */
    public function createTransfer(){

        //  创建虚拟单据,为了可以沿用动态框架
        $productTransfer = new ProductTransfer($this->client_id);
        $productTransfer->client_id = $this->client_id;
        $productTransfer->type = Constants::TYPE_INQUIRY_COLLABORATION;
        $productTransfer->refer_id = $this->inquiry_collaboration_id;
        $productTransfer->refer_type = Constants::TYPE_INQUIRY_COLLABORATION;
        $productTransfer->expect_time = $this->inquiry_deadline;
        $productTransfer->handler = $this->handler;
        $productTransfer->create();

        //保存动态
        $dynamic = TransferInvoiceDynamicFactory::make($this->_clientId, $this->update_user,TransferInvoiceDynamicConstant::TYPE_CREATE);
        $dynamicParams = ["handler"=>$this->create_user];
        $dynamic->setTransferInvoice($productTransfer->transfer_invoice_id,Constants::TYPE_INQUIRY_COLLABORATION, $this->create_user, $this->handler);
        $dynamic->setParams($dynamicParams);
        $dynamic->write();
    }

    public function getRemindTime()
    {
        $threeDaysBefore = strtotime('-3day', strtotime($this->inquiry_deadline));
        if ($threeDaysBefore < strtotime(date('Y-m-d 00:00:00'))) {
            $notifyTime = date('Y-m-d 09:00:00', strtotime($this->inquiry_deadline));
        } else {
            $notifyTime = date('Y-m-d 09:00:00', strtotime('-3day', strtotime($this->inquiry_deadline)));
        }
        return [
            [
                'type' => 'ext',
                'time' => $notifyTime,
            ]
        ];
    }

    private function updateIndex(): void {
        $userId = $this->getDomainHandler()->getUserId();
        $updateType = $this->isNew() ? \Constants::INQUIRY_COLLABORATION_INDEX_TYPE_CREATE : \Constants::INQUIRY_COLLABORATION_INDEX_TYPE_UPDATE;
        SearchQueueService::pushInquiryCollaborationQueue($userId, $this->client_id, [$this->inquiry_collaboration_id], $updateType);
        $this->updateIndexOfProduct();
    }

    private function updateIndexOfProduct(): void {
        $userId = $this->getDomainHandler()->getUserId();
        if(!empty($this->addInquiryProductIds)){
            SearchQueueService::pushInquiryCollaborationProductQueue($userId, $this->client_id, $this->addInquiryProductIds, Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_CREATE);
        }
        if(!empty($this->updateInquiryProductIds)){
            SearchQueueService::pushInquiryCollaborationProductQueue($userId, $this->client_id, $this->updateInquiryProductIds, Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_UPDATE);
        }
        if(!empty($this->deleteInquiryProductIds)){
            SearchQueueService::pushInquiryCollaborationProductQueue($userId, $this->client_id, $this->deleteInquiryProductIds, Constants::INQUIRY_COLLABORATION_PRODUCT_INDEX_TYPE_DELETE);
        }
    }


    private function operationRecordSave(): void {
        $setting = new InquiryCollaborationSetting();
        $setting->setClientId($this->getClientId());
        $setting->setExtraCompareFields($setting->getCustomFieldId());
        $newAttributes = $this->getRawAttributes();
        $historyBuilder = new Builder($setting);
        $historyBuilder->setClientId($this->_clientId)->setType(InquiryCollaborationSetting::TYPE_CREATE)
            ->setUpdateUser($this->getDomainHandler()->getUserId())
            ->initFromRawData($setting->sortAttributes($newAttributes))
            ->build();
    }


    private function scheduleSave(){
        if($this->create_schedule == 1){
            $remindTime = $this->getRemindTime();
            $schedule = new Schedule($this->client_id);
            $schedule->setOperator($this->getDomainHandler()->getUserId());
            $schedule->title               = "询价任务:{$this->inquiry_collaboration_no}";
            $schedule->color               = 1;
            $schedule->participant_user_id = array_unique(array_merge([$this->create_user], $this->handler));
            $schedule->start_time          = $this->create_time;
            $schedule->end_time            = date('Y-m-d 23:59:59', strtotime($remindTime[0]['time']));
            $schedule->full_day_flag       = Schedule::IS_FULL_DAY;
            $schedule->remind_time         = $remindTime;
            $schedule->relation_mail_id    = 0;
            $schedule->refer_type          = \Constants::TYPE_INQUIRY_COLLABORATION;
            $schedule->refer_id            = $this->inquiry_collaboration_id;
            $schedule->remark              = $this->remark;
            $schedule->attach_list         = [];
            $schedule->image_list          = [];
            $schedule->repeat_id           = 0;
            $schedule->create_type         = Schedule::CREATE_TYPE_PRODUCT_TRANSFER;
            $schedule->create_refer        = $this->inquiry_collaboration_id;
            $schedule->save();
        }
    }

    public function canCreate() {
        if (!Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CREATE)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }
    }

    public function canView($scene = Invoice::SYSTEM_MODE_CRM)
    {
        if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
            return true;
        }

//        if ($this->inquiry_collaboration_id && $scene == APIConstant::SCENE_APPROVAL) {
//            $approvalIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id), 'approver');
//            if (in_array($this->getDomainHandler()->getUserId(), $approvalIds)) {
//                // NOTE: 审批人允许查看
//                return;
//            }
//        }
//        if (!Helper::hasPermission($this->getClientId(),$this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_VIEW)) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
//        }

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_VIEW)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }
    }

    public function canChangeHandler() {

        if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
            return true;
        }

//        if ($this->inquiry_collaboration_id && $this->isLock($this->inquiry_collaboration_id, \common\library\approval_flow\Constants::ENTITY_TYPE_INQUIRY_COLLABORATION) && $this->isOpenForApprovalFlow()) {
//            $approvalIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'approver');
//            if (in_array($this->getDomainHandler()->getUserId(), $approvalIds)) {
//                // NOTE: 审批人允许编辑
//                return;
//            } else {
//                throw new \RuntimeException(\Yii::t('approvalflow', 'Current object is locked,can\'t be update'));
//            }
//        }
//        if (!Helper::hasPermission($this->getClientId(),$this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_HANDLER)) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
//        }

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_HANDLER)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
        }
    }

    public function canChangeStatus() {

        if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
            return true;
        }

//        if ($this->inquiry_collaboration_id && $this->isLock($this->inquiry_collaboration_id, \common\library\approval_flow\Constants::ENTITY_TYPE_INQUIRY_COLLABORATION) && $this->isOpenForApprovalFlow()) {
//            $approvalIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'approver');
//            if (in_array($this->getDomainHandler()->getUserId(), $approvalIds)) {
//                // NOTE: 审批人允许编辑
//                return;
//            } else {
//                throw new \RuntimeException(\Yii::t('approvalflow', 'Current object is locked,can\'t be update'));
//            }
//        }
//        if (!Helper::hasPermission($this->getClientId(),$this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_STATUS)) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
//        }

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_CHANGE_STATUS)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
        }
    }

    public function canDelete() {
//        if (!Helper::hasPermission($this->getClientId(),$this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_DELETE)) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
//        }

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_DELETE)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_FAIL);
        }

        if ($this->inquiry_collaboration_id && \common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING)) {
            throw new \RuntimeException(\Yii::t('invoice', 'Failed to delete, the inquiry task is still under approval'));
        }
    }

    public function canEdit() {
        if($this->isNew()){
            throw new \RuntimeException(\Yii::t('invoice', 'data not exsit'), ErrorCode::CODE_REQUEST_VALIDATION_FAIL);
        }
        if($this->status == OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH){
            throw new \RuntimeException(\Yii::t('invoice', 'status is finish'), ErrorCode::CODE_REQUEST_VALIDATION_FAIL);
        }

        if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
            return true;
        }

//        if ($this->inquiry_collaboration_id && $this->isLock($this->inquiry_collaboration_id, \common\library\approval_flow\Constants::ENTITY_TYPE_INQUIRY_COLLABORATION) && $this->isOpenForApprovalFlow()) {
//            $approvalIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'approver');
//            if (in_array($this->getDomainHandler()->getUserId(), $approvalIds)) {
//                // NOTE: 审批人允许编辑
//                return;
//            } else {
//                throw new  \RuntimeException(\Yii::t('approvalflow', 'Current object is locked,can\'t be update'));
//            }
//        }
//        if (!Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EDIT)) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
//        }

        if (!\common\library\privilege_v3\Helper::canOperateRecord($this->getClientId(), $this->getDomainHandler()->getUserId(), $this->scope_user_ids, PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EDIT)) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }
    }

    public function canFeedback()
    {
        $user = User::getLoginUser();

        if ($this->status == OmsConstant::PRODUCT_TRANSFER_STATUS_FINISH) {
            throw new \RuntimeException(\Yii::t('invoice', 'status is finish'), ErrorCode::CODE_REQUEST_VALIDATION_FAIL);
        }

        if($this->hasReviewPrivilege() && $this->isCanEditApprover()) {
            return true;
        }

//        if ($this->inquiry_collaboration_id && $this->isLock($this->inquiry_collaboration_id, \common\library\approval_flow\Constants::ENTITY_TYPE_INQUIRY_COLLABORATION) && $this->isOpenForApprovalFlow()) {
//            $approvalIds = array_column(\common\library\approval_flow\Helper::getReferApprovalInfos($this->client_id, $this->inquiry_collaboration_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING), 'approver');
//            if (in_array($this->getDomainHandler()->getUserId(), $approvalIds)) {
//                // NOTE: 审批人允许编辑
//                return;
//            } else {
//                throw new \RuntimeException(\Yii::t('approvalflow', 'Current object is locked,can\'t be update'));
//            }
//        }
//        if (
//            !Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_EDIT)
//             !Helper::hasPermission($this->getClientId(), $this->getDomainHandler()->getUserId(), PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_ADOPTION)
//        ) {
//            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_MANAGE);
//        }

        $userId = $this->getDomainHandler()->getUserId();
        $clientId = $this->getClientId();

        $hasFeedbackPerm = Helper::hasPermission(
            $clientId,
            $userId,
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_FEEDBACK_ADOPTION
        );

        $hasCollaborationPerm = Helper::hasPermission(
            $clientId,
            $userId,
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_ADOPTION
        );

        $inquiry_view_user_ids = PrivilegeHelper::getPermissionScopeUser($user->getClientId(), $user->getUserId(),
            PrivilegeConstants::PRIVILEGE_CRM_INQUIRY_COLLABORATION_VIEW);

        $canOnlyView = \common\library\oms\inquiry_feedback\Helper::checkCanView($hasCollaborationPerm, $inquiry_view_user_ids, $this->create_user, $this->handler);
        $shouldAllowAccess = $hasFeedbackPerm || $canOnlyView;

        if (!$shouldAllowAccess) {
            throw new \RuntimeException(\Yii::t('privilege', 'Missing permission'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_MANAGE);
        }
    }

    public  function checkNoRules($inquiry_collaboration_no) {
        if ($inquiry_collaboration_no) {
            if (mb_strlen($inquiry_collaboration_no) > 100) {
                throw new \RuntimeException(\Yii::t('invoice', '编号长度不能超过100'), \ErrorCode::CODE_OMS_NO_TO_LONG);
            }
            if ($this->checkExistForNo($inquiry_collaboration_no)) {
                throw new \RuntimeException(\Yii::t('invoice', '编号已存在'), \ErrorCode::CODE_OMS_NO_EXIST);
            }
        }
    }

    public function checkExistForNo($inquiryCollaborationNo) {
        try {
            $inquiryCollaboration = new InquiryCollaboration($this->_clientId);
            $inquiryCollaboration->loadByNo($inquiryCollaborationNo);
            return $inquiryCollaboration->isNew() ? false : ($this->inquiry_collaboration_id != $inquiryCollaboration->inquiry_collaboration_id);
        } catch (\RuntimeException) {
            return false;
        }
    }


    public function loadByNo($inquiryCollaborationNo) {
            $this->load(['client_id' => $this->_clientId, 'inquiry_collaboration_no' => $inquiryCollaborationNo, 'enable_flag' => 1]);
    }

    private function refreshProductList()
    {
        if (is_null($this->record_list)) {
            return;
        }

        $addProduct = [];
        $updateProduct = [];

        $paramProductIds = array_filter(array_column($this->record_list, 'inquiry_product_id'));
        $filter = new InquiryCollaborationProductFilter($this->client_id);
        $filter->inquiry_collaboration_id = $this->inquiry_collaboration_id;
        $filter->enable_flag = 1;
        $filter->selectAll();
        $dbProductList = $filter->rawData();
        $dbProductIdMap = array_column($dbProductList, null, 'inquiry_product_id');
        $this->deleteInquiryProductIds = array_diff(array_keys($dbProductIdMap), $paramProductIds);
        $this->updateInquiryProductIds = array_intersect($paramProductIds, array_keys($dbProductIdMap));

        $product_total_amount = $product_total_count = 0;
        $sort = 1;
        foreach ($this->record_list as &$product) {
            $product['sort'] = $sort++;
            $product['client_id'] = $product['client_id']??$this->client_id;
            $product['enable_flag'] =  $product['enable_flag']??Constants::ENABLE_FLAG_TRUE;
            $boolProduct = is_numeric($product['product_id']) && $product['product_id'] > 0;
            $boolSku = is_numeric($product['sku_id']) && $product['sku_id'] > 0;
            if($boolProduct || $boolSku ){
                $product['local_product'] =  Constants::LOCAL_PRODUCT_TRUE;
            }else{
                $product['local_product'] =  Constants::LOCAL_FLAG_FALSE;
            }
            $product['product_no'] = mb_substr($product['product_no'], 0, 40);
            $product['sku_attributes'] =  empty($product['sku_attributes']) ? '{}':$product['sku_attributes'];
            $product['delivery_date'] =  empty($product['delivery_date']) ? '1970-01-01':$product['delivery_date'];
            $product['price_validity_date'] = empty($product['price_validity_date']) ? '1970-01-01':$product['price_validity_date'];
            $product['inquiry_collaboration_id'] = $this->inquiry_collaboration_id;
            $product['update_user'] = $this->getDomainHandler()->getUserId();
            $product['update_time'] = xm_function_now();
            //前端传的空字符串 要转NULL处理 数据库会把空字符串转成0
            foreach (self::NULL_NOT_DEFAULT_FILED as $field) {
                if(key_exists($field,$product) && empty($product[$field]) && !is_numeric($product[$field])){
                    $product[$field] = null;
                }
            }
            if(is_numeric($product['purchase_quote'])){
                $product['purchase_quote_usd'] = ($this->exchange_rate_usd / 100) * $product['purchase_quote'];
            }else{
                $product['purchase_quote_usd'] = null;
            }
            $product['product_images'] = empty($product['product_images']) ? array():$product['product_images'];
            if(is_numeric($product['amount'])){
                $product['amount_usd'] = $product['amount'] * ($this->exchange_rate_usd/100);
                $product['amount_rmb'] = $product['amount'] * ($this->exchange_rate_cny/100);
            }else{
                $product['amount_usd'] = 0;
                $product['amount_rmb'] = 0;
            }
            if (empty($product['inquiry_product_id'])) {
                $product['inquiry_product_id'] = intval(\ProjectActiveRecord::produceAutoIncrementId());
                $product['create_user'] = $this->getDomainHandler()->getUserId();
                $product['create_time'] =  xm_function_now();
                $addProduct[] = $product;
            }
            if (array_key_exists($product['inquiry_product_id'], $dbProductIdMap)) {
                $updateProduct[] = $product;
            }
            if (is_numeric($product['count'])) {
                $product_total_count += $product['count'];
            }
            if (is_numeric($product['amount'])) {
                $product_total_amount += $product['amount'];
            }
        }
        $this->product_total_amount = $product_total_amount;
        $this->product_total_amount_rmb = $product_total_amount * ($this->exchange_rate / 100);
        $this->product_total_amount_usd = $product_total_amount * ($this->exchange_rate_usd / 100);
        $this->product_total_count = $product_total_count;

        $this->addInquiryProductIds = array_column($addProduct, 'inquiry_product_id');
        if (!empty($addProduct)) {
            $inquiryCollaborationProduct = new  BatchInquiryCollaborationProduct($this->_clientId);
            $inquiryCollaborationProduct->setDomainHandler($this->getDomainHandler());
            $inquiryCollaborationProduct->initFromData($addProduct);
            $this->batchCreateInquiryCollaborationProduct = $inquiryCollaborationProduct;
        }

        if (!empty($updateProduct)) {
            $inquiryCollaborationProduct = new  BatchInquiryCollaborationProduct($this->_clientId);
            $inquiryCollaborationProduct->setDomainHandler($this->getDomainHandler());
            $inquiryCollaborationProduct->initFromData($updateProduct);
            $inquiryCollaborationProduct->setInquiryCollaboration($this);
            $inquiryCollaborationProduct->beforeUpdate();
            $this->batchUpdateInquiryCollaborationProduct = $inquiryCollaborationProduct;
        }

        if (!empty($this->deleteInquiryProductIds)) {
            $deleteProducts = [];
            foreach ($dbProductList as $dbProduct) {
                if (in_array($dbProduct['inquiry_product_id'], $this->deleteInquiryProductIds)) {
                    $deleteProducts[] = $dbProduct;
                }
            }
            $inquiryCollaborationProduct = new  BatchInquiryCollaborationProduct($this->_clientId);
            $inquiryCollaborationProduct->setDomainHandler($this->getDomainHandler());
            $inquiryCollaborationProduct->initFromData($deleteProducts);
            $this->batchDeleteInquiryCollaborationProduct = $inquiryCollaborationProduct;
        }
    }

    public function loadRecordList()
    {
        if ($this->isNew()) {
            throw new \RuntimeException("new object not support this method");
        }
        $filter = new InquiryCollaborationProductFilter($this->getClientId());
        $filter->enable_flag = BaseObject::ENABLE_FLAG_TRUE;
        $filter->inquiry_collaboration_id = $this->getObjectId();
        $batchOrderProduct = $filter->find();
        $batchOrderProduct->getAttributes();
        return $this->record_list = $batchOrderProduct->getRawAttributes();
    }
}
