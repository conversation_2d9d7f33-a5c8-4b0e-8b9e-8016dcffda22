<?php
/**
 *
 * Author: ruisenlin
 * Date: 2022/2/27
 */


namespace common\library\chat_assistant;

use common\library\chat_assistant\action_record\ActionRecordAPI;
use common\library\chat_assistant\identity\BatchIdentity;
use common\library\chat_assistant\identity\IdentityAPI;
use common\library\chat_assistant\identity\IdentityFilter;
use common\library\chat_assistant\identity_external\BatchIdentityExternal;
use common\library\chat_assistant\identity_external\IdentityExternalFilter;
use common\library\email_identity\EmailIdentity;
use common\library\util\PgsqlUtil;
use common\library\util\RedLock;
use common\library\validation\Validator;
use xiaoman\orm\database\data\In;

class Helper
{

    /**
     *
     * 获取已使用文档内容
     *
     * @param $clientId
     * @param $userId
     * @param $referType
     * @param $referIds
     * @param array $actionType
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public static function getUsedTradeDocData($clientId, $userId, $referType, $referIds, array $actionType)
    {
        $api = new ActionRecordAPI($clientId, $userId);
        $result = $api->getTradeDocByRefer($referType, $referIds, $actionType);

        // 通过一个identityId获取对应的文档
        $identityId = 0;
        !empty($result) && $identityId = $result[0]['identity_id'] ?? 0;
        $map = [];
        foreach($result as $item)
        {
            if ($item['identity_id'] == $identityId) {
                $map[$item['refer_id']][] = $item;
            }
        }
        return $map;
    }


    /**
     *
     * 批量插入邮件身份信息
     *
     * @param $clientId
     * @param $userId
     * @param $emailList
     * @return array
     * @throws \xiaoman\orm\exception\OrmException
     * @throws \xiaoman\orm\exception\QueryException
     */
    public static function insertBatchEmailToIdentity($clientId, $userId, $emailList)
    {

        $retryInsertRows = [];
        $existedEmailList = [];

        // 批量插入邮箱身份
        $insertRows = self::batchInsertMail($clientId, $emailList);

        // 获取插入失败的邮箱
        $retryEmailList = array_diff($emailList, array_column($insertRows, 'email'));

        if (!empty($retryEmailList)) {
            // 重新查询插入失败邮箱是否已经入库
            $identityAPI = new IdentityAPI($clientId, $userId);
            $existedEmailList = $identityAPI->getIdentityList(Constants::PLATFORM_EMAIL, $retryEmailList)['list'];
            $existedEmails = array_column($existedEmailList, 'email');

            // 获取重试邮箱中还没入库的邮箱
            $noExistEmails = array_diff($retryEmailList, $existedEmails);

            // 再次批量插入未入库的邮箱
            if (!empty($noExistEmails)) {
                $retryInsertRows = self::batchInsertMail($clientId, $noExistEmails);
            }
        }

        // 合并 首次插入成功-首次插入失败但已经入库-重新插入 的数据
        return array_merge($insertRows, $existedEmailList, $retryInsertRows);

    }


    /**
     * 生成身份验证加锁 key
     *
     * @param $key
     * @return string
     */
    public static function generateIdentityLockKey($key)
    {
        return sprintf('identity_%s_lock', $key);
    }


    /**
     *
     * 批量插入邮箱到 tbl_platform_identity
     *
     * @param $clientId
     * @param $emailList
     * @return array|\CDbDataReader|mixed
     * @throws \CDbException
     * @throws \CException
     * @throws \ProcessException
     */
    public static function batchInsertMail($clientId, $emailList)
    {

        if (empty($emailList)) {
            return [];
        }

        $lockerList = [];
        $insertRows = [];

        try{
            $redis = \RedisService::cache();
            $insertSql = "insert into tbl_platform_identity (identity_id, client_id, email, create_time, update_time) values ";
            $date = date('Y-m-d H:i:s');
            foreach ($emailList as $email) {
                $locker = new RedLock($redis, self::generateIdentityLockKey($clientId.$email));
                if (!$locker->lock(1)) {
                    continue;
                }
                $lockerList[] = $locker;
                $identityId = \ProjectActiveRecord::produceAutoIncrementId();
                $valueParams[] = "($identityId, $clientId, '$email', '$date', '$date')";

            }
            if (!empty($valueParams)) {
                $valueParamsStr = implode(',', $valueParams);
                $returningSql = " returning identity_id, email ";
                $insertSql = $insertSql . $valueParamsStr . $returningSql;

                $db = \PgActiveRecord::getDbByClientId($clientId);

                $insertRows = $db->createCommand($insertSql)->queryAll();

            }

        } finally {
            foreach ($lockerList as $locker) {
                $locker->unlock();
            }
        }

        return $insertRows;

    }

    public static function batchCreateIdentity(int $clientId, int $userId, array $contactList) {
        if (empty($contactList)) return 0;
        (new Validator(['contact_list' => $contactList], [
            'contact_list.*.nickname' => 'string',
            'contact_list.*.account' => 'required',
            'contact_list.*.company_name' => 'string',
        ]))->validate();

        extract(\common\library\auto_market\Helper::getContactsByType($contactList));

        $identityApi = new IdentityAPI($clientId, $userId);
        $existEmails = $identityApi->findBatchIdentity(Constants::PLATFORM_EMAIL, array_column($emails, 'account'));
        $existWhatsapp = $identityApi->findBatchIdentity(Constants::PLATFORM_WHATSAPP, array_column($phones, 'account'));

        $contactMap = array_column($contactList, null, 'account');
        $saveIdentity = [];
        $time = xm_function_now();
        $saveEmails = [];
        if (!empty($existEmails)) {
            foreach ($existEmails as $identityId => $email) {
                $saveEmails[] = [
                    'client_id' => $clientId,
                    'user_id' => $userId,
                    'identity_id' => $identityId,
                    'nickname' => $contactMap[$email]['nickname']??'',
                    'company_name' => $contactMap[$email]['company_name']??'',
                ];
            }

            $emails = array_filter($emails, function($contact) use ($existEmails) {
                return !in_array($contact['account'], $existEmails);
            });
        }
        foreach ($emails as $contact) {
            $saveIdentity[] = [
                'client_id' => $clientId,
                'email' => $contact['account'],
                'whatsapp_id' => '',
                'create_time' => $time,
                'update_time' => $time
            ];
        }

        $savePhones = [];
        if (!empty($existWhatsapp)) {
            foreach ($existWhatsapp as $identityId => $whatsappId) {
                $savePhones[] = [
                    'client_id' => $clientId,
                    'user_id' => $userId,
                    'identity_id' => $identityId,
                    'nickname' => $contactMap[$whatsappId]['nickname']??'',
                    'company_name' => $contactMap[$whatsappId]['company_name']??'',
                ];
            }

            $phones = array_filter($phones, function ($contact) use ($existWhatsapp) {
                return !in_array($contact['account'], $existWhatsapp);
            });
        }

        foreach ($phones as $contact) {
            $saveIdentity[] = [
                'client_id' => $clientId,
                'email' => '',
                'whatsapp_id' => $contact['account'],
                'create_time' => $time,
                'update_time' => $time,
            ];
        }

        $saveExternals = array_merge($savePhones, $saveEmails);
        $batchIdExternal = new BatchIdentityExternal($clientId);
        if (!empty($saveExternals)) {
            $batchIdExternal->initFromData($saveExternals);
            $successCount = $batchIdExternal->getOperator()->batchUpdate(['client_id', 'user_id', 'identity_id', 'nickname', 'company_name']);
        }

        if (empty($saveIdentity)) {
            return $successCount??0;
        }

        $batchIdentity = new BatchIdentity($clientId);
        $batchIdentity->initFromData($saveIdentity);
        $batchIdentity->getOperator()->batchCreate(['client_id', 'email', 'whatsapp_id', 'create_time', 'update_time']);
        $identities = $batchIdentity->getListAttributes(['identity_id', 'email', 'whatsapp_id']);

        $saveExternals = [];
        foreach ($identities as $identity) {
            $identityId = $identity['identity_id'];
            $email = $identity['email'];
            $whatsappId = $identity['whatsapp_id'];
            $account = $email ? : $whatsappId;
            $saveExternals[] = [
                'client_id' => $clientId,
                'user_id' => $userId,
                'identity_id' => $identityId,
                'nickname' => $contactMap[$account]['nickname']??'',
                'company_name' => $contactMap[$account]['company_name']??'',
                'contact_status' => 0,
                'contact_count' => 0,
                'last_contact_time' => '1970-01-01 00:00:00',
                'create_time' => $time,
                'update_time' => $time
            ];
        }
        $batchIdExternal->initFromData($saveExternals);
        return $batchIdExternal->getOperator()->batchCreate(['client_id', 'user_id', 'identity_id','nickname','company_name', 'contact_status','contact_count','last_contact_time','create_time','update_time']);
    }

    /**
     * 失败的联系人不更新状态（无法区分联系人号码邮箱异常还是渠道失败导致的发送失败）
    */
    public static function getUpdateContactStatus($oldContactStatus, $newContactStatus) {
        if ($newContactStatus == Constants::CONTACT_STATUS_CONTACT_FAILED) {
            return $oldContactStatus;
        }

        return max($oldContactStatus, $newContactStatus);
    }

    public static function getContactStatus($clientId, $userId, $facebook_page_user_id = 0, $instagram_user_id = 0): array
    {
        $identityFilter = new IdentityFilter($clientId);
        if ($facebook_page_user_id) {
            $identityFilter->facebook_page_user_id = new In($facebook_page_user_id);
        }

        if ($instagram_user_id) {
            $identityFilter->instagram_user_id = new In($instagram_user_id);
        }

        $identityFilter->select(['facebook_page_user_id', 'instagram_user_id']);

        $identityExternalFilter = new IdentityExternalFilter($clientId);
        $identityExternalFilter->user_id = $userId;
        $identityExternalFilter->select(['contact_status']);

        $joinFilter = $identityFilter->initJoin();
        $joinFilter = $joinFilter->innerJoin($identityExternalFilter)->on('identity_id', 'identity_id');
        return $joinFilter->rawData();
    }

    public static function getCardTypeByWhatsapp($userId, $whatsapp)
    {
        // 定义优先级顺序
        $priorityOrder = [
            EmailIdentity::CARD_TYPE_COMPANY,
            EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY,
            EmailIdentity::CARD_TYPE_PUBLIC_COMPANY,
            EmailIdentity::CARD_TYPE_LEAD,
            EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD
        ];

        $cardType = [EmailIdentity::CARD_TYPE_STRANGER];

        $companyList = new \common\library\customer\CompanyList($userId);
        $companyList->setKeyword($whatsapp);
        $companyList->setSearchFields([
            'customer_list.contact.value.whatsapp',
        ]);
        $companyList->setSkipPrivilege(true);
        $companyList->setFields(['company_id', 'user_id']);
        $companyListData = $companyList->find();

        foreach($companyListData as $company){
            $userIds = PgsqlUtil::trimArray($company['user_id']);

            if(!empty($userIds)){
                $cardType[] = in_array($userId, $userIds) ? EmailIdentity::CARD_TYPE_COMPANY : EmailIdentity::CARD_TYPE_CAN_NOT_MANAGE_COMPANY;
            }else{
                $cardType[] = EmailIdentity::CARD_TYPE_PUBLIC_COMPANY;
            }
        }

        $leadList = new \common\library\lead\LeadList($userId);
        $leadList->setKeyword($whatsapp);
        $leadList->setSearchFields([
            'customer_list.contact.value.tel',
        ]);
        $leadList->setSkipPrivilege(true);
        $leadList->setFields(['lead_id', 'user_id']);
        $leadListData = $leadList->find();

        foreach($leadListData as $lead){
            $userIds = PgsqlUtil::trimArray($lead['user_id']);

            if(!empty($userIds)){
                $cardType[] = in_array($userId, $userIds) ? EmailIdentity::CARD_TYPE_LEAD : EmailIdentity::CARD_TYPE_COLLEAGUE_LEAD;
            }

        }
        // 按优先级顺序返回第一个匹配的类型
        foreach ($priorityOrder as $type) {
            if (in_array($type, $cardType)) {
                return $type;
            }
        }
        // 如果没有匹配任何高优先级类型，返回陌生人类型
        return EmailIdentity::CARD_TYPE_STRANGER;

    }
}