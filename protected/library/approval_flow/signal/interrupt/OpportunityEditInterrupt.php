<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 2020/5/21
 * Time: 5:50 PM
 */

namespace common\library\approval_flow\signal\interrupt;


use common\library\approval_flow\Constants;
use common\library\approval_flow\signal\SignalConstants;

class OpportunityEditInterrupt extends AbstractInterrupt
{
    public function entity()
    {
        return Constants::ENTITY_TYPE_OPPORTUNITY;
    }

    public function event()
    {
        return Constants::ENTITY_EVENT_UPDATE;
    }

    public function origin()
    {
        return  SignalConstants::SIGNAL_ORIGIN_OPPORTUNITY_EDIT;
    }
}