<?php
namespace common\library\approval_flow\signal\interrupt;

use common\library\approval_flow\Constants;
use common\library\approval_flow\signal\SignalConstants;

class OrderV2ChangeStatusInterrupt extends AbstractInterrupt
{

    protected $attributes;
    protected $oldAttributes;

    public function __construct(
        int $user_id,
        int $refer_id,
        int $refer_type,
            $refer_name,
            $attributes = [],
        array $diff = [],
        array $wakeup_params = []
    ) {
        $this->attributes = $attributes['new']??[];
        $this->oldAttributes = $attributes['old']??[];
        parent::__construct($user_id, $refer_id, $refer_type, $refer_name, $diff, $wakeup_params);
    }


    public function entity()
    {
        return Constants::ENTITY_TYPE_ORDER;
    }

    public function event()
    {
        return Constants::ENTITY_EVENT_STATUS;
    }

    public function origin()
    {
        return  SignalConstants::SIGNAL_ORIGIN_ORDER_CHANGE_STATUS;
    }

    public function attributes()
    {
        return $this->attributes;
    }

    public function oldAttributes()
    {
        return $this->oldAttributes;
    }

    /**
     * @param mixed $attributes
     */
    public function setAttributes($attributes): void
    {
        $this->attributes = $attributes;
    }

    /**
     * @param mixed $oldAttributes
     */
    public function setOldAttributes($oldAttributes): void
    {
        $this->oldAttributes = $oldAttributes;
    }
}