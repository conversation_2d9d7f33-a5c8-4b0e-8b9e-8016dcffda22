<?php
$trace = $trace ?? debug_backtrace();
$client_id = $client_id ?? '';
$user_id = $user_id ?? '';
$email = $email ?? '';
$db_name = $db_name ?? '';
$mysql_set_id = $mysql_set_id ?? '';
$mongo_set_id = $mongo_set_id ?? '';
$pgsql_set_id = $pgsql_set_id ?? '';
$code = $code ?? '';
$message = $message ?? '';
$file = $file ?? '';
$line = $line ?? '';
$extra_info = $extra_info ?? '';
$request = $request ?? [];
$env = $env ?? [];
$cookie = $cookie ?? [];
$md5 = $md5 ?? null;
$count = $count ?? 0;
$gap = $gap ?? 0;
$time = $time ?? 'unknown';

$param['summary_md5'] = $md5 ?? '';
$param['system'] = $system ?? '';
$param['search_day'] = $day ?? '';
$baseUrl = Yii::app()->params['report']['error']['error_detail_url'];
$url = $baseUrl . '?' . http_build_query($param);
?>

<pre>
error code <?php echo $code."\n";?>
message <?php echo $message."\n";?>
in <?php echo "$file($line)\n"?>

<?php if (!empty($extra_info)):?>
extra info: <?php echo "$extra_info\n";?>
<?php endif;?>

<?php //if ($count > 1):?>
该错误在最近<?php echo $gap ?>秒内被触发了<a href="<?php echo $url;?>" target="_blank"><?php echo $count ?>次</a>，最近一次触发时间<?php echo $time;?>
<?php //endif; ?>

---------user----------
client_id <?php echo $client_id . "\n"; ?>
user_id <?php echo $user_id . "\n"; ?>
email <?php echo $email . "\n"; ?>
mysql_set_id <?php echo $mysql_set_id . "\n"; ?>
pgsql_set_id <?php echo $pgsql_set_id . "\n"; ?>
db_name <?php echo $db_name . "\n"; ?>

---------trace----------
<?php if (!is_array($trace)): ?>
internal error: $trace is not an array.
$trace value:
<?php var_dump($trace); ?>
<?php else: ?>
<?php for ($i = 0; $i < count($trace); ++$i):?>
    <?php
    //var_dump($trace[$i]);
    $file = $trace[$i]['file'] ?? 'unknown';
    $line = $trace[$i]['line'] ?? 0;
    $function = $trace[$i]['function'] ?? 'unknown';
    //$object = isset($trace[$i]['object']) ? get_class($trace[$i]['object']) : '';
    $type = $trace[$i]['type'] ?? '';
    $class = $trace[$i]['class'] ?? '';
    $args = $trace[$i]['args'] ?? [];

    $argList = [];
    foreach ($args as $arg)
    {
        if (is_object($arg))
            $argList[] = get_class($arg);
        else if (is_array($arg))
        {
            $array = [];
            $simple = 1;
            foreach ($arg as $key=>$value)
            {
                if (is_object($value))
                {
                    $simple = 0;
                    $array[$key] = get_class($value);
                }
                else if (is_array($value))
                {
                    $simple = 0;
                    $array[$key] = 'array';
                }
                else if (is_string($value))
                    $array[$key] = "\"$value\"";
            }

            $argList[] = $simple ? json_encode($arg) : json_encode($array);
        }
        else if (is_string($arg))
            $argList[] = "\"$arg\"";
        else
            $argList[] = $arg;
    }
    $args = implode(', ', $argList);

    echo "#$i $file($line): {$class}{$type}{$function}($args)\n";
    ?>
<?php endfor;?>
<?php endif;?>

---------request----------
<?php foreach ($request as $key=>$value): ?>
    <?php  $value = print_r($value, true);echo "[$key] => $value\n";?>
<?php endforeach;?>

---------env----------
<?php foreach ($env as $key=>$value): ?>
    <?php  $value = print_r($value, true);echo "[$key] => $value\n";?>
<?php endforeach;?>

---------cookies----------
<?php foreach ($cookie as $key=>$value): ?>
    <?php echo "[$key] => $value\n";?>
<?php endforeach;?>
</pre>
