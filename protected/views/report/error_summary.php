<?php
$summary = $summary ?? [];

$baseUrl = Yii::app()->params['report']['error']['error_detail_url'];
?>

<pre>
<?php foreach ($summary as $elem): ?>
code: <?php echo $elem['error_code']."\n";?>
message: <?php echo $elem['message']."\n";?>
in <?php echo "{$elem['file']}({$elem['line']})\n"?>
<?php if (!empty($elem['extra_info'])):?>
extra info: <?php echo "{$elem['extra_info']}\n";?>
<?php endif;?>

本日发生<?php echo $elem['count'];?>次，最近一次发生时间<?php echo $elem['happened_time'] . "\n";?>
    <?php
    $param['summary_md5'] = $elem['summary_md5'];
    $param['system'] = $elem['system'];
    $param['search_day'] = $elem['day'];
    ?>
    详情 <a target="_blank"><?php echo $baseUrl . '?' . http_build_query($param); ?></a>

--------------------------------------

<?php endforeach;?>
</pre>
