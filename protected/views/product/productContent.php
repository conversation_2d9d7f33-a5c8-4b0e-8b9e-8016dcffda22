<style>
    * {
        padding: 0;
        margin: 0;
    }

    li {
        list-style: none;
    }
    * {
        padding: 0;
        margin: 0;
    }

    .ellipsis {
        min-width: 1em;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    li {
        list-style: none;
    }
    .page-product-pdf {
        width: 768px;
        margin: 0 auto;
    }

    .product-base-info {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        padding: 28px 0;
        background: #fff;
    }
    .product-base-info .image-box {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        margin: 0 110px 0 28px;
    }
    .product-base-info .image-box .main-product-image {
        width: 260px;
        height: 260px;
        text-align: center;
    }
    .product-base-info .image-box .image-thu-list {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        margin-left: 12px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }
    .product-base-info .image-box .image-thu-list li {
        width: 46px;
        height: 46px;
        cursor: pointer;
        border: 1px solid #ccc;
    }
    .product-base-info .image-box .image-thu-list li:nth-child(n + 2) {
        margin-top: 7px;
    }
    .product-base-info .image-box .image-thu-list li.active {
        border: 1px solid #188ae8;
    }
    .product-base-info .base-info-box {
        font-size: 12px;
        line-height: 30px;
    }
    .product-base-info .base-info-box .field-name {
        margin-right: 24px;
        color: rgba(0, 0, 0, .54);
    }
    .product-base-info .base-info-box .field-value {
        color: rgba(0, 0, 0, .87);
        word-wrap: break-word;
        word-break: break-all;
    }

    .product-tab-detail {
        padding: 28px;
    }
    .product-tab-detail .product-field-group:nth-child(n + 2) {
        margin-top: 40px;
    }
    .product-tab-detail .field-lsit {
        line-height: 2.4em;
    }
    .product-tab-detail .field-lsit .field-item {
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex;
        width: 49%;
        font-size: 12px;
        -webkit-box-align: flex-start;

        -ms-flex-align: flex-start;

        align-items: flex-start;
    }

    .product-tab-detail .field-lsit .fields-long {
        width: 100%;
    }

    .product-tab-detail .field-lsit .field-item .field-name {
        width: 88px;
        min-width: 88px;
        margin-right: 24px;
        color: rgba(0, 0, 0, .54);
        display: inline-block;
        vertical-align: top;
    }
    .product-tab-detail .field-lsit .field-item .field-value {
        display: inline-block;
        color: rgba(0, 0, 0, .87);
    }

    .product-tab-detail .group-title {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;

        -webkit-box-align: center;

        -ms-flex-align: center;

        align-items: center;
    }
    .product-tab-detail .group-title .symbol {
        display: inline-block;
        width: 4px;
        margin-right: 8px;
        background-color: #188ae8;

        -ms-flex-item-align: stretch;

        -ms-grid-row-align: stretch;

        align-self: stretch;
    }

    .image-thu-list .x-center {
        margin-left: 50%;
        margin-top: 50%;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }

    @media print {
        @page {
            margin: 0 0 0 0;
        }
    }
</style>

<?php
    function myStrCut($oldStr, $len = 45) {
        $newStr = '';
        $strLen = mb_strlen($oldStr);
        if($strLen > $len) {
            $strRow = ceil($strLen / $len);
            for($idx = 1; $idx <= $strRow; $idx++) {
                $start = ($idx - 1) * $len;
                $newRow = mb_strcut($oldStr, $start, $len);
                $newStr .= $newRow."<br>";
            }
            return $newStr;
        } else {
            return $oldStr;
        }
    }
?>


<div class="page-product-pdf">
    <div class="product-base-info">
        <div class="image-box">
            <div class="main-product-image">
                <img alt="" src="<?= $images[0]['src']??'/static/images/project/default.png' ?>?x-oss-process=image/resize,m_lfit,h_260,w_260">
            </div>
            <ul class="image-thu-list">
                <?php for ($i = 0; $i < count($images); $i++): ?>
                    <li>
                        <img alt="" src="<?= $images[$i]['src']??'/static/images/project/default.png' ?>?x-oss-process=image/resize,m_lfit,h_46,w_46">
                    </li>
                <?php endfor; ?>
            </ul>
        </div>

        <ul class="base-info-box">
            <?php for ($i = 0; $i < count($base_info); $i++): ?>
                <li class="base-info-field">
                    <span class="field-name"><?= $base_info[$i]['name'] ?></span>
                    <span class="field-value"><?= is_array($base_info[$i]['value']) ? implode(', ', $base_info[$i]['value']) : myStrCut($base_info[$i]['value']) ?></span>
                </li>
            <?php endfor; ?>
        </ul>
    </div>

    <div class="product-tab-detail">
        <?php for ($i = 0; $i < count($groups_info); $i++): ?>
            <section class="product-field-group">
                <h4 class="group-title"><i class="symbol"></i><?= $groups_info[$i]['name'] ?></h4>
                <ul class="field-lsit">
                    <?php for ($k = 0; $k < count($groups_info[$i]['fields']); $k++): ?>
                        <li class="field-item <?= $groups_info[$i]['fields'][$k]['field_type'] == '2' ? 'fields-long' : '' ?>">
                            <span class="field-name">
                                <span style="padding-top: 12px; line-height: 1.2; display: block;"><?= $groups_info[$i]['fields'][$k]['name']  ?></span>
                            </span>
                            <span class="field-value"><?= $groups_info[$i]['fields'][$k]['value'] ?></span>
                        </li>
                    <?php endfor; ?>
                </ul>
            </section>
        <?php endfor; ?>
    </div>
</div>