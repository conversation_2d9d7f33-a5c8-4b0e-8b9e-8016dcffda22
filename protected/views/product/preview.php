<html>
<head>
  <meta name="renderer" content="webkit|ie-stand|ie-comp" />
  <meta content="width=device-width,initial-scale=1.0,user-scalable=no" name="viewport" />
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="keywords" content="" />
  <meta name="description" content="" />

<title>小满销售管理 - 查看产品</title>

<style>
body {
	overflow-y: scroll!important;
	-webkit-overflow-scrolling: touch;
	font-family: -apple-system,"Helvetica Neue",Arial,"PingFang SC","Hiragino Sans GB",STHeiti,"Microsoft YaHei","Microsoft JhengHei","Source Han Sans SC","Noto Sans CJK SC","Source Han Sans CN","Noto Sans SC","Source Han Sans TC","Noto Sans CJK TC","WenQuanYi Micro Hei",SimSun,sans-serif;
	background: #ececec;
}

h1 {
	margin: .67em 0;
	font-size: 2em;
	font-weight: 700
}

h2 {
	margin: .83em 0;
	font-size: 1.5em;
	font-weight: 700
}

h3 {
	margin: 1.17em 0;
	font-size: 1.17em;
	font-weight: 700
}

h4 {
	margin: 1.33em 0;
	font-weight: 700
}

h5 {
	margin: 1.67em 0;
	margin: 2.33em 0;
	font-size: .83em;
	font-size: .67em;
	font-weight: 700;
	font-weight: 700
}

ol,ul {
	padding: 15px;
	margin: 0
}

ul li {
	list-style-position: inside;
	list-style-type: none
}

.main-container {
	margin: 80px auto 24px;
	max-width: 1160px;
	background: #fefefe
}

.clearfix:before {
	display: table;
	content: " "
}

.clearfix:after {
	display: table;
	clear: both;
	content: " "
}

.ellipsis {
	min-width: 1em;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}

.product-info {
	color: #666
}

.product-info .c-content-title {
	text-indent: 20px;
	border-bottom: 1px solid #e5e5e5
}

.product-info .c-content-title>div {
	float: left;
	width: 640px
}

.product-info .c-content-title span {
	padding-left: 4px;
	font-size: 12px
}

.product-info .c-content-title .light {
	color: silver
}

.product-info .c-box-main {
	padding: 15px 20px;
	line-height: 25px
}

.product-info .tool-right {
	float: right;
	padding-top: 3px;
	margin-right: 20px
}

.product-info .tool-right li {
	display: inline-block;
	text-indent: 10px
}

.product-info .tool-right li span {
	margin-left: -14px;
	font-size: 16px
}

.product-info .tool-right a:hover {
	display: inline-block;
	color: #262626;
	background: #eee
}

.product-info .product-detail {
	width: 100%;
	box-sizing: border-box;
	padding: 25px 20px 10px
}

.product-info .slider {
	float: left
}

.product-info .slider .main-img {
	width: 320px;
	height: 320px;
	overflow: hidden;
	line-height: 320px;
	text-align: center;
	border: 1px solid #ccc
}

.product-info .main-img img {
	display: inline;
	max-width: 100%;
  max-height: 100%;
  height: auto;
	vertical-align: middle
}

.product-info .basic-info {
	min-height: 260px;
	margin-left: 350px
}

.product-info .statistics {
	margin-left: 350px
}

.product-info .statistics strong {
	font-size: 16px
}

.product-info .statistics li>ul {
	display: inline-block;
	width: 96px;
	padding: 8px;
	text-align: center;
	vertical-align: middle;
	background: #e5e5e5
}

.product-info .basic-info h3 {
	font-size: 16px;
	word-break: break-all;
	word-wrap: break-word
}

.product-info .basic-info .ellipsis_multiple {
	-webkit-line-clamp: 3
}

.product-info .c-list {
	padding-top: 14px;
	line-height: 32px
}

.page-product-online .product-info .c-list {
	padding-top: 14px
}

.product-info .c-list {
	line-height: 32px
}

.page-product-online .product-info .c-list {
	line-height: 23px
}

.product-info .c-list>li {
	margin: 0
}

.product-info .c-list .c-list-label {
	display: inline-block;
	width: 150px;
	margin-right: 8px
}

.product-info .column3 {
	width: 100%;
	box-sizing: border-box
}

.product-info .column3>li {
	float: left;
	width: 32%;
	line-height: 25px
}

.product-info .column3>li:nth-child(3n+2) {
	margin: 0 2%
}

.product-info .url {
	position: relative
}

.product-info .url .c-list-label {
	position: absolute;
	top: 0;
	left: 0
}

.product-info .url .c-list-text {
	width: 100%;
	padding-left: 142px;
	border-sizing: border-box;
	font-size: 12px;
}

.template .product-info footer,.template .product-info header {
	height: 130px
}

.product-info .c-list .c-list-text #copy_url {
	margin-left: 20px
}

.product-info .c-list div {
	display: inline-block
}

.product-info .c-list .tips {
	margin-left: 144px;
	color: #ccc
}

.product-select {
	padding: 8px 32px 24px;
	text-align: left;
	background: #fff
}

.product-select .group-select {
	padding-right: 32px
}

.product-select li {
	overflow: hidden
}

.product-select li .c-cell-main ul {
	overflow: hidden
}

.product-select li .c-imgBox {
	width: 40px;
	height: 40px;
	line-height: 40px
}

.product-select li .c-imgBox img {
	margin-top: -2px
}

.product-select li .c-cell-main li {
	float: left;
	width: 50%
}

.product-select .m-search {
	width: 712px
}

.product-select .m-search .m-input {
	width: 732px
}

.product-select .m-label {
	width: auto;
	padding-top: 24px;
	margin-right: 4px
}

.product-select .c-form-inputItem .m-select {
	width: 116px;
	margin-right: 8px
}

.product-select .group-select,.product-select .type-select {
	display: inline-block
}

.product-select .m-list-item {
	border: none;
	border-bottom: 1px solid #ccc
}

.product-select .m-list-item-head {
	cursor: default
}

.product-select .product-cell-info {
	padding-bottom: 4px;
	font-size: 12px;
	line-height: 20px
}

.product-select .c-cell-left {
	padding-top: 8px;
	padding-right: 16px
}

.product-select .product-cell-name {
	padding-top: 0;
	margin-right: 8px;
	line-height: 32px
}

.product-select .m-list-item-head-choose {
	padding-left: 0;
	line-height: 56px
}

.product-select .product-cell-name {
	float: left;
	max-width: 96%
}

.product-select .c-cell-main>div {
	overflow: hidden;
	line-height: 32px
}

.product-select .c-cell-main>div .product-cell-link {
	display: none
}

.product-select .c-cell-main>div:hover .product-cell-link {
	display: block;
	color: #188ae8
}

.product-select .e-submit {
	margin-top: 24px;
	margin-left: 304px
}

.product_history {
	position: relative;
	margin-left: 80px;
	font-size: 12px;
	border-left: 1px solid #f1f1f1
}

.product_history .m-list {
	min-width: 10px
}

.product_history .m-list:hover .m-list-title {
	background: #6cba11
}

.product_history .m-list-title {
	display: inline-block;
	width: auto;
	padding: 4px 8px;
	margin: 12px 0 12px -36px;
	color: #fefefe;
	background: #9c9c9c;
	border-radius: 7px;
	transform: translateX(-50%)
}

.product_history_more {
	display: inline-block;
	width: auto;
	padding: 4px 8px;
	margin: 12px 0 12px -36px;
	color: #fefefe;
	background: #9c9c9c;
	border-radius: 7px;
	transform: translateX(-50%)
}

.product_history .m-list-content li {
	position: relative;
	margin-bottom: 16px
}

.product_history .m-list-content li:after {
	display: block;
	height: 1px;
	clear: both;
	content: ''
}

.product_history .modification_time {
	position: absolute;
	top: 18px;
	left: -96px;
	width: 48px;
	line-height: 12px;
	text-align: right
}

.product_history .modification_disk {
	position: absolute;
	top: 18px;
	left: -42px;
	display: block;
	width: 12px;
	height: 12px;
	background: #9c9c9c;
	border-radius: 6px
}

.product_history .modification_user {
	float: left;
	max-width: 160px
}

.product_history .modification_user img {
	width: 48px;
	height: 48px;
	margin-right: 8px;
	vertical-align: middle;
	border-radius: 24px
}

.product_history .modification_information {
	float: left;
	width: 498px;
	padding: 0 16px 16px;
	margin-right: 33px;
	margin-left: 15px;
	font-size: 14px
}

.product_history .modification_information.active {
	background: #f3f3f3
}

.product_history .modification_information .title {
	line-height: 48px;
	cursor: pointer
}

.product_history .modification_information .title strong {
	padding-left: 8px;
	color: #333
}

.product_history .modification_information img {
	max-width: 100%;
	vertical-align: middle
}

.product_history .modification_information .content {
	display: none
}

.product_history .modification_information .content h2 {
	margin: 8px 0;
	font-size: 14px;
	line-height: 32px;
	color: #3392e9;
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5
}

.product_history .modification_information .content .content_values {
	display: inline-block;
	max-width: 400px;
	vertical-align: top
}

.product_history .modification_information .content .content_values img {
	max-width: 48px;
	vertical-align: top
}

.product_history .e-site-down {
	position: absolute;
	top: 0;
	right: 0;
	font-size: 24px;
	line-height: 48px;
	vertical-align: middle;
	cursor: pointer
}

.product_history_more {
	margin: 0 0 0 56px;
	cursor: pointer;
	background: #188ae8;
	transform: translate(0)
}

.remark {
	color: #a1a1a1
}

.page-product-info .product-info {
	padding-bottom: 20px
}

.template footer,.template header {
	position: relative;
	background: #333
}

.template footer .m-btn,.template header .m-btn {
	position: absolute;
	top: 50px;
	left: 50%;
	width: 140px;
	margin-left: -70px;
	line-height: 20px;
	color: #fff
}

.product-info .product-show {
	padding: 0 20px 20px
}

.product-info .product-show li {
	margin-bottom: 20px;
	text-align: center
}

.product-info .product-show li img {
	max-width: 100%
}

.c-form-inputItem .m-input,.c-form-inputItem .m-select {
	width: 235px
}

.m-select {
	padding-top: 14px
}

.product-info .email .m-input {
	width: 472px
}

.product-info .contact {
	padding: 10px 0 35px;
	margin-left: 350px;
	border: 1px solid #ebebeb
}

.product-info .contact .m-btn {
	float: right;
	width: 86px;
	height: 25px;
	margin-right: 20px;
	border-radius: 2px
}

.product-info .m-label {
	width: 70px;
	margin-right: 5px;
	text-align: right;
	vertical-align: top
}

.product-info .c-form-textarea textarea {
	width: 459px;
	height: 70px;
	padding: 5px;
	margin-top: 10px;
	background: #fbfbfb;
	border: none
}

.template .btn-group {
	height: 120px
}

.template .btn-group .save {
	top: 45px;
	left: 35%
}

.template .btn-group .preview {
	top: 45px;
	left: 40%;
	background: #6cba11
}

.page-product-online .product-info .basic-info {
	height: 188px
}
</style>

</head>
<body style="height: auto;">
<div class="main-container main-simple" id="main-container" style="min-height: 245px;">
    <div class="page-product-info">


        <div class="c-content product-info">


            <div class="product-export">
                <div class="product-detail clearfix">
                    <div class="slider">
                        <div class="main-img">

                            <?php if(!empty($images) && count($images) > 0){?>
                                <img src="<?=$images[0]['src']?>">
                            <?php } else { ?>
                                <img src="/static/images/project/default.png">
                            <?php } ?>

                        </div>
                    </div>
                    <div class="basic-info">
                        <h3 class="ellipsis_multiple" title="custom running shorts"><strong><?=$name?></strong></h3>
                        <ul class="c-list">

                            <li><span class="c-list-label">FOB Price:</span><span class="c-list-text"><?=$v_price?></span></li>

                            <li><span class="c-list-label">Min.Order.Quantity:</span><span class="c-list-text"><?=$v_quantity?></span></li>

                            <li><span class="c-list-label">Supply Ability:</span><span class="c-list-text"><?=$v_supply?></span></li>

                            <li><span class="c-list-label">Port:</span><span class="c-list-text"><?=$port?></span></li>

                            <li><span class="c-list-label">Payment Terms:</span><span class="c-list-text"><?=$v_payment_terms?></span></li>

                        </ul>
                    </div>
                </div>
                <div class="c-box-list">

                    <div class="c-box-item">
                        <h2 class="c-content-title">
                            <strong>产品信息</strong> <span>Product Info</span>
                        </h2>
                        <ul class="c-box-main column3 clearfix">

                            <li class="ellipsis" title="<?=$v_category_names?>">Product Type:<?=$v_category_names?></li>
                            <?php
								$more_details = isset($info_json['more_details']) ? $info_json['more_details']: $info_json;
                                if(!empty($more_details)){
                                    foreach($more_details as $detail){?>
                                        <li class="ellipsis" title="<?=$detail['value']??''?>"><?=$detail['name']??''?>:<?=$detail['value']??''?></li>
                                    <?php
                                    }
                                }
                            ?>

                        </ul>
                    </div>


                    <div class="c-box-item">
                        <h2 class="c-content-title">
                            <strong>包装和货运</strong><span>Packaging &amp; Delivery</span>
                        </h2>
                        <ul class="c-box-main">
                            <li>Packaging Details: <?=$packaging?></li>
                            <?php if(!empty($delivery_time)){?>
                                <li>Delivery Time: <?=$delivery_time?></li>
                            <?php }?>
                        </ul>
                    </div>


                    <div class="c-box-item">
                        <h2 class="c-content-title">
                            <strong>产品描述</strong><span>Product Description</span>
                        </h2>
                        <p class="c-box-main"><?=$description?></p>
                    </div>

                    <div class="c-box-item">
                        <ul class="product-show">
                            <?php if(!empty($images)){
                                $i = 0;
                                foreach($images as $img){?>
                                    <li>
                                        <img src="<?=$img['src']?>">
                                    </li>
                                    <?php
                                    $i++;
                                }
                            }?>
                        </ul>
                    </div>
                </div>
            </div></div>

    </div>
</div>
</body>
