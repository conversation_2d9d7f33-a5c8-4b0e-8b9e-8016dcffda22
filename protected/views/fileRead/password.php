<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        <?php echo $file_name;?>
    </title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body,
        .app {
            height: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", STHeiti, "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
            background-color: #fff;
        }

        .app {}

        .app img {
            height: 100%;
            width: auto;
            display: block;
        }

        .app .sidebar {
            float: left;
            height: 100%;
        }

        .app .main {
            overflow: hidden;
            height: 100%;
            position: relative;
        }

        .app .content {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            max-width: 540px;
        }

        .app .name {
            font-size: 28px;
            color: #454545;
            line-height: 40px;
        }

        .app .tips {
            font-size: 12px;
            padding-top: 10px;
            padding-bottom: 44px;
        }

        .app input[name="password"] {
            width: 100%;
            height: 40px;
            border: 1px solid #DCDCDC;
            border-radius: 4px;
            outline: none;
            padding: 0 16px;
            font-size: 14px;
        }

        .app .submit-button {
            width: 120px;
            height: 36px;
            line-height: 36px;
            border: none;
            padding: 0;
            margin: 0;
            text-align: center;
            color: #FFFFFF;
            background-color: #FF6868;
            border-radius: 18px;
            font-size: 14px;
            outline: none;
            cursor: pointer;
        }

        .app .label {
            font-size: 14px;
            line-height: 16px;
            padding-bottom: 15px;
        }

        .app .error-tips {
            color: #FF6868;
            font-size: 12px;
            padding-top: 10px;
        }

        .app .button-group {
            padding-top: 36px;
            text-align: center;
        }

        @media (max-width: 960px) {
            .sidebar {
                display: none;
            }
        }

    </style>
</head>

<body>
<div class="app">

    <div class="sidebar">
        <img src="https://mkoss.xiaoman.cn/uploader/98d7c4f4829ee101389bc3c239febd77cc7a022e/38ca3b32-9077-c084-1806-a874637ce5b1.png"
             alt="">
    </div>

    <div class="main">
        <div class="content">
            <div class="name">
                <?php echo $file_name;?>
            </div>
            <form method="POST" action="">
                <input type="hidden" value="1" name="check">
                <div class="tips">
                    To access this file, please enter the password.
                </div>
                <div class="label">
                    <label for="password">Password:</label>
                </div>
                <div>
                    <input name="password" type="text">
                </div>
                <?php if($password_error == true) { ?>
                    <div class="error-tips">Please enter the correct password.</div>
                <?php } ?>
                <div class="button-group">
                    <button type="submit" class="submit-button">submit</button>
                </div>
            </form>
        </div>
    </div>
</div>
</body>

</html>
