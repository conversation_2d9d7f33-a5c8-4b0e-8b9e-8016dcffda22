<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        <?php echo $file_name;?>
    </title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
        }

        body {
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", STHeiti, "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
            background-color: #fff;
        }

        .ellipsis {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .fullscreen,
        .download {
            cursor: pointer;
            position: relative;
            display: inline-block;
        }

        .fullscreen .tooltips,
        .download .tooltips {
            display: none;
        }

        .fullscreen:hover .tooltips,
        .download:hover .tooltips {
            display: block;
        }

        .fullscreen {
            margin-right: 24px;
        }

        .header {
            padding: 0 34px;
            line-height: 54px;
            border-bottom: 1px solid #DCDCDC;
            z-index: 3333;
            overflow: hidden;
        }

        .header .name {
            float: left;
            font-size: 14px;
            color: #454545;
            margin: 0;
            padding: 0;
            margin-right: 14px;
            max-width: 240px;
        }

        .header .tips {
            float: left;
            font-size: 12px;
            color: #959595;
        }

        .header .left {
            float: left;
        }

        .header .right {
            position: absolute;
            right: 34px;
            top: 0;
        }

        div.image-preview {
            overflow-y: auto;
            text-align: center;
        }

        div.image-preview img {
            vertical-align: middle;
            max-width: 100%;
        }
        .tooltips {
            padding: 7px 10px;
            border-radius: 4px;
            background: #222222;
            color: #FFFFFF;
            position: absolute;
            font-size: 12px;
            white-space:nowrap;
            line-height: 20px;
            right: 0;
            top: 44px;
        }
        .tooltips::after {
            position: absolute;
            right: 3px;
            top: -8px;
            content: '';
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 6.5px 11px 6.5px;
            border-color: transparent transparent #222222 transparent;
        }

    </style>
</head>

<body>
<div class="header">
    <div class="left">
        <h1 class="name ellipsis">
            <?php echo $file_name;?>
        </h1>
    </div>
    <div class="right">
        <div class="fullscreen">
            <img src="https://mkoss.xiaoman.cn/uploader/98d7c4f4829ee101389bc3c239febd77cc7a022e/745b354e-b79c-acdd-8caf-9afe6c514b16.png">
            <div class="tooltips">
                打开全屏
            </div>
        </div>
        <div class="download">
            <a href="<?php echo $download_url;?>" download="<?php echo $file_name;?>">
                <img src="https://mkoss.xiaoman.cn/uploader/98d7c4f4829ee101389bc3c239febd77cc7a022e/40461560-951d-132b-2b27-17d6fbfd8966.png">
            </a>
            <div class="tooltips">
                下载
            </div>
        </div>
    </div>
</div>
<!-- 判断 ['png', 'jpg', 'gif', 'jpeg', 'webp', 'bmp'] 显示这个 -->
<?php if(in_array($file_ext, ['.png', '.jpg', '.gif', '.jpeg', '.webp', '.bmp'], true)) {?>
    <div class="image-preview" oncontextmenu="return false">
        <img src="<?php echo $file_src;?>" alt="">
    </div>
<?php } else { ?>
    <!-- 否则这个 -->
    <iframe class="iframe-preview" src="https://doc-preview.xiaoman.cn/preview/agent?url=<?php echo $file_src; ?>" frameborder="0">
    </iframe>
<?php } ?>

<script src="https://mkoss.xiaoman.cn/cdn/fingerprint2.min.js"></script>
<script src="https://mkoss.xiaoman.cn/cdn/cookie.min.js"></script>
<script src="https://mkoss.xiaoman.cn/landing-page/common/js/jquery.1.12.4.min.js"></script>
<script>
    var updateIframe = function () {
        $('.iframe-preview').attr({
            width: $(window).width(),
            height: $(window).height() - $('.header').height(),
        })
        $('.image-preview').css({
            width: $(window).width(),
            height: $(window).height() - $('.header').height(),
        })
    }


    $(function () {
        updateIframe()
    })

    var startDate
    var maxPage = 1
    var totalPage = 0

    var uid = cookie.get('file_visitor_id')
    var getUID = function () {
        var dfd = $.Deferred()
        if (uid) {
            dfd.resolve(uid)
        } else {
            new window.Fingerprint2().get(function(result, components) {
                uid = result
                cookie.set('file_visitor_id', uid), {
                    path: '/'
                }
                dfd.resolve(uid)
            })
        }
        return dfd.promise()
    }


    var guuid = function () {
        var s = []
        var hexDigits = "0123456789abcdef"
        for (var i = 0; i < 36; i++) {
            s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
        }
        s[14] = "4";
        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
        s[8] = s[13] = s[18] = s[23] = "-"

        var uuid = s.join("")
        return uuid
    }

    var uuid = guuid()


    var versions = function(){
        var u = navigator.userAgent, app = navigator.appVersion;
        return {//移动终端浏览器版本信息
            trident: u.indexOf('Trident') > -1, //IE内核
            presto: u.indexOf('Presto') > -1, //opera内核
            webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
            gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
            iPhone: u.indexOf('iPhone') > -1 , //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            webApp: u.indexOf('Safari') == -1, //是否web应该程序，没有头部与底部
            weixin: u.indexOf('MicroMessenger') > -1, //是否微信
            qq: u.match(/\sQQ/i) == " qq", //是否QQ
            windowsWechat: u.indexOf('WindowsWechat') > -1,
        };
    }()

    var reporting = false;

    var report = function () {

        reporting = true;

        var duration = Math.ceil((new Date().getTime() - startDate) / 1000)
        return getUID().done(function (value) {
            return $.ajax({
                method: 'get',
                async: false,
                url: "<?php echo $visit_url;?>",
                data: {
                    total_page: totalPage,
                    read_total_time: duration,
                    current_page: maxPage,
                }
            })
                .always(function () {
                    startDate = new Date().getTime()
                    timeoutReport()
                })
        })
    }


    var count = 0
    var timeoutReport = function () {
        count = count < 5 ? count + 1.2 : count
        setTimeout(function () {
            report()
        }, count * 1000)
    }

    if (versions.android || versions.ios || (versions.weixin && !versions.windowsWechat)) {
        $('.download').hide()
        $('.fullscreen').hide()
    }


    if (['.png', '.jpg', '.gif', '.jpeg', '.webp', '.bmp'].indexOf('<?php echo $file_ext; ?>') > -1) {
        maxPage = 1
        totalPage = 1
        startDate = new Date().getTime()
        report()
    }

    $(window)
        .on('beforeunload', function () {
            report()
        })
        .on('resize', function () {
            updateIframe()
        })
        .on('message', function (e) {
            var data = e.originalEvent.data
            switch (data.type) {
                case "opened":
                {
                    break
                }
                case "loaded":
                {
                    startDate = new Date().getTime()
                    totalPage = Number(data.doc.totalPages)
                    report()
                    break
                }
                case "changed":
                {
                    maxPage = Math.max(data.page, maxPage)
                }
            }
        })

    // 某些移动端不能走加载插件后的回调，这里设置一个辅助获取文档信息
    $(document).ready(function(e) {
        var iframe = $('.iframe-preview')[0]
        iframe.onload = function () {
            setTimeout(function () {
                if (!reporting) {
                    startDate = new Date().getTime()
                    totalPage = 1
                    report()
                }
            }, 3000)
        }
    });



    var supportFullScreen = function (el) {
        el = el || document.documentElement
        var fs = [
            'requestFullscreen',
            'mozRequestFullScreen',
            'webkitRequestFullscreen',
            'msRequestFullscreen'
        ]
        for(var i = 0; i < fs.length; i++) {
            if (el[fs[i]]) {
                return true
            }
        }
        return false
    }

    var fullScreenAction = function (el) {
        el = el || document.documentElement
        var fs = [
            'requestFullscreen',
            'mozRequestFullScreen',
            'webkitRequestFullscreen',
            'msRequestFullscreen'
        ]

        for(var i = 0; i < fs.length; i++) {
            if (el[fs[i]]) {
                el[fs[i]]()
                return
            }
        }
    }


    var exitFullScreenAction = function () {
        var exitFullScreen = [
            'exitFullscreen',
            'mozCancelFullScreen',
            'webkitExitFullscreen'
        ]

        for(var i = 0; i < exitFullScreen.length; i++) {
            if (document[exitFullScreen[i]]) {
                document[exitFullScreen[i]]()
                return
            }
        }
    }

    var isFullscreenAction = function () {
        return document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen;
    }

    var fullscreenActionHandler = function () {
        var is = isFullscreenAction()
        var e = 'https://mkoss.xiaoman.cn/uploader/650a77c6b8db62ed61038cb48123c3278e67f127/3d7c2ee3-7184-cae4-348e-1699909cbeca.png'
        var f = 'https://mkoss.xiaoman.cn/uploader/98d7c4f4829ee101389bc3c239febd77cc7a022e/745b354e-b79c-acdd-8caf-9afe6c514b16.png'

        if (is) {
            $('.fullscreen img').attr('src', e)
            $('.fullscreen .tooltips').html('退出全屏')
        } else {
            $('.fullscreen img').attr('src', f)
            $('.fullscreen .tooltips').html('打开全屏')
        }
    }

    if (!supportFullScreen()) {
        $('.fullscreen').hide()
    }

    $('.fullscreen').on('click', function () {
        var is = isFullscreenAction()
        if (is) {
            exitFullScreenAction()
        } else {
            fullScreenAction()
        }
    })

    $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange', function () {
        fullscreenActionHandler()
    });

</script>
</body>

</html>
