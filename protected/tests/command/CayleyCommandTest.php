<?php


class CayleyCommandTest extends ConsoleTestCase
{

    public function testAds()
    {
        $data = json_decode('{
            "baseMetrics": {
                "impressions": 2482,
                "clicks": 198,
                "costMicros": "312847500",
                "conversions": 3
            },
            "potentialMetrics": {
                "impressions": 2482,
                "clicks": 198,
                "costMicros": "312847500",
                "conversions": 3
            }
        }', true);
        dd(array_diff_assoc($data['baseMetrics'], $data['potentialMetrics']));

        $this->runCommand('TestAds');
    }

}