<?php

namespace common\tests\command;

use ConsoleTestCase;

class ExportCommandTest extends ConsoleTestCase
{
    public function testMultiExport()
    {
        $taskId = 3161902008;
        $model = \CustomerExport::model()->findByPk($taskId);
        $model->status = \CustomerExport::STATUS_INIT;
        $model->update(['status']);
        $param = [
            'task_id' => $taskId,
            'operator_id' => 11864032,
            'type' => 11,
            'params' => "start_date=2022-09-01&end_date=2022-11-20&type=11"
        ];

        $this->runCommand('multiExport', $param);
    }

    public function testExport()
    {
        //$this->loginAsWeason3();
        $this->loginAsKk();
        $taskId = 4293904382;
        //$taskId = 3446460733;
        $model = \CustomerExport::model()->findByPk($taskId);
        $model->status = \CustomerExport::STATUS_INIT;
        $model->update(['status']);
        $param = [
            'task_id' => $taskId,
            'operator_id' => $model->user_id,
            'type' => $model->type,
            'params' => $model->param
        ];

        $this->runCommand('export', $param);
    }

    public function testOpportunityExport()
    {
        $taskId = 3420376187;
        $model = \CustomerExport::model()->findByPk($taskId);
        $model->status = \CustomerExport::STATUS_INIT;
        $model->update(['status']);
        $param = [
            'task_id' => $taskId,
            'operator_id' => $model->user_id,
            'type' => \CustomerExportTask::TYPE_OPPORTUNITY,
            'params' => $model->param
        ];

        $this->runCommand('export', $param);
    }

    public function testCustomerExport()
    {
        $taskId = 4228415390;
        $model = \CustomerExport::model()->findByPk($taskId);
        $model->status = \CustomerExport::STATUS_INIT;
        $model->update(['status']);
        $param = [
            'task_id' => $taskId,
            'operator_id' => $model->user_id,
            'type' => \CustomerExportTask::TYPE_CUSTOMER,
            'params' => $model->param
        ];

        $this->runCommand('export', $param);
    }
}