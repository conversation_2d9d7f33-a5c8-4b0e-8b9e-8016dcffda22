<?php

namespace tests\integration\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\AiAgentFactory;
use common\library\ai_sdr\Constant;

require_once __DIR__ . '/../../unit/ai_sdr/MockServices.php';

use tests\unit\ai_sdr\MockServiceFactory;

/**
 * AI SDR端到端集成测试
 * 
 * 测试完整的AI SDR工作流，验证依赖注入架构的端到端功能
 */
class AiSdrEndToEndIntegrationTest extends AiSdrTestCase
{
    private array $mockServices;
    private AISdrService $aiSdrService;
    private SdrDetailExecutor $sdrDetailExecutor;
    private RecommendCompanyService $recommendCompanyService;
    
    protected function setUp(): void
    {
        parent::setUp();

        // 创建Mock服务集合
        $this->mockServices = MockServiceFactory::createMockServiceSet();

        // 配置Mock服务的响应（在创建服务实例之前）
        $this->setupMockResponses();

        // 创建使用依赖注入的服务实例
        $this->aiSdrService = new AISdrService(
            $this->getTestClientId(),
            $this->getTestUserId(),
            $this->mockServices['recommendApi'],
            $this->mockServices['leadAutoArchive'],
            $this->mockServices['queueService']
        );

        $this->sdrDetailExecutor = new SdrDetailExecutor(
            $this->getTestClientId(),
            $this->getTestUserId(),
            $this->mockServices['recommendApi'],
            $this->mockServices['aiAgentFactory']
        );

        $this->recommendCompanyService = new RecommendCompanyService(
            $this->getTestClientId(),
            $this->getTestUserId(),
            $this->mockServices['recommendApi'],
            $this->mockServices['aiAgentFactory']
        );
    }
    
    private function setupMockResponses(): void
    {
        // 配置推荐API响应
        $this->mockServices['recommendApi']->setMatchResults([
            'data' => [
                [
                    'id' => 'company_001',
                    'domain' => 'tech-company.com',
                    'company_name' => 'Tech Company Ltd',
                    'industry' => 'Technology',
                    'match_score' => 0.92
                ],
                [
                    'id' => 'company_002',
                    'domain' => 'manufacturing-corp.com',
                    'company_name' => 'Manufacturing Corp',
                    'industry' => 'Manufacturing',
                    'match_score' => 0.87
                ]
            ],
            'total' => 2
        ]);
        
        $this->mockServices['recommendApi']->setResponse(['tech-company.com'], [
            'company_name' => 'Tech Company Ltd',
            'main_products' => ['Software Solutions', 'Cloud Services'],
            'company_type' => ['Technology Provider'],
            'public_homepage' => ['https://tech-company.com'],
            'industry' => 'Technology'
        ]);
        
        $this->mockServices['recommendApi']->setEmailRatings([
            '<EMAIL>' => \common\library\auto_market\Constant::MAIL_LEVEL_GOOD,
            '<EMAIL>' => \common\library\auto_market\Constant::MAIL_LEVEL_GENERAL
        ]);
        
        // 配置线索归档响应
        $this->mockServices['leadAutoArchive']->setLeadForDomain('tech-company.com', (object)[
            'lead_id' => 2001,
            'company_name' => 'Tech Company Ltd',
            'website' => 'https://tech-company.com',
            'image_list' => ['tech-logo.jpg'],
            'company_hash_id' => 'tech_hash_001',
            'main_customer_email' => '<EMAIL>'
        ]);
        
        $this->mockServices['leadAutoArchive']->setLeadForDomain('manufacturing-corp.com', (object)[
            'lead_id' => 2002,
            'company_name' => 'Manufacturing Corp',
            'website' => 'https://manufacturing-corp.com',
            'image_list' => ['mfg-logo.jpg'],
            'company_hash_id' => 'mfg_hash_002',
            'main_customer_email' => '<EMAIL>'
        ]);
        
        // 配置AI服务响应
        $this->mockServices['aiServices']->setQualityAnalysisResult([
            'answer' => [
                'quality_score' => 88,
                'quality_level' => Constant::LEAD_QUALITY_HIGH,
                'analysis_details' => [
                    'company_size' => 'Large',
                    'industry_match' => 'Excellent',
                    'contact_quality' => 'High',
                    'business_potential' => 'Very High'
                ]
            ],
            'record_id' => 30001
        ]);
        
        $this->mockServices['aiServices']->setMarketingContentResult([
            'answer' => [
                'subject' => 'Strategic Partnership Opportunity in Technology Solutions',
                'content' => 'Dear Tech Company Team, We have identified exciting synergies between our solutions and your technology needs...',
                'personalization_score' => 0.94,
                'tone' => 'Professional',
                'call_to_action' => 'Schedule a strategic discussion'
            ],
            'record_id' => 30002
        ]);
        
        $this->mockServices['aiServices']->setBackgroundCheckResult([
            'answer' => [
                'company_analysis' => [
                    'business_model' => 'B2B Technology Solutions',
                    'market_position' => 'Market Leader',
                    'growth_stage' => 'Expansion',
                    'technology_stack' => ['Cloud', 'AI', 'SaaS']
                ],
                'risk_assessment' => 'Low Risk',
                'opportunity_score' => 0.91
            ],
            'record_id' => 30003
        ]);
        
        // 配置队列服务为同步模式
        $this->mockServices['queueService']->setSyncMode(true);
    }
    
    /**
     * 测试完整的AI SDR工作流
     */
    public function testCompleteAiSdrWorkflow()
    {
        // 清空所有调用记录
        $this->clearAllMockServices();
        
        // 阶段1: 任务创建
        $this->assertTrue(method_exists($this->aiSdrService, 'createAiSdrTask'));
        
        // 阶段2: 公司推荐和挖掘
        // 直接在测试中设置Mock响应
        $this->mockServices['recommendApi']->setMatchResults([
            'data' => [
                [
                    'id' => 'company_001',
                    'domain' => 'tech-company.com',
                    'company_name' => 'Tech Company Ltd',
                    'industry' => 'Technology',
                    'match_score' => 0.92
                ],
                [
                    'id' => 'company_002',
                    'domain' => 'manufacturing-corp.com',
                    'company_name' => 'Manufacturing Corp',
                    'industry' => 'Manufacturing',
                    'match_score' => 0.87
                ]
            ],
            'total' => 2
        ]);

        // 验证Mock配置是否正确
        $reflection = new \ReflectionClass($this->mockServices['recommendApi']);
        $matchResultsProperty = $reflection->getProperty('matchResults');
        $matchResultsProperty->setAccessible(true);
        $configuredResults = $matchResultsProperty->getValue($this->mockServices['recommendApi']);
        $this->assertNotEmpty($configuredResults, 'Match results should be configured');

        $matchResults = $this->mockServices['recommendApi']->getMatchCompanyByProfile(
            1, // 行业匹配
            [1, 2], // industryIds
            ['Software Solutions'], // industryProducts
            null, // beforePortraitIds
            [], // excludeDomains
            20 // pageSize
        );
        
        $this->assertNotEmpty($matchResults['data']);
        $this->assertCount(2, $matchResults['data']);
        $this->assertEquals('tech-company.com', $matchResults['data'][0]['domain']);
        $this->assertEquals('manufacturing-corp.com', $matchResults['data'][1]['domain']);

        // 阶段3: 线索归档
        $domains = array_column($matchResults['data'], 'domain');
        $archivedLeads = $this->mockServices['leadAutoArchive']->archiveByBatchDomain($domains, true);
        
        $this->assertCount(2, $archivedLeads);
        $this->assertArrayHasKey('tech-company.com', $archivedLeads);
        $this->assertArrayHasKey('manufacturing-corp.com', $archivedLeads);
        $this->assertNotNull($archivedLeads['tech-company.com']);
        $this->assertNotNull($archivedLeads['manufacturing-corp.com']);
        // 验证线索对象包含必要的字段
        $this->assertObjectHasAttribute('company_name', $archivedLeads['tech-company.com']);
        $this->assertObjectHasAttribute('company_name', $archivedLeads['manufacturing-corp.com']);
        
        // 阶段4: 质量分析
        $qualityResult = $this->mockServices['aiServices']->getQualityAnalysisResult();
        
        $this->assertEquals(88, $qualityResult['answer']['quality_score']);
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $qualityResult['answer']['quality_level']);
        $this->assertEquals('Excellent', $qualityResult['answer']['analysis_details']['industry_match']);
        
        // 阶段5: 背景调研
        $backgroundResult = $this->mockServices['aiServices']->getBackgroundCheckResult();
        
        $this->assertEquals('B2B Technology Solutions', $backgroundResult['answer']['company_analysis']['business_model']);
        $this->assertEquals('Low Risk', $backgroundResult['answer']['risk_assessment']);
        $this->assertEquals(0.91, $backgroundResult['answer']['opportunity_score']);
        
        // 阶段6: 营销内容生成
        $marketingResult = $this->mockServices['aiServices']->getMarketingContentResult();
        
        $this->assertStringContainsString('Strategic Partnership', $marketingResult['answer']['subject']);
        $this->assertStringContainsString('technology needs', $marketingResult['answer']['content']);
        $this->assertEquals(0.94, $marketingResult['answer']['personalization_score']);
        
        // 阶段7: 邮箱质量评级
        $emailRatings = $this->mockServices['recommendApi']->getMailQualityRating([
            '<EMAIL>',
            '<EMAIL>'
        ]);
        
        $this->assertEquals(\common\library\auto_market\Constant::MAIL_LEVEL_GOOD, $emailRatings['<EMAIL>']);
        $this->assertEquals(\common\library\auto_market\Constant::MAIL_LEVEL_GENERAL, $emailRatings['<EMAIL>']);
        
        // 验证所有服务都被正确调用
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'));
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMailQualityRating'));
        $this->assertTrue($this->mockServices['leadAutoArchive']->wasMethodCalled('archiveByBatchDomain'));
        
        $this->assertTrue(true, 'Complete AI SDR workflow test passed');
    }
    
    /**
     * 测试AI Agent工厂的端到端使用
     */
    public function testAiAgentFactoryEndToEnd()
    {
        $this->clearAllMockServices();
        
        // 通过反射测试各个服务中的Agent创建
        $services = [
            'sdrDetailExecutor' => $this->sdrDetailExecutor,
            'recommendCompanyService' => $this->recommendCompanyService
        ];
        
        foreach ($services as $serviceName => $service) {
            $reflection = new \ReflectionClass($service);
            
            // 测试质量分析Agent创建（如果方法存在）
            if ($reflection->hasMethod('createQualityAnalysisAgent')) {
                $method = $reflection->getMethod('createQualityAnalysisAgent');
                $method->setAccessible(true);
                $agent = $method->invoke($service);
                $this->assertNotNull($agent, "Quality analysis agent should be created in {$serviceName}");
            }
            
            // 测试Agent工作流服务创建（如果方法存在）
            if ($reflection->hasMethod('createAgentWorkflowService')) {
                $method = $reflection->getMethod('createAgentWorkflowService');
                $method->setAccessible(true);
                $workflowService = $method->invoke($service);
                $this->assertNotNull($workflowService, "Workflow service should be created in {$serviceName}");
            }
        }
        
        // 验证AI Agent工厂被调用
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createQualityAnalysisAgent'));
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createAgentWorkflowService'));
        
        $this->assertTrue(true, 'AI Agent factory end-to-end test passed');
    }
    
    /**
     * 测试队列服务的端到端使用
     */
    public function testQueueServiceEndToEnd()
    {
        $this->clearAllMockServices();
        
        // 测试队列任务分发
        $mockJob1 = new \stdClass();
        $mockJob1->type = 'ai_sdr_dig_task';
        
        $mockJob2 = new \stdClass();
        $mockJob2->type = 'ai_sdr_quality_analyze';
        
        $this->mockServices['queueService']->dispatch($mockJob1);
        $this->mockServices['queueService']->dispatchSync($mockJob2);
        
        // 验证队列任务被正确分发（可能有多个任务）
        $this->assertGreaterThanOrEqual(1, $this->mockServices['queueService']->getDispatchedJobsCount());
        
        $dispatchedJobs = $this->mockServices['queueService']->getDispatchedJobs();
        $this->assertEquals('async', $dispatchedJobs[0]['type']);
        $this->assertEquals('sync', $dispatchedJobs[1]['type']);
        
        $this->assertTrue(true, 'Queue service end-to-end test passed');
    }
    
    /**
     * 测试异常场景的处理
     */
    public function testErrorHandlingEndToEnd()
    {
        $this->clearAllMockServices();
        
        // 测试空的推荐结果
        $this->mockServices['recommendApi']->setMatchResults(['data' => [], 'total' => 0]);

        $emptyResults = $this->mockServices['recommendApi']->getMatchCompanyByProfile(
            1, [999], ['NonExistent Product'], null, [], 20
        );

        $this->assertEmpty($emptyResults['data']);
        $this->assertEquals(0, $emptyResults['total']);
        
        // 测试线索归档失败
        $failedArchive = $this->mockServices['leadAutoArchive']->archiveByBatchDomain(['nonexistent.com'], false);
        $this->assertEmpty($failedArchive);
        
        $this->assertTrue(true, 'Error handling end-to-end test passed');
    }
    
    private function clearAllMockServices(): void
    {
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
    }
    
    protected function tearDown(): void
    {
        $this->clearAllMockServices();
        parent::tearDown();
    }
}
