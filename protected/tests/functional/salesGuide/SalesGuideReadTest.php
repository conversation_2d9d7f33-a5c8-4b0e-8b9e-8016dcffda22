<?php
/**
 * Created by PhpStorm.
 * User: june
 * Date: 2022/3/1
 * Time: 5:27 PM
 */
namespace tests\functional\alibaba;

class SalesGuideReadTest extends \WebFunctionalTestCase
{

    public function testFlowList()
    {
       // $this->loginUser('<EMAIL>');
        $this->loginAsYuanfan4();
        $this->callAction('FlowList');
        $this->responseOk();
    }

    public function testGetAllFlowLink()
    {
        $this->loginUser('<EMAIL>');
        $this->callAction('GetAllFlowLink');
        $this->responseOk();
    }

    public function testFlowDetail()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'flow_id' => 1106206990
        ];
        $this->callAction('FlowDetail', $params);
        $this->responseOk();
    }

    public function testGetLink()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'stage_id' => 2383038716
        ];
        $this->callAction('GetLink', $params);
        $this->responseOk();
    }

    public function testGetDefaultFlowLink()
    {
        $this->loginUser('<EMAIL>');
        \Yii::app()->language = 'zh-TW';
        $this->callAction('getDefaultFlowLink');
        $this->responseOk();
    }

    public function testGetLinkList()
    {
        $this->loginUser('<EMAIL>');
        $params = [
            'keyword' => '',
            'sort_field' => 'create_time',
            'sort_type' => 'asc',
            'page' => 1,
            'page_size' => 20
        ];
        $this->callAction('GetLinkList',$params);
        $this->responseOk();
    }


}
