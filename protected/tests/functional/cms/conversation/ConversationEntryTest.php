<?php

namespace common\tests\functional\cms\conversation;

use common\library\cms\conversation\CmsConversationService;
use InternalFunctionalTestCase;

class ConversationEntryTest extends InternalFunctionalTestCase
{

	public function testGetConversationEntryConfig(){
		$clientId = 364115;
		$siteId = 1000346;
		$res = CmsConversationService::getVisitorConversationEntryConfig($clientId, $siteId);
		var_dump($res);
		die;
	}

}