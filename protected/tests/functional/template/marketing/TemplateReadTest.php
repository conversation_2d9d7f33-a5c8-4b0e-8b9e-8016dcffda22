<?php
/**
 * Created by PhpStorm.
 * User: cayley
 * Date: 2020/4/15
 * Time: 4:09 PM
 */



class TemplateReadTest extends MarketingFunctionalTestCase
{

    public function testCreate()
    {
//        $path ='/Users/<USER>/Downloads/test-template_1.jpg';
//        $ali = new AliyunUpload();
//        $ali->setBucket ( $ali->getAllowBucket() );
//        $ali->setLocalFilePath ( $path );
//        $ali->setScenes(AliyunUpload::SCENES_CMS);
//        $ali->setFileName ( 'test-template_1.jpg' );
//        $ali->upload ();

        $template = new \common\library\cms\template\Template(1);
        $template->template_name = '测试模板2';
//        $template->cover_file_id = $ali->getFileObject()->file_id;
//        $template->category_id = 1;
        $template->enable_flag=1;
        $template->data ='todo';
        $template->create_time= date('Y-m-d H:i:s');
        $template->update_time = date('Y-m-d H:i:s');
        $template->save();

        $page = new \common\library\cms\template\TemplatePage();
        $page->template_id = $template->template_id;
        $page->type =1;
        $page->data = 'todo';
        $page->create_time = $page->update_time = date('Y-m-d H:i:s');
        $page->save();
    }

    public function testList()
    {
        $params = [];
        $this->callAction('list',  $params);
        $this->responseOk();
    }

    public function testSiteTemplateList()
    {
        $params = [
            'site_id' =>6
        ];
        $this->callAction('siteTemplateList',  $params);
        $this->responseOk();
    }



    public function testCategoryList()
    {
        $params = [
        ];
        $this->callAction('CategoryList',  $params);
        $this->responseOk();
    }

}
