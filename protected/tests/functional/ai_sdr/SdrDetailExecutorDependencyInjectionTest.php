<?php

namespace tests\functional\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\AiAgentFactory;
use common\library\ai_sdr\Constant;

require_once __DIR__ . '/../../unit/ai_sdr/MockServices.php';

use tests\unit\ai_sdr\MockServiceFactory;

/**
 * SdrDetailExecutor依赖注入测试
 * 
 * 测试重构后的SdrDetailExecutor是否正确支持依赖注入
 */
class SdrDetailExecutorDependencyInjectionTest extends AiSdrTestCase
{
    private array $mockServices;
    private SdrDetailExecutor $sdrDetailExecutor;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Mock服务集合
        $this->mockServices = MockServiceFactory::createMockServiceSet();
        
        // 创建使用依赖注入的SdrDetailExecutor
        $this->sdrDetailExecutor = new SdrDetailExecutor(
            $this->getTestClientId(),
            $this->getTestUserId(),
            $this->mockServices['recommendApi'],
            $this->mockServices['aiAgentFactory']
        );
    }
    
    /**
     * 测试SdrDetailExecutor构造函数的依赖注入
     */
    public function testConstructorDependencyInjection()
    {
        // 验证服务实例正确注入
        $this->assertInstanceOf(SdrDetailExecutor::class, $this->sdrDetailExecutor);
        
        // 通过反射验证依赖注入的服务
        $reflection = new \ReflectionClass($this->sdrDetailExecutor);
        
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $this->assertSame($this->mockServices['recommendApi'], $recommendApiProperty->getValue($this->sdrDetailExecutor));
        
        $aiAgentFactoryProperty = $reflection->getProperty('aiAgentFactory');
        $aiAgentFactoryProperty->setAccessible(true);
        $this->assertSame($this->mockServices['aiAgentFactory'], $aiAgentFactoryProperty->getValue($this->sdrDetailExecutor));
        
        $this->assertTrue(true, 'Dependency injection working correctly');
    }
    
    /**
     * 测试默认构造函数（向后兼容）
     */
    public function testDefaultConstructorBackwardCompatibility()
    {
        // 创建不使用依赖注入的SdrDetailExecutor（向后兼容）
        $defaultExecutor = new SdrDetailExecutor($this->getTestClientId(), $this->getTestUserId());
        
        $this->assertInstanceOf(SdrDetailExecutor::class, $defaultExecutor);
        
        // 验证默认实现被正确创建
        $reflection = new \ReflectionClass($defaultExecutor);
        
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $recommendApi = $recommendApiProperty->getValue($defaultExecutor);
        $this->assertInstanceOf(\common\library\recommend_plaza\RecommendApi::class, $recommendApi);
        
        $aiAgentFactoryProperty = $reflection->getProperty('aiAgentFactory');
        $aiAgentFactoryProperty->setAccessible(true);
        $aiAgentFactory = $aiAgentFactoryProperty->getValue($defaultExecutor);
        $this->assertNull($aiAgentFactory, 'Default executor should not inject AI agent factory');
        
        $this->assertTrue(true, 'Backward compatibility maintained');
    }
    
    /**
     * 测试AI Agent工厂的使用
     */
    public function testAiAgentFactoryUsage()
    {
        // 清空调用记录
        $this->mockServices['aiAgentFactory']->clear();
        
        // 通过反射调用受保护的方法
        $reflection = new \ReflectionClass($this->sdrDetailExecutor);
        
        // 测试质量分析Agent创建
        $createQualityAgentMethod = $reflection->getMethod('createQualityAnalysisAgent');
        $createQualityAgentMethod->setAccessible(true);
        $qualityAgent = $createQualityAgentMethod->invoke($this->sdrDetailExecutor);
        
        $this->assertNotNull($qualityAgent);
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createQualityAnalysisAgent'));
        
        // 测试EDM写作Agent创建
        $createEdmAgentMethod = $reflection->getMethod('createEdmWriteAgent');
        $createEdmAgentMethod->setAccessible(true);
        $edmAgent = $createEdmAgentMethod->invoke($this->sdrDetailExecutor);
        
        $this->assertNotNull($edmAgent);
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createEdmWriteAgent'));
        
        $this->assertTrue(true, 'AI Agent factory usage working correctly');
    }
    
    /**
     * 测试推荐API的使用
     */
    public function testRecommendApiUsage()
    {
        // 清空调用记录
        $this->mockServices['recommendApi']->clear();
        
        // 配置Mock响应
        $this->mockServices['recommendApi']->setEmailRatings([
            '<EMAIL>' => \common\library\auto_market\Constant::MAIL_LEVEL_GENERAL
        ]);
        
        // 通过反射获取recommendApi属性
        $reflection = new \ReflectionClass($this->sdrDetailExecutor);
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $recommendApi = $recommendApiProperty->getValue($this->sdrDetailExecutor);
        
        // 直接调用推荐API方法
        $result = $recommendApi->getMailQualityRating(['<EMAIL>']);
        
        $this->assertNotEmpty($result);
        $this->assertEquals(\common\library\auto_market\Constant::MAIL_LEVEL_GENERAL, $result['<EMAIL>']);
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMailQualityRating'));
        
        $this->assertTrue(true, 'Recommend API usage working correctly');
    }
    
    /**
     * 测试具体的AI Agent工厂实现
     */
    public function testConcreteAiAgentFactory()
    {
        // 创建具体的AI Agent工厂
        $concreteFactory = new AiAgentFactory($this->getTestClientId(), $this->getTestUserId());

        // 验证工厂实例创建成功
        $this->assertInstanceOf(AiAgentFactory::class, $concreteFactory);

        // 验证工厂方法存在
        $this->assertTrue(method_exists($concreteFactory, 'createQualityAnalysisAgent'));
        $this->assertTrue(method_exists($concreteFactory, 'createEdmWriteAgent'));
        $this->assertTrue(method_exists($concreteFactory, 'createAgentWorkflowService'));

        // 注意：跳过实际的Agent创建，因为需要数据库连接
        // 在集成测试中可以测试具体的Agent创建

        $this->assertTrue(true, 'Concrete AI Agent factory structure working correctly');
    }
    
    /**
     * 测试Mock服务的调用追踪
     */
    public function testMockServiceCallTracking()
    {
        // 清空所有调用记录
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
        
        // 验证服务调用记录功能正常
        $this->assertIsArray($this->mockServices['recommendApi']->getCallLog());
        $this->assertIsArray($this->mockServices['aiAgentFactory']->getCallLog());
        
        // 执行一些操作来触发服务调用
        $reflection = new \ReflectionClass($this->sdrDetailExecutor);
        $createQualityAgentMethod = $reflection->getMethod('createQualityAnalysisAgent');
        $createQualityAgentMethod->setAccessible(true);
        $createQualityAgentMethod->invoke($this->sdrDetailExecutor);
        
        // 验证调用记录
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createQualityAnalysisAgent'));
        $this->assertEquals(1, count($this->mockServices['aiAgentFactory']->getCallLog()));
        
        $this->assertTrue(true, 'Mock service call tracking working correctly');
    }
    
    protected function tearDown(): void
    {
        // 清理Mock服务状态
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
        
        parent::tearDown();
    }
}
