<?php

namespace tests\functional\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\Constant;

require_once __DIR__ . '/../../unit/ai_sdr/MockServices.php';

use tests\unit\ai_sdr\MockServiceFactory;

/**
 * AI SDR业务流程测试
 * 
 * 测试完整的AI SDR工作流，使用Mock服务来模拟外部依赖
 */
class AiSdrBusinessFlowTest extends AiSdrTestCase
{
    private array $mockServices;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Mock服务集合
        $this->mockServices = MockServiceFactory::createMockServiceSet();
        
        // 配置Mock推荐API的响应
        $this->mockServices['recommendApi']->setMatchResults([
            'data' => [
                [
                    'company_name' => 'Test Company 1',
                    'domain' => 'test1.com',
                    'industry' => 'Technology',
                    'match_score' => 0.85,
                    'portrait_id' => 'portrait_001'
                ],
                [
                    'company_name' => 'Test Company 2', 
                    'domain' => 'test2.com',
                    'industry' => 'Manufacturing',
                    'match_score' => 0.78,
                    'portrait_id' => 'portrait_002'
                ]
            ],
            'total' => 2
        ]);
        
        // 配置Mock线索归档的响应
        $this->mockServices['leadAutoArchive']->setLeadForDomain('test1.com', (object)[
            'id' => 1001,
            'company_name' => 'Test Company 1',
            'website' => 'https://test1.com',
            'status' => 'active'
        ]);
        
        $this->mockServices['leadAutoArchive']->setLeadForDomain('test2.com', (object)[
            'id' => 1002,
            'company_name' => 'Test Company 2',
            'website' => 'https://test2.com',
            'status' => 'active'
        ]);
        
        // 配置Mock AI服务的响应
        $this->mockServices['aiServices']->setQualityAnalysisResult([
            'answer' => [
                'quality_score' => 85,
                'quality_level' => Constant::LEAD_QUALITY_HIGH,
                'analysis_details' => [
                    'company_size' => 'Medium',
                    'industry_match' => 'High',
                    'contact_quality' => 'Good'
                ]
            ],
            'record_id' => 12345
        ]);
        
        $this->mockServices['aiServices']->setMarketingContentResult([
            'answer' => [
                'subject' => 'Partnership Opportunity with Your Company',
                'content' => 'Dear Sir/Madam, We would like to explore potential business opportunities...',
                'personalization_score' => 0.92
            ],
            'record_id' => 12346
        ]);
    }
    
    /**
     * 测试完整的AI SDR任务创建和执行流程
     */
    public function testCompleteAiSdrWorkflow()
    {
        // 注意：这里我们需要修改AISdrService来支持依赖注入
        // 目前先创建一个测试用的服务实例
        $taskData = AiSdrTestDataFactory::createTaskData([
            'id' => 12345, // 添加模拟的任务ID
            'task_status' => Constant::AI_SDR_TASK_STATUS_PROCESSING, // 使用正确的字段名
            'current_stage' => Constant::AI_SDR_STAGE_DIG, // 使用正确的字段名
            'tags' => [1, 2]
        ]);

        // 验证任务创建
        $this->assertNotEmpty($taskData['id'], 'Task should have an ID');
        $this->assertEquals(Constant::AI_SDR_TASK_STATUS_PROCESSING, $taskData['task_status']);
        $this->assertEquals(Constant::AI_SDR_STAGE_DIG, $taskData['current_stage']);
        
        // 模拟挖掘阶段 - 调用推荐API
        $matchResults = $this->mockServices['recommendApi']->getMatchCompanyByProfile(
            1, // 行业匹配
            [1, 2], // industryIds
            ['Product A'], // industryProducts
            null, // beforePortraitIds
            [], // excludeDomains
            20 // pageSize
        );
        
        $this->assertNotEmpty($matchResults['data'], 'Should have match results');
        $this->assertCount(2, $matchResults['data'], 'Should have 2 matched companies');
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'));
        
        // 模拟线索归档
        $domains = array_column($matchResults['data'], 'domain');
        $archivedLeads = $this->mockServices['leadAutoArchive']->archiveByBatchDomain($domains, true);
        
        $this->assertCount(2, $archivedLeads, 'Should have 2 archived leads');
        $this->assertArrayHasKey('test1.com', $archivedLeads);
        $this->assertArrayHasKey('test2.com', $archivedLeads);
        $this->assertTrue($this->mockServices['leadAutoArchive']->wasMethodCalled('archiveByBatchDomain'));
        
        // 模拟质量分析阶段
        $qualityResult = $this->mockServices['aiServices']->getQualityAnalysisResult();
        
        $this->assertArrayHasKey('answer', $qualityResult);
        $this->assertEquals(85, $qualityResult['answer']['quality_score']);
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $qualityResult['answer']['quality_level']);
        
        // 模拟营销内容生成
        $marketingResult = $this->mockServices['aiServices']->getMarketingContentResult();
        
        $this->assertArrayHasKey('answer', $marketingResult);
        $this->assertNotEmpty($marketingResult['answer']['subject']);
        $this->assertNotEmpty($marketingResult['answer']['content']);
        $this->assertGreaterThan(0.8, $marketingResult['answer']['personalization_score']);
        
        // 验证队列任务分发
        $this->mockServices['queueService']->dispatch(new \stdClass());
        $this->assertEquals(1, $this->mockServices['queueService']->getDispatchedJobsCount());
        
        $this->assertTrue(true, 'Complete AI SDR workflow test passed');
    }
    
    /**
     * 测试异常情况处理
     */
    public function testWorkflowErrorHandling()
    {
        // 测试空的匹配结果
        $this->mockServices['recommendApi']->setMatchResults(['data' => [], 'total' => 0]);
        
        $matchResults = $this->mockServices['recommendApi']->getMatchCompanyByProfile(
            1,
            [999], // 不存在的行业ID
            ['NonExistent Product'],
            null,
            [],
            20
        );
        
        $this->assertEmpty($matchResults['data'], 'Should have no match results for non-existent industry');
        $this->assertEquals(0, $matchResults['total']);
        
        // 测试线索归档失败
        $archivedLeads = $this->mockServices['leadAutoArchive']->archiveByBatchDomain(['nonexistent.com'], false);
        $this->assertEmpty($archivedLeads, 'Should have no archived leads for non-existent domain');
        
        $this->assertTrue(true, 'Error handling test passed');
    }
    
    /**
     * 测试Mock服务的调用记录
     */
    public function testMockServiceCallTracking()
    {
        // 调用各种Mock服务方法
        $this->mockServices['recommendApi']->getCompanyProfileByDomains(['example.com']);
        $this->mockServices['recommendApi']->getMailQualityRating(['<EMAIL>']);
        $this->mockServices['leadAutoArchive']->archiveByDomain('example.com', true);
        $this->mockServices['queueService']->dispatch(new \stdClass());
        
        // 验证调用记录
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getCompanyProfileByDomains'));
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMailQualityRating'));
        $this->assertTrue($this->mockServices['leadAutoArchive']->wasMethodCalled('archiveByDomain'));
        $this->assertEquals(1, $this->mockServices['queueService']->getDispatchedJobsCount());
        
        // 验证调用次数
        $this->assertEquals(1, $this->mockServices['recommendApi']->getCallCount('getCompanyProfileByDomains'));
        $this->assertEquals(1, $this->mockServices['recommendApi']->getCallCount('getMailQualityRating'));
        
        $this->assertTrue(true, 'Mock service call tracking test passed');
    }
    
    /**
     * 测试AI Agent工厂
     */
    public function testAiAgentFactory()
    {
        $factory = $this->mockServices['aiAgentFactory'];
        
        // 创建各种Agent
        $qualityAgent = $factory->createQualityAnalysisAgent();
        $industryAnalyzer = $factory->createSellerIndustryAnalyzer();
        $edmAgent = $factory->createEdmWriteAgent();
        $workflowService = $factory->createAgentWorkflowService();
        
        $this->assertNotNull($qualityAgent);
        $this->assertNotNull($industryAnalyzer);
        $this->assertNotNull($edmAgent);
        $this->assertNotNull($workflowService);
        
        // 验证调用记录
        $this->assertTrue($factory->wasMethodCalled('createQualityAnalysisAgent'));
        $this->assertTrue($factory->wasMethodCalled('createSellerIndustryAnalyzer'));
        $this->assertTrue($factory->wasMethodCalled('createEdmWriteAgent'));
        $this->assertTrue($factory->wasMethodCalled('createAgentWorkflowService'));
        
        $this->assertTrue(true, 'AI Agent factory test passed');
    }
    
    protected function tearDown(): void
    {
        // 清理Mock服务状态
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
        
        parent::tearDown();
    }
}
