<?php

namespace tests\functional\ai_sdr;

use common\library\ai_sdr\Constant;
use common\library\ai_sdr\AISdrService;
use common\library\ai_sdr\SdrDetailExecutor;
use common\library\ai_sdr\task\AiSdrTask;
use common\library\ai_sdr\task\AiSdrTaskFilter;
use common\library\ai_sdr\task_detail\AiSdrTaskDetail;
use common\library\ai_sdr\task_detail\AiSdrTaskDetailFilter;
use tests\DatabaseTransactions;

/**
 * AI SDR 性能和压力测试
 * 
 * 测试AI SDR模块在高负载下的性能表现
 */
class PerformanceTest extends \WebFunctionalTestCase
{
    use DatabaseTransactions;
    
    protected $testClientId = 333392;
    protected $testUserId = 249519530;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginAsQqlite_01();
    }
    
    /**
     * 测试大量任务查询性能
     */
    public function testLargeTaskQueryPerformance_WithManyTasks_CompletesWithinTimeLimit()
    {
        try {
            $startTime = microtime(true);
            
            // 执行多次任务查询
            $iterations = 50;
            $service = new AISdrService($this->testClientId, $this->testUserId);
            
            for ($i = 0; $i < $iterations; $i++) {
                $taskList = $service->getTaskList();
                $this->assertIsArray($taskList, "Task list query {$i} should return array");
            }
            
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            
            // 性能断言：50次查询应该在5秒内完成
            $this->assertLessThan(5.0, $executionTime, "50 task queries should complete within 5 seconds, took {$executionTime}s");
            
            // 平均查询时间应该小于100ms
            $averageTime = $executionTime / $iterations;
            $this->assertLessThan(0.1, $averageTime, "Average query time should be less than 100ms, was {$averageTime}s");
            
            $this->assertTrue(true, 'Large task query performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Large task query performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试批量数据处理性能
     */
    public function testBatchDataProcessingPerformance_WithLargeDataset_ProcessesEfficiently()
    {
        try {
            $startTime = microtime(true);
            
            // 模拟大量数据处理
            $batchSize = 100;
            $batches = 10;
            $totalProcessed = 0;
            
            for ($batch = 0; $batch < $batches; $batch++) {
                $batchData = $this->generateBatchTestData($batchSize);
                $processed = $this->processBatchData($batchData);
                $totalProcessed += $processed;
            }
            
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            
            // 性能断言
            $this->assertEquals($batchSize * $batches, $totalProcessed, 'Should process all data items');
            $this->assertLessThan(10.0, $executionTime, "Batch processing should complete within 10 seconds, took {$executionTime}s");
            
            // 吞吐量测试：每秒应该处理至少100个项目
            $throughput = $totalProcessed / $executionTime;
            $this->assertGreaterThan(100, $throughput, "Throughput should be > 100 items/sec, was {$throughput}");
            
            $this->assertTrue(true, 'Batch data processing performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Batch data processing performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试并发查询性能
     */
    public function testConcurrentQueryPerformance_WithSimulatedConcurrency_HandlesLoad()
    {
        try {
            $startTime = microtime(true);
            
            // 模拟并发查询
            $concurrentQueries = 20;
            $results = [];
            
            // 使用任务过滤器进行并发查询模拟
            for ($i = 0; $i < $concurrentQueries; $i++) {
                $filter = new AiSdrTaskFilter($this->testClientId);
                $filter->task_status = Constant::AI_SDR_TASK_STATUS_PROCESSING;
                $filter->limit(10);
                
                $queryStartTime = microtime(true);
                $tasks = $filter->find();
                $count = $filter->count();
                $queryEndTime = microtime(true);
                
                $results[] = [
                    'query_id' => $i,
                    'execution_time' => $queryEndTime - $queryStartTime,
                    'result_count' => is_array($tasks) ? count($tasks) : 0,
                    'total_count' => $count
                ];
            }
            
            $endTime = microtime(true);
            $totalExecutionTime = $endTime - $startTime;
            
            // 性能分析
            $queryTimes = array_column($results, 'execution_time');
            $averageQueryTime = array_sum($queryTimes) / count($queryTimes);
            $maxQueryTime = max($queryTimes);
            $minQueryTime = min($queryTimes);
            
            // 性能断言
            $this->assertLessThan(0.5, $averageQueryTime, "Average query time should be < 500ms, was {$averageQueryTime}s");
            $this->assertLessThan(2.0, $maxQueryTime, "Max query time should be < 2s, was {$maxQueryTime}s");
            $this->assertLessThan(10.0, $totalExecutionTime, "Total execution time should be < 10s, was {$totalExecutionTime}s");
            
            // 验证所有查询都成功
            foreach ($results as $result) {
                $this->assertGreaterThanOrEqual(0, $result['result_count'], 'Query should return non-negative result count');
                $this->assertGreaterThanOrEqual(0, $result['total_count'], 'Query should return non-negative total count');
            }
            
            $this->assertTrue(true, 'Concurrent query performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Concurrent query performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试内存使用性能
     */
    public function testMemoryUsagePerformance_WithLargeOperations_StaysWithinLimits()
    {
        try {
            $initialMemory = memory_get_usage(true);
            $peakMemory = $initialMemory;
            
            // 执行内存密集型操作
            $iterations = 100;
            $largeDataSets = [];
            
            for ($i = 0; $i < $iterations; $i++) {
                // 创建大型数据集
                $dataSet = $this->createLargeDataSet(1000);
                $largeDataSets[] = $dataSet;
                
                // 处理数据
                $this->processLargeDataSet($dataSet);
                
                // 监控内存使用
                $currentMemory = memory_get_usage(true);
                $peakMemory = max($peakMemory, $currentMemory);
                
                // 定期清理以防止内存泄漏
                if ($i % 10 === 0) {
                    unset($largeDataSets);
                    $largeDataSets = [];
                    gc_collect_cycles();
                }
            }
            
            $finalMemory = memory_get_usage(true);
            $memoryIncrease = $finalMemory - $initialMemory;
            $peakIncrease = $peakMemory - $initialMemory;
            
            // 内存使用断言
            $maxAllowedIncrease = 50 * 1024 * 1024; // 50MB
            $this->assertLessThan($maxAllowedIncrease, $memoryIncrease, "Memory increase should be < 50MB, was " . ($memoryIncrease / 1024 / 1024) . "MB");
            $this->assertLessThan($maxAllowedIncrease * 2, $peakIncrease, "Peak memory increase should be < 100MB, was " . ($peakIncrease / 1024 / 1024) . "MB");
            
            $this->assertTrue(true, 'Memory usage performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Memory usage performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试数据库连接池性能
     */
    public function testDatabaseConnectionPerformance_WithMultipleConnections_ManagesEfficiently()
    {
        try {
            $startTime = microtime(true);
            
            // 模拟多个数据库连接操作
            $connectionTests = 30;
            $connectionTimes = [];
            
            for ($i = 0; $i < $connectionTests; $i++) {
                $connStartTime = microtime(true);
                
                // 执行数据库操作
                $filter = new AiSdrTaskDetailFilter($this->testClientId);
                $filter->status = Constant::DETAIL_STATUS_ADD;
                $filter->limit(5);
                
                $details = $filter->find();
                $count = $filter->count();
                
                $connEndTime = microtime(true);
                $connectionTimes[] = $connEndTime - $connStartTime;
                
                // 验证操作成功
                $this->assertNotNull($details, "Connection {$i} should return results");
                $this->assertIsInt($count, "Connection {$i} should return count");
            }
            
            $endTime = microtime(true);
            $totalTime = $endTime - $startTime;
            
            // 连接性能分析
            $averageConnectionTime = array_sum($connectionTimes) / count($connectionTimes);
            $maxConnectionTime = max($connectionTimes);
            
            // 性能断言
            $this->assertLessThan(0.1, $averageConnectionTime, "Average connection time should be < 100ms, was {$averageConnectionTime}s");
            $this->assertLessThan(0.5, $maxConnectionTime, "Max connection time should be < 500ms, was {$maxConnectionTime}s");
            $this->assertLessThan(5.0, $totalTime, "Total connection test time should be < 5s, was {$totalTime}s");
            
            $this->assertTrue(true, 'Database connection performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Database connection performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试缓存性能
     */
    public function testCachePerformance_WithFrequentAccess_PerformsWell()
    {
        try {
            $startTime = microtime(true);
            
            // 模拟频繁的缓存操作
            $cacheOperations = 200;
            $cacheKeys = [];
            
            // 生成缓存键
            for ($i = 0; $i < $cacheOperations; $i++) {
                $clientId = $this->testClientId;
                $taskId = rand(10000, 99999);
                $cacheKey = sprintf(Constant::REDIS_CACHE_TASK_KEY, $clientId, $taskId);
                $cacheKeys[] = $cacheKey;
            }
            
            // 验证缓存键生成性能
            $keyGenerationTime = microtime(true) - $startTime;
            $this->assertLessThan(0.1, $keyGenerationTime, "Cache key generation should be fast, took {$keyGenerationTime}s");
            
            // 验证缓存键格式
            foreach ($cacheKeys as $index => $key) {
                $this->assertIsString($key, "Cache key {$index} should be string");
                $this->assertStringContainsString('sdr:task:', $key, "Cache key {$index} should have correct prefix");
                $this->assertStringContainsString((string)$this->testClientId, $key, "Cache key {$index} should contain client ID");
            }
            
            $endTime = microtime(true);
            $totalTime = $endTime - $startTime;
            
            // 性能断言
            $this->assertLessThan(1.0, $totalTime, "Cache performance test should complete within 1s, took {$totalTime}s");
            $this->assertCount($cacheOperations, $cacheKeys, 'Should generate all cache keys');
            
            $this->assertTrue(true, 'Cache performance test passed');
        } catch (\Exception $e) {
            $this->markTestSkipped('Cache performance test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成批量测试数据
     */
    private function generateBatchTestData(int $size): array
    {
        $data = [];
        for ($i = 0; $i < $size; $i++) {
            $data[] = [
                'id' => $i,
                'task_id' => rand(10000, 99999),
                'lead_id' => rand(1000000, 9999999),
                'status' => Constant::DETAIL_STATUS_ADD,
                'stage' => Constant::AI_SDR_STAGE_DIG,
                'quality' => Constant::LEAD_QUALITY_UNKNOWN,
                'data' => [
                    'company_name' => "Test Company {$i}",
                    'email' => "test{$i}@example.com",
                    'products' => ["Product A", "Product B"],
                    'timestamp' => time()
                ]
            ];
        }
        return $data;
    }
    
    /**
     * 处理批量数据
     */
    private function processBatchData(array $batchData): int
    {
        $processed = 0;
        foreach ($batchData as $item) {
            // 模拟数据处理
            if (isset($item['id']) && isset($item['task_id'])) {
                // 验证数据完整性
                $this->assertArrayHasKey('status', $item);
                $this->assertArrayHasKey('stage', $item);
                $this->assertArrayHasKey('data', $item);
                $processed++;
            }
        }
        return $processed;
    }
    
    /**
     * 创建大型数据集
     */
    private function createLargeDataSet(int $size): array
    {
        $dataSet = [];
        for ($i = 0; $i < $size; $i++) {
            $dataSet[] = [
                'index' => $i,
                'data' => str_repeat('x', 1024), // 1KB per item
                'metadata' => [
                    'created' => time(),
                    'type' => 'performance_test',
                    'size' => 1024
                ]
            ];
        }
        return $dataSet;
    }
    
    /**
     * 处理大型数据集
     */
    private function processLargeDataSet(array $dataSet): void
    {
        foreach ($dataSet as $item) {
            // 模拟数据处理
            $processed = strlen($item['data']);
            $this->assertGreaterThan(0, $processed);
        }
    }
}
