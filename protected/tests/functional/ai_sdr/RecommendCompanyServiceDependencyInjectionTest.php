<?php

namespace tests\functional\ai_sdr;

use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\AiSdrTestDataFactory;
use common\library\ai_sdr\RecommendCompanyService;
use common\library\ai_sdr\AiAgentFactory;
use common\library\ai_sdr\Constant;

require_once __DIR__ . '/../../unit/ai_sdr/MockServices.php';

use tests\unit\ai_sdr\MockServiceFactory;

/**
 * RecommendCompanyService依赖注入测试
 * 
 * 测试重构后的RecommendCompanyService是否正确支持依赖注入
 */
class RecommendCompanyServiceDependencyInjectionTest extends AiSdrTestCase
{
    private array $mockServices;
    private RecommendCompanyService $recommendCompanyService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建Mock服务集合
        $this->mockServices = MockServiceFactory::createMockServiceSet();
        
        // 创建使用依赖注入的RecommendCompanyService
        $this->recommendCompanyService = new RecommendCompanyService(
            $this->getTestClientId(),
            $this->getTestUserId(),
            $this->mockServices['recommendApi'],
            $this->mockServices['aiAgentFactory']
        );
    }
    
    /**
     * 测试RecommendCompanyService构造函数的依赖注入
     */
    public function testConstructorDependencyInjection()
    {
        // 验证服务实例正确注入
        $this->assertInstanceOf(RecommendCompanyService::class, $this->recommendCompanyService);
        
        // 通过反射验证依赖注入的服务
        $reflection = new \ReflectionClass($this->recommendCompanyService);
        
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $this->assertSame($this->mockServices['recommendApi'], $recommendApiProperty->getValue($this->recommendCompanyService));
        
        $aiAgentFactoryProperty = $reflection->getProperty('aiAgentFactory');
        $aiAgentFactoryProperty->setAccessible(true);
        $this->assertSame($this->mockServices['aiAgentFactory'], $aiAgentFactoryProperty->getValue($this->recommendCompanyService));
        
        $this->assertTrue(true, 'Dependency injection working correctly');
    }
    
    /**
     * 测试默认构造函数（向后兼容）
     */
    public function testDefaultConstructorBackwardCompatibility()
    {
        // 创建不使用依赖注入的RecommendCompanyService（向后兼容）
        $defaultService = new RecommendCompanyService($this->getTestClientId(), $this->getTestUserId());
        
        $this->assertInstanceOf(RecommendCompanyService::class, $defaultService);
        
        // 验证默认实现被正确创建
        $reflection = new \ReflectionClass($defaultService);
        
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $recommendApi = $recommendApiProperty->getValue($defaultService);
        $this->assertInstanceOf(\common\library\recommend_plaza\RecommendApi::class, $recommendApi);
        
        $aiAgentFactoryProperty = $reflection->getProperty('aiAgentFactory');
        $aiAgentFactoryProperty->setAccessible(true);
        $aiAgentFactory = $aiAgentFactoryProperty->getValue($defaultService);
        $this->assertNull($aiAgentFactory, 'Default service should not inject AI agent factory');
        
        $this->assertTrue(true, 'Backward compatibility maintained');
    }
    
    /**
     * 测试AI Agent工厂的使用
     */
    public function testAiAgentFactoryUsage()
    {
        // 清空调用记录
        $this->mockServices['aiAgentFactory']->clear();
        
        // 通过反射调用受保护的方法
        $reflection = new \ReflectionClass($this->recommendCompanyService);
        
        // 测试Agent工作流服务创建
        $createWorkflowServiceMethod = $reflection->getMethod('createAgentWorkflowService');
        $createWorkflowServiceMethod->setAccessible(true);
        $workflowService = $createWorkflowServiceMethod->invoke($this->recommendCompanyService);
        
        $this->assertNotNull($workflowService);
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createAgentWorkflowService'));
        
        // 测试背景调研Agent创建
        $createBackgroundCheckMethod = $reflection->getMethod('createBackgroundCheckAgent');
        $createBackgroundCheckMethod->setAccessible(true);
        $backgroundCheckAgent = $createBackgroundCheckMethod->invoke($this->recommendCompanyService);
        
        $this->assertNotNull($backgroundCheckAgent);
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createBackgroundCheckAgent'));
        
        // 测试卖家行业分析Agent创建
        $createIndustryAnalyzerMethod = $reflection->getMethod('createSellerIndustryAnalyzer');
        $createIndustryAnalyzerMethod->setAccessible(true);
        $industryAnalyzer = $createIndustryAnalyzerMethod->invoke($this->recommendCompanyService);
        
        $this->assertNotNull($industryAnalyzer);
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createSellerIndustryAnalyzer'));
        
        $this->assertTrue(true, 'AI Agent factory usage working correctly');
    }
    
    /**
     * 测试推荐API的使用
     */
    public function testRecommendApiUsage()
    {
        // 清空调用记录
        $this->mockServices['recommendApi']->clear();
        
        // 配置Mock响应
        $this->mockServices['recommendApi']->setMatchResults([
            'list' => [
                [
                    'id' => 'company_001',
                    'domain' => 'test1.com',
                    'company_name' => 'Test Company 1'
                ]
            ],
            'count' => 1
        ]);
        
        // 通过反射获取recommendApi属性
        $reflection = new \ReflectionClass($this->recommendCompanyService);
        $recommendApiProperty = $reflection->getProperty('recommendApi');
        $recommendApiProperty->setAccessible(true);
        $recommendApi = $recommendApiProperty->getValue($this->recommendCompanyService);
        
        // 直接调用推荐API方法
        $result = $recommendApi->getMatchCompanyByProfile(
            1, // matchType
            [1, 2], // industryIds
            ['Product A'], // industryProducts
            null, // beforePortraitIds
            [], // excludeDomains
            20 // pageSize
        );
        
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertTrue($this->mockServices['recommendApi']->wasMethodCalled('getMatchCompanyByProfile'));
        
        $this->assertTrue(true, 'Recommend API usage working correctly');
    }
    
    /**
     * 测试processRecommend方法
     */
    public function testProcessRecommendMethod()
    {
        // 验证processRecommend方法存在
        $this->assertTrue(method_exists($this->recommendCompanyService, 'processRecommend'));

        // 注意：跳过实际的推荐处理，因为需要数据库连接
        // 在集成测试中可以测试具体的推荐逻辑

        $this->assertTrue(true, 'processRecommend method exists and dependency injection working correctly');
    }
    
    /**
     * 测试getProductGraph方法
     */
    public function testGetProductGraphMethod()
    {
        // 验证getProductGraph方法存在
        $this->assertTrue(method_exists($this->recommendCompanyService, 'getProductGraph'));
        
        // 注意：跳过实际的图谱生成，因为需要数据库数据
        // 在集成测试中可以测试具体的图谱生成
        
        $this->assertTrue(true, 'getProductGraph method exists');
    }
    
    /**
     * 测试Mock服务的调用追踪
     */
    public function testMockServiceCallTracking()
    {
        // 清空所有调用记录
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
        
        // 验证服务调用记录功能正常
        $this->assertIsArray($this->mockServices['recommendApi']->getCallLog());
        $this->assertIsArray($this->mockServices['aiAgentFactory']->getCallLog());
        
        // 执行一些操作来触发服务调用
        $reflection = new \ReflectionClass($this->recommendCompanyService);
        $createWorkflowServiceMethod = $reflection->getMethod('createAgentWorkflowService');
        $createWorkflowServiceMethod->setAccessible(true);
        $createWorkflowServiceMethod->invoke($this->recommendCompanyService);
        
        // 验证调用记录
        $this->assertTrue($this->mockServices['aiAgentFactory']->wasMethodCalled('createAgentWorkflowService'));
        $this->assertEquals(1, count($this->mockServices['aiAgentFactory']->getCallLog()));
        
        $this->assertTrue(true, 'Mock service call tracking working correctly');
    }
    
    protected function tearDown(): void
    {
        // 清理Mock服务状态
        foreach ($this->mockServices as $service) {
            if (method_exists($service, 'clear')) {
                $service->clear();
            }
        }
        
        parent::tearDown();
    }
}
