<?php
/**
 * This file is part of php-crm.
 *
 * <AUTHOR> <<EMAIL>>
 * @created_at  2021/7/8 5:05 下午
 */

namespace common\tests\unit\marketing;

use common\library\cms\builder\render\components\ProductListComponent;
use common\library\cms\builder\render\Render;

class ProductListComponentTest extends \ProjectTestCase
{
    public function dataProvider()
    {
        return [
            [11, 14064],
        ];
    }

    /**
     * @param $siteId
     * @param $clientId
     * @dataProvider dataProvider
     */
    public function testRenderData($siteId, $clientId)
    {
        $render = new Render($siteId);
        $render->setClientId($clientId);

        $productList = new ProductListComponent($render);
        $productList->setGroupId(-1);
        $productList->setCurPage(1);
        $productList->setPageSize(10);
        $productList->setKeyword('');
        var_dump($productList->renderData());

        dd($productList->count());
    }
}
