<?php

namespace common\tests\unit\cache;

use common\library\sns\Constants;
use common\library\sns\customer\CustomerContactList;
use common\library\util\SqlBuilder;
use FunctionalTestCase;
use PgActiveRecord;
use Yii;

class YiiCacheTest extends FunctionalTestCase
{
    /**
     * 测试字符串读取、写入
     * @return void
     */
    public function testStringGetSet():void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->get($key));

        $value = time();
        $cache->set($key, $value);
        $this->assertEquals($cache->get($key), $value);

        $cache->delete($key);
    }

    /**
     * 测试set的NX选项
     * @return void
     */
    public function testSetNx(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('GET', [$key]));

        $value = time();
        $cache->executeCommand('SET', [$key, $value, 'NX']);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        $cache->executeCommand('SET', [$key, "Whatever", 'NX']);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试同时使用EX和NX选项
     * @return void
     */
    public function testSetExNx(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('GET', [$key]));

        $value = time();
        $cache->executeCommand('SET', [$key, $value, 'EX', 1800, 'NX']);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        $cache->executeCommand('SET', [$key, "Whatever", 'EX', 1800, 'NX']);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        // 获取key的过期时间
        $ttl = $cache->executeCommand('TTL', [$key]);
        $this->assertLessThanOrEqual(1800, $ttl);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试字符串删除
     * @return void
     */
    public function testStringDelete(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->set($key, $value);
        $this->assertEquals($cache->get($key), $value);

        $cache->delete($key);
        $this->assertEmpty($cache->get($key));
    }

    /**
     * 测试删除多个key
     * @return void
     */
    public function testDeleteMulti(): void
    {
        $cache = Yii::app()->cache;
        $key1 = "unittest_" . microtime(true);
        $key2 = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('SET', [$key1, $value]);
        $cache->executeCommand('SET', [$key2, $value]);
        $this->assertEquals($cache->executeCommand('GET', [$key1]), $value);
        $this->assertEquals($cache->executeCommand('GET', [$key2]), $value);

        $cache->executeCommand('DEL', [$key1, $key2]);
        $this->assertEmpty($cache->executeCommand('GET', [$key1]));
        $this->assertEmpty($cache->executeCommand('GET', [$key2]));
    }

    /**
     * 测试字符串过期
     * @return void
     */
    public function testStringExpire(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->set($key, $value, 2);
        $this->assertEquals($cache->get($key), $value);

        // 1秒后未过期
        sleep(1);
        $this->assertEquals($cache->get($key), $value);

        // 3秒后已过期
        sleep(2);
        $this->assertEmpty($cache->get($key));
    }

    /**
     * 测试字符串自增
     * @return void
     * @throws
     */
    public function testStringIncr(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('SET', [$key, $value]);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        $cache->executeCommand('INCR', [$key]);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value + 1);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试字符串自减
     * @return void
     * @throws
     */
    public function testStringDecr(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('SET', [$key, $value]);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value);

        $cache->executeCommand('DECR', [$key]);
        $this->assertEquals($cache->executeCommand('GET', [$key]), $value - 1);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试哈希获取（单个字段）
     * @return void
     */
    public function testHashGet_SingleField(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('HGETALL', [$key]));

        $value = time();
        $cache->executeCommand('HMSET', [$key, 'field', $value]);
        $this->assertEquals($cache->executeCommand('HGET', [$key, 'field']), $value);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试哈希获取（多个字段）
     * @return void
     */
    public function testHashGet_MultiField(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('HGETALL', [$key]));

        $value = time();
        $cache->executeCommand('HMSET', [$key, 'field1', $value, 'field2', $value]);
        $this->assertEquals($cache->executeCommand('HMGET', [$key, 'field1', 'field2']), [$value, $value]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试获取哈希的值
     */
    public function testHashGetValues(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('HGETALL', [$key]));

        $value = time();
        $cache->executeCommand('HMSET', [$key, 'field1', $value, 'field2', $value]);
        $this->assertEquals($cache->executeCommand('HVALS', [$key]), [$value, $value]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试哈希删除
     * @return void
     */
    public function testHashDelete(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('HMSET', [$key, 'field', $value]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value]);

        $cache->executeCommand('HDEL', [$key, 'field']);
        $this->assertEmpty($cache->executeCommand('HGETALL', [$key]));
    }

    /**
     * 测试哈希过期
     * @return void
     */
    public function testHashExpire(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('HMSET', [$key, 'field', $value]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value]);

        $cache->executeCommand('EXPIRE', [$key, 2]);

        // 1秒后未过期
        sleep(1);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value]);

        // 3秒后已过期
        sleep(2);
        $this->assertEmpty($cache->executeCommand('HGETALL', [$key]));
    }

    /**
     * 测试哈希自增
     * @return void
     * @throws
     */
    public function testHashIncr(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('HMSET', [$key, 'field', $value]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value]);

        $cache->executeCommand('HINCRBY', [$key, 'field', 1]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value + 1]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试哈希自减
     * @return void
     * @throws
     */
    public function testHashDecr(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('HMSET', [$key, 'field', $value]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value]);

        $cache->executeCommand('HINCRBY', [$key, 'field', -1]);
        $this->assertEquals($cache->executeCommand('HGETALL', [$key]), ['field', $value - 1]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试列表读取、写入
     * @return void
     */
    public function testListGetSet(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('LRANGE', [$key, 0, -1]));

        $value = time();
        $cache->executeCommand('RPUSH', [$key, $value]);
        $this->assertEquals($cache->executeCommand('LRANGE', [$key, 0, -1]), [$value]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试列表删除
     * @return void
     */
    public function testListDelete(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('RPUSH', [$key, $value]);
        $this->assertEquals($cache->executeCommand('LRANGE', [$key, 0, -1]), [$value]);

        $cache->executeCommand('LPOP', [$key]);
        $this->assertEmpty($cache->executeCommand('LRANGE', [$key, 0, -1]));
    }

    /**
     * 测试列表过期
     * @return void
     */
    public function testListExpire(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('RPUSH', [$key, $value]);
        $this->assertEquals($cache->executeCommand('LRANGE', [$key, 0, -1]), [$value]);

        $cache->executeCommand('EXPIRE', [$key, 2]);

        // 1秒后未过期
        sleep(1);
        $this->assertEquals($cache->executeCommand('LRANGE', [$key, 0, -1]), [$value]);

        // 3秒后已过期
        sleep(2);
        $this->assertEmpty($cache->executeCommand('LRANGE', [$key, 0, -1]));
    }

    /**
     * 测试集合读取、写入
     */
    public function testSetGetSet(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEmpty($cache->executeCommand('SMEMBERS', [$key]));

        $value = time();
        $cache->executeCommand('SADD', [$key, $value]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试集合删除元素
     * @return void
     */
    public function testSetDelete(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('SADD', [$key, $value]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        $cache->executeCommand('SREM', [$key, $value]);
        $this->assertEmpty($cache->executeCommand('SMEMBERS', [$key]));
    }

    /**
     * 测试集合过期
     * @return void
     */
    public function testSetExpire(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = time();
        $cache->executeCommand('SADD', [$key, $value]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        $cache->executeCommand('EXPIRE', [$key, 2]);

        // 1秒后未过期
        sleep(1);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        // 3秒后已过期
        sleep(2);
        $this->assertEmpty($cache->executeCommand('SMEMBERS', [$key]));
    }

    /**
     * 测试集合增加元素
     * @return void
     * @throws
     */
    public function testSetAdd(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('SADD', [$key, $value]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        $cache->executeCommand('SADD', [$key, $value + 1]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value, $value + 1]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试集合是否存在元素
     * @return void
     * @throws
     */
    public function testSetIsMember(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $value = random_int(1, 100000);
        $cache->executeCommand('SADD', [$key, $value]);
        $this->assertEquals($cache->executeCommand('SMEMBERS', [$key]), [$value]);

        $this->assertEquals($cache->executeCommand('SISMEMBER', [$key, $value]), 1);
        $this->assertEquals($cache->executeCommand('SISMEMBER', [$key, $value + 1]), 0);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试集合随机获取元素
     * @return void
     * @throws
     */
    public function testSetRandomMember(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        // 向集合写入多个元素
        $value = random_int(1, 100000);
        $cache->executeCommand('SADD', [$key, $value]);
        $cache->executeCommand('SADD', [$key, $value + 1]);
        $cache->executeCommand('SADD', [$key, $value + 2]);

        $this->assertContains($cache->executeCommand('SRANDMEMBER', [$key]), [$value, $value + 1, $value + 2]);

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试集合随机获取多个元素
     * @return void
     * @throws
     */
    public function testSetRandomMembers(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        // 向集合写入多个元素
        $value = random_int(1, 100000);
        $cache->executeCommand('SADD', [$key, $value]);
        $cache->executeCommand('SADD', [$key, $value + 1]);
        $cache->executeCommand('SADD', [$key, $value + 2]);

        $result = $cache->executeCommand('SRANDMEMBER', [$key, 2]);
        $this->assertEmpty(array_diff($result, [$value, $value + 1, $value + 2]));

        $cache->executeCommand('DEL', [$key]);
    }

    /**
     * 测试位图读取、写入
     * @return void
     */
    public function testBitMapGetSet(): void
    {
        $cache = Yii::app()->cache;
        $key = "unittest_" . microtime(true);

        $this->assertEquals($cache->executeCommand('GETBIT', [$key, 0]), 0);

        $cache->executeCommand('SETBIT', [$key, 0, 1]);
        $this->assertEquals($cache->executeCommand('GETBIT', [$key, 0]), 1);

        $cache->executeCommand('DEL', [$key]);
    }
}