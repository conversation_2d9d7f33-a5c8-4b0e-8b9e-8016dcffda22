<?php

namespace tests\unit\ai_sdr;

use common\library\ai_sdr\Constant;

/**
 * Mock AI Agent实现集合
 * 
 * 提供各种AI Agent的Mock实现，用于测试
 */

/**
 * Mock质量分析Agent
 */
class MockQualityAnalysisAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return $this->mockAiServices->getQualityAnalysisResult();
    }
}

/**
 * Mock卖家行业分析Agent
 */
class MockSellerIndustryAnalyzer
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return [
            'answer' => [
                'industry_analysis' => [
                    'primary_industry' => 'Technology',
                    'secondary_industries' => ['Manufacturing', 'Export'],
                    'confidence' => 0.85
                ]
            ],
            'record_id' => 12345
        ];
    }
}

/**
 * Mock EDM写作Agent
 */
class MockEdmWriteAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return $this->mockAiServices->getMarketingContentResult();
    }
}

/**
 * Mock Agent工作流服务
 */
class MockAgentWorkflowService
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function run(string $workflow, array $params): array
    {
        switch ($workflow) {
            case 'generate_product_usage':
                return [
                    'product_usage' => [
                        [
                            'source_product' => 'Solar Panel',
                            'target_industry' => 'Renewable Energy',
                            'usage_type' => 'Manufacturing',
                            'confidence' => 0.9
                        ]
                    ],
                    'record_id' => 12345
                ];
            default:
                return [
                    'result' => 'Mock workflow result for ' . $workflow,
                    'record_id' => 12345
                ];
        }
    }
}

/**
 * Mock背景调研Agent
 */
class MockBackgroundCheckAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return $this->mockAiServices->getBackgroundCheckResult();
    }
}

/**
 * Mock产品分类Agent
 */
class MockProductCategoryAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return [
            'answer' => [
                'categories' => [
                    'Electronics → Consumer Electronics → Mobile Phones',
                    'Technology → Software → Business Software'
                ],
                'confidence' => 0.88
            ],
            'record_id' => 12345
        ];
    }
}

/**
 * Mock卖家档案Agent
 */
class MockSellerProfileAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return [
            'answer' => [
                'company_profile' => [
                    'company_name' => 'Mock Technology Co., Ltd.',
                    'industry' => 'Technology',
                    'main_products' => ['Software', 'Hardware'],
                    'company_type' => ['Manufacturer', 'Exporter'],
                    'employees' => '100-500',
                    'established_year' => '2010'
                ],
                'confidence' => 0.92
            ],
            'record_id' => 12345
        ];
    }
}

/**
 * Mock主页分析Agent
 */
class MockHomepageAgent
{
    private MockAiServices $mockAiServices;
    
    public function __construct(MockAiServices $mockAiServices)
    {
        $this->mockAiServices = $mockAiServices;
    }
    
    public function process(array $input): array
    {
        return [
            'answer' => [
                'homepage_analysis' => [
                    'company_info' => [
                        'name' => 'Mock Company from Homepage',
                        'description' => 'A leading technology company',
                        'contact_info' => [
                            'email' => '<EMAIL>',
                            'phone' => '******-0123'
                        ]
                    ],
                    'products' => ['Product A', 'Product B'],
                    'services' => ['Service X', 'Service Y']
                ],
                'confidence' => 0.87
            ],
            'record_id' => 12345
        ];
    }
}
