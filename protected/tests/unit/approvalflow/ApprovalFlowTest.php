<?php
/**
 * Author: nuxse
 * Data: 2019/12/10
 */

/**
 * Author: nuxse
 * Data: 2019/11/13
 */

class ApprovalFlowTest extends ProjectTestCase
{
    public function setUp(): void
    {
        parent::setUp();
        $this->loginUser(765);
    }

    public function dataProvider()
    {
        return [
            [
                'approval_test_title'.rand(1,1000),
                \common\library\approval_flow\Constants::ENTITY_TYPE_ORDER,
                \common\library\approval_flow\Constants::ENTITY_EVENT_CREATE,
                '',
                \common\library\workflow\WorkflowConstant::CRITERIA_TYPE_NULL,
                'approval_test_description',
                '',
                [],
                []
            ]
        ];
    }


    //public function testCreate

    public function testMultiViewMultiDecide()
    {

    }

    public static function tearDownAfterClass(): void
    {
        $approvalFlow = static::$approvalFlow;
        $db = \ProjectActiveRecord::getDbByUserId(static::$userId);
        $sql = "DELETE FROM tbl_approval_flow_base_config WHERE  approval_flow_id={$approvalFlow->approval_flow_id}";
        $result = $db->createCommand($sql)->execute();
        parent::tearDownAfterClass();
    }

    public function testOpportunityChangeStage()
    {
        \common\library\opportunity\Opportunity::$enableForApprovalFlow = true;
        \User::setLoginUserByAccount('<EMAIL>');

        $user = User::getLoginUser();

        $opportunity_id = **********;
        $stage_id = **********;
        $stage = (new \common\library\setting\library\stage\StageApi($user->getClientId()))->find($stage_id);
        $opportunity = new \common\library\opportunity\Opportunity($user->getClientId(), $opportunity_id);
        $opportunity->setUserId($user->getUserId());
        $opportunity->changeStage($stage_id,$stage['flow_id']);
    }

    public function testMail()
    {
        \common\library\mail\Mail::$enableForApprovalFlow = true;
        \User::setLoginUserByAccount('<EMAIL>');
        $user = User::getLoginUser();

        $mailId = **********;
        $mail = new \common\library\mail\Mail($mailId, $user->getUserId());

        $signal = new \common\library\approval_flow\signal\interrupt\MailSendInterrupt(
            $mail->user_id,
            $mail->mail_id,
            \common\library\approval_flow\Constants::ENTITY_TYPE_MAIL,
            $mail->subject,
            $mail->getUpdateFields(),
            ['mail_id' => $mail->mail_id,],
            $mail
        );
        $mail->runApprovalFlow($signal);
    }
}