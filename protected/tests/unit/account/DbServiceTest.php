<?php
/**
 * Created by Ph<PERSON>Stor<PERSON>.
 * User: cayley
 * Date: 2019/9/10
 * Time: 5:27 PM
 */

class DbServiceTest extends ProjectTestCase
{

    public function testGetDefaultDbSetId()
    {
        $versions = [\common\library\privilege_v3\PrivilegeConstants::OKKI_ALI_BASIC_ID];
        if (in_array(\common\library\privilege_v3\PrivilegeConstants::CRM_PRO_SYSTEM_ID, $versions)) {
            $module = \common\library\account\service\DbService::MODULE_PRO;
        } elseif (in_array(\common\library\privilege_v3\PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_ID, $versions)) {
            $module = \common\library\account\service\DbService::MODULE_PERSONAL;
        } elseif (in_array(\common\library\privilege_v3\PrivilegeConstants::OKKI_PERSONAL_LEADS_AI_GLOBAL_BASIC_ID, $versions)) {
            $module = \common\library\account\service\DbService::MODULE_PERSONAL_GLOBAL;
        } elseif (in_array(\common\library\privilege_v3\PrivilegeConstants::OKKI_ALI_BASIC_ID, $versions)) {
            $module = \common\library\account\service\DbService::MODULE_ALI_BASIC;
        } else {
            $module = \common\library\account\service\DbService::MODULE_V5;
        }

        list($mysql, $pg) = \common\library\account\service\DbService::getDefaultDbSetId($module);
        dd($mysql, $pg);
    }

    public function testPrefix()
    {
//        $prefix = \common\library\account\service\DbService::getDbPrefix(1, \common\library\account\service\DbService::MODULE_PERSONAL);
        $prefix = \common\library\account\service\DbService::getDbPrefix(1, \common\library\account\service\DbService::MODULE_ALI_BASIC);
        dd($prefix);
    }

    public function testDbProxy()
    {
        $db = ProjectActiveRecord::getDbByDbSetId(16);
        $res = $db->createCommand("select * from tbl_item_setting limit 1")->queryAll();
        var_dump($res);
        $res = $db->createCommand("select * from tbl_notification limit 1")->queryAll();
        var_dump($res);
    }
}
