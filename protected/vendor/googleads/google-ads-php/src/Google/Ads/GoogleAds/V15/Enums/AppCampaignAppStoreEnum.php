<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/enums/app_campaign_app_store.proto

namespace Google\Ads\GoogleAds\V15\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The application store that distributes mobile applications.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.enums.AppCampaignAppStoreEnum</code>
 */
class AppCampaignAppStoreEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Enums\AppCampaignAppStore::initOnce();
        parent::__construct($data);
    }

}

