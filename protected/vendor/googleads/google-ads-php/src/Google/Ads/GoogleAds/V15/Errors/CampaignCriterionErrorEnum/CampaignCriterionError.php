<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/errors/campaign_criterion_error.proto

namespace Google\Ads\GoogleAds\V15\Errors\CampaignCriterionErrorEnum;

use UnexpectedValueException;

/**
 * Enum describing possible campaign criterion errors.
 *
 * Protobuf type <code>google.ads.googleads.v15.errors.CampaignCriterionErrorEnum.CampaignCriterionError</code>
 */
class CampaignCriterionError
{
    /**
     * Enum unspecified.
     *
     * Generated from protobuf enum <code>UNSPECIFIED = 0;</code>
     */
    const UNSPECIFIED = 0;
    /**
     * The received error code is not known in this version.
     *
     * Generated from protobuf enum <code>UNKNOWN = 1;</code>
     */
    const UNKNOWN = 1;
    /**
     * Concrete type of criterion (keyword v.s. placement) is required for
     * CREATE and UPDATE operations.
     *
     * Generated from protobuf enum <code>CONCRETE_TYPE_REQUIRED = 2;</code>
     */
    const CONCRETE_TYPE_REQUIRED = 2;
    /**
     * Invalid placement URL.
     *
     * Generated from protobuf enum <code>INVALID_PLACEMENT_URL = 3;</code>
     */
    const INVALID_PLACEMENT_URL = 3;
    /**
     * Criteria type can not be excluded for the campaign by the customer. like
     * AOL account type cannot target site type criteria
     *
     * Generated from protobuf enum <code>CANNOT_EXCLUDE_CRITERIA_TYPE = 4;</code>
     */
    const CANNOT_EXCLUDE_CRITERIA_TYPE = 4;
    /**
     * Cannot set the campaign criterion status for this criteria type.
     *
     * Generated from protobuf enum <code>CANNOT_SET_STATUS_FOR_CRITERIA_TYPE = 5;</code>
     */
    const CANNOT_SET_STATUS_FOR_CRITERIA_TYPE = 5;
    /**
     * Cannot set the campaign criterion status for an excluded criteria.
     *
     * Generated from protobuf enum <code>CANNOT_SET_STATUS_FOR_EXCLUDED_CRITERIA = 6;</code>
     */
    const CANNOT_SET_STATUS_FOR_EXCLUDED_CRITERIA = 6;
    /**
     * Cannot target and exclude the same criterion.
     *
     * Generated from protobuf enum <code>CANNOT_TARGET_AND_EXCLUDE = 7;</code>
     */
    const CANNOT_TARGET_AND_EXCLUDE = 7;
    /**
     * The mutate contained too many operations.
     *
     * Generated from protobuf enum <code>TOO_MANY_OPERATIONS = 8;</code>
     */
    const TOO_MANY_OPERATIONS = 8;
    /**
     * This operator cannot be applied to a criterion of this type.
     *
     * Generated from protobuf enum <code>OPERATOR_NOT_SUPPORTED_FOR_CRITERION_TYPE = 9;</code>
     */
    const OPERATOR_NOT_SUPPORTED_FOR_CRITERION_TYPE = 9;
    /**
     * The Shopping campaign sales country is not supported for
     * ProductSalesChannel targeting.
     *
     * Generated from protobuf enum <code>SHOPPING_CAMPAIGN_SALES_COUNTRY_NOT_SUPPORTED_FOR_SALES_CHANNEL = 10;</code>
     */
    const SHOPPING_CAMPAIGN_SALES_COUNTRY_NOT_SUPPORTED_FOR_SALES_CHANNEL = 10;
    /**
     * The existing field can't be updated with CREATE operation. It can be
     * updated with UPDATE operation only.
     *
     * Generated from protobuf enum <code>CANNOT_ADD_EXISTING_FIELD = 11;</code>
     */
    const CANNOT_ADD_EXISTING_FIELD = 11;
    /**
     * Negative criteria are immutable, so updates are not allowed.
     *
     * Generated from protobuf enum <code>CANNOT_UPDATE_NEGATIVE_CRITERION = 12;</code>
     */
    const CANNOT_UPDATE_NEGATIVE_CRITERION = 12;
    /**
     * Only free form names are allowed for negative Smart campaign keyword
     * theme.
     *
     * Generated from protobuf enum <code>CANNOT_SET_NEGATIVE_KEYWORD_THEME_CONSTANT_CRITERION = 13;</code>
     */
    const CANNOT_SET_NEGATIVE_KEYWORD_THEME_CONSTANT_CRITERION = 13;
    /**
     * Invalid Smart campaign keyword theme constant criterion.
     *
     * Generated from protobuf enum <code>INVALID_KEYWORD_THEME_CONSTANT = 14;</code>
     */
    const INVALID_KEYWORD_THEME_CONSTANT = 14;
    /**
     * A Smart campaign keyword theme constant or free-form Smart campaign
     * keyword theme is required.
     *
     * Generated from protobuf enum <code>MISSING_KEYWORD_THEME_CONSTANT_OR_FREE_FORM_KEYWORD_THEME = 15;</code>
     */
    const MISSING_KEYWORD_THEME_CONSTANT_OR_FREE_FORM_KEYWORD_THEME = 15;
    /**
     * A Smart campaign may not target proximity and location criteria
     * simultaneously.
     *
     * Generated from protobuf enum <code>CANNOT_TARGET_BOTH_PROXIMITY_AND_LOCATION_CRITERIA_FOR_SMART_CAMPAIGN = 16;</code>
     */
    const CANNOT_TARGET_BOTH_PROXIMITY_AND_LOCATION_CRITERIA_FOR_SMART_CAMPAIGN = 16;
    /**
     * A Smart campaign may not target multiple proximity criteria.
     *
     * Generated from protobuf enum <code>CANNOT_TARGET_MULTIPLE_PROXIMITY_CRITERIA_FOR_SMART_CAMPAIGN = 17;</code>
     */
    const CANNOT_TARGET_MULTIPLE_PROXIMITY_CRITERIA_FOR_SMART_CAMPAIGN = 17;
    /**
     * Location is not launched for Local Services Campaigns.
     *
     * Generated from protobuf enum <code>LOCATION_NOT_LAUNCHED_FOR_LOCAL_SERVICES_CAMPAIGN = 18;</code>
     */
    const LOCATION_NOT_LAUNCHED_FOR_LOCAL_SERVICES_CAMPAIGN = 18;
    /**
     * A Local Services campaign may not target certain criteria types.
     *
     * Generated from protobuf enum <code>LOCATION_INVALID_FOR_LOCAL_SERVICES_CAMPAIGN = 19;</code>
     */
    const LOCATION_INVALID_FOR_LOCAL_SERVICES_CAMPAIGN = 19;
    /**
     * Country locations are not supported for Local Services campaign.
     *
     * Generated from protobuf enum <code>CANNOT_TARGET_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN = 20;</code>
     */
    const CANNOT_TARGET_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN = 20;
    /**
     * Location is not within the home country of Local Services campaign.
     *
     * Generated from protobuf enum <code>LOCATION_NOT_IN_HOME_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN = 21;</code>
     */
    const LOCATION_NOT_IN_HOME_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN = 21;
    /**
     * Local Services profile does not exist for a particular Local Services
     * campaign.
     *
     * Generated from protobuf enum <code>CANNOT_ADD_OR_REMOVE_LOCATION_FOR_LOCAL_SERVICES_CAMPAIGN = 22;</code>
     */
    const CANNOT_ADD_OR_REMOVE_LOCATION_FOR_LOCAL_SERVICES_CAMPAIGN = 22;
    /**
     * Local Services campaign must have at least one target location.
     *
     * Generated from protobuf enum <code>AT_LEAST_ONE_POSITIVE_LOCATION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN = 23;</code>
     */
    const AT_LEAST_ONE_POSITIVE_LOCATION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN = 23;
    /**
     * At least one positive local service ID criterion is required for a Local
     * Services campaign.
     *
     * Generated from protobuf enum <code>AT_LEAST_ONE_LOCAL_SERVICE_ID_CRITERION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN = 24;</code>
     */
    const AT_LEAST_ONE_LOCAL_SERVICE_ID_CRITERION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN = 24;
    /**
     * Local service ID is not found under selected categories in local
     * services campaign setting.
     *
     * Generated from protobuf enum <code>LOCAL_SERVICE_ID_NOT_FOUND_FOR_CATEGORY = 25;</code>
     */
    const LOCAL_SERVICE_ID_NOT_FOUND_FOR_CATEGORY = 25;
    /**
     * For search advertising channel, brand lists can only be applied to
     * exclusive targeting, broad match campaigns for inclusive targeting or
     * PMax generated campaigns.
     *
     * Generated from protobuf enum <code>CANNOT_ATTACH_BRAND_LIST_TO_NON_QUALIFIED_SEARCH_CAMPAIGN = 26;</code>
     */
    const CANNOT_ATTACH_BRAND_LIST_TO_NON_QUALIFIED_SEARCH_CAMPAIGN = 26;

    private static $valueToName = [
        self::UNSPECIFIED => 'UNSPECIFIED',
        self::UNKNOWN => 'UNKNOWN',
        self::CONCRETE_TYPE_REQUIRED => 'CONCRETE_TYPE_REQUIRED',
        self::INVALID_PLACEMENT_URL => 'INVALID_PLACEMENT_URL',
        self::CANNOT_EXCLUDE_CRITERIA_TYPE => 'CANNOT_EXCLUDE_CRITERIA_TYPE',
        self::CANNOT_SET_STATUS_FOR_CRITERIA_TYPE => 'CANNOT_SET_STATUS_FOR_CRITERIA_TYPE',
        self::CANNOT_SET_STATUS_FOR_EXCLUDED_CRITERIA => 'CANNOT_SET_STATUS_FOR_EXCLUDED_CRITERIA',
        self::CANNOT_TARGET_AND_EXCLUDE => 'CANNOT_TARGET_AND_EXCLUDE',
        self::TOO_MANY_OPERATIONS => 'TOO_MANY_OPERATIONS',
        self::OPERATOR_NOT_SUPPORTED_FOR_CRITERION_TYPE => 'OPERATOR_NOT_SUPPORTED_FOR_CRITERION_TYPE',
        self::SHOPPING_CAMPAIGN_SALES_COUNTRY_NOT_SUPPORTED_FOR_SALES_CHANNEL => 'SHOPPING_CAMPAIGN_SALES_COUNTRY_NOT_SUPPORTED_FOR_SALES_CHANNEL',
        self::CANNOT_ADD_EXISTING_FIELD => 'CANNOT_ADD_EXISTING_FIELD',
        self::CANNOT_UPDATE_NEGATIVE_CRITERION => 'CANNOT_UPDATE_NEGATIVE_CRITERION',
        self::CANNOT_SET_NEGATIVE_KEYWORD_THEME_CONSTANT_CRITERION => 'CANNOT_SET_NEGATIVE_KEYWORD_THEME_CONSTANT_CRITERION',
        self::INVALID_KEYWORD_THEME_CONSTANT => 'INVALID_KEYWORD_THEME_CONSTANT',
        self::MISSING_KEYWORD_THEME_CONSTANT_OR_FREE_FORM_KEYWORD_THEME => 'MISSING_KEYWORD_THEME_CONSTANT_OR_FREE_FORM_KEYWORD_THEME',
        self::CANNOT_TARGET_BOTH_PROXIMITY_AND_LOCATION_CRITERIA_FOR_SMART_CAMPAIGN => 'CANNOT_TARGET_BOTH_PROXIMITY_AND_LOCATION_CRITERIA_FOR_SMART_CAMPAIGN',
        self::CANNOT_TARGET_MULTIPLE_PROXIMITY_CRITERIA_FOR_SMART_CAMPAIGN => 'CANNOT_TARGET_MULTIPLE_PROXIMITY_CRITERIA_FOR_SMART_CAMPAIGN',
        self::LOCATION_NOT_LAUNCHED_FOR_LOCAL_SERVICES_CAMPAIGN => 'LOCATION_NOT_LAUNCHED_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::LOCATION_INVALID_FOR_LOCAL_SERVICES_CAMPAIGN => 'LOCATION_INVALID_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::CANNOT_TARGET_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN => 'CANNOT_TARGET_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::LOCATION_NOT_IN_HOME_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN => 'LOCATION_NOT_IN_HOME_COUNTRY_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::CANNOT_ADD_OR_REMOVE_LOCATION_FOR_LOCAL_SERVICES_CAMPAIGN => 'CANNOT_ADD_OR_REMOVE_LOCATION_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::AT_LEAST_ONE_POSITIVE_LOCATION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN => 'AT_LEAST_ONE_POSITIVE_LOCATION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::AT_LEAST_ONE_LOCAL_SERVICE_ID_CRITERION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN => 'AT_LEAST_ONE_LOCAL_SERVICE_ID_CRITERION_REQUIRED_FOR_LOCAL_SERVICES_CAMPAIGN',
        self::LOCAL_SERVICE_ID_NOT_FOUND_FOR_CATEGORY => 'LOCAL_SERVICE_ID_NOT_FOUND_FOR_CATEGORY',
        self::CANNOT_ATTACH_BRAND_LIST_TO_NON_QUALIFIED_SEARCH_CAMPAIGN => 'CANNOT_ATTACH_BRAND_LIST_TO_NON_QUALIFIED_SEARCH_CAMPAIGN',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CampaignCriterionError::class, \Google\Ads\GoogleAds\V15\Errors\CampaignCriterionErrorEnum_CampaignCriterionError::class);

