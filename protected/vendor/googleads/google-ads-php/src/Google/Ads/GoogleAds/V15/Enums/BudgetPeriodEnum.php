<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/enums/budget_period.proto

namespace Google\Ads\GoogleAds\V15\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Message describing Budget period.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.enums.BudgetPeriodEnum</code>
 */
class BudgetPeriodEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Enums\BudgetPeriod::initOnce();
        parent::__construct($data);
    }

}

