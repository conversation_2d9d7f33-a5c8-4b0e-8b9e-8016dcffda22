<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/billing_setup_error.proto

namespace Google\Ads\GoogleAds\V16\Errors;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible billing setup errors.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.errors.BillingSetupErrorEnum</code>
 */
class BillingSetupErrorEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Errors\BillingSetupError::initOnce();
        parent::__construct($data);
    }

}

