<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/location_group_radius_units.proto

namespace Google\Ads\GoogleAds\V17\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing unit of radius in location group.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.enums.LocationGroupRadiusUnitsEnum</code>
 */
class LocationGroupRadiusUnitsEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Enums\LocationGroupRadiusUnits::initOnce();
        parent::__construct($data);
    }

}

