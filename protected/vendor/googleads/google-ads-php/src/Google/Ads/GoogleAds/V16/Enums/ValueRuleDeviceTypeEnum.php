<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/value_rule_device_type.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible device types used in a conversion
 * value rule.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.ValueRuleDeviceTypeEnum</code>
 */
class ValueRuleDeviceTypeEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\ValueRuleDeviceType::initOnce();
        parent::__construct($data);
    }

}

