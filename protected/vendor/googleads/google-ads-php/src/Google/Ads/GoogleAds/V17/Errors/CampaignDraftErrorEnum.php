<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/campaign_draft_error.proto

namespace Google\Ads\GoogleAds\V17\Errors;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible campaign draft errors.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.errors.CampaignDraftErrorEnum</code>
 */
class CampaignDraftErrorEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Errors\CampaignDraftError::initOnce();
        parent::__construct($data);
    }

}

