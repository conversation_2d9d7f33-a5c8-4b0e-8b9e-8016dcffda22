<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/user_list_customer_type_category.proto

namespace Google\Ads\GoogleAds\V17\Enums\UserListCustomerTypeCategoryEnum;

use UnexpectedValueException;

/**
 * Enum containing possible user list customer type categories.
 *
 * Protobuf type <code>google.ads.googleads.v17.enums.UserListCustomerTypeCategoryEnum.UserListCustomerTypeCategory</code>
 */
class UserListCustomerTypeCategory
{
    /**
     * Not specified.
     *
     * Generated from protobuf enum <code>UNSPECIFIED = 0;</code>
     */
    const UNSPECIFIED = 0;
    /**
     * Unknown type.
     *
     * Generated from protobuf enum <code>UNKNOWN = 1;</code>
     */
    const UNKNOWN = 1;
    /**
     * Customer type category for all customers.
     *
     * Generated from protobuf enum <code>ALL_CUSTOMERS = 2;</code>
     */
    const ALL_CUSTOMERS = 2;
    /**
     * Customer type category for all purchasers.
     *
     * Generated from protobuf enum <code>PURCHASERS = 3;</code>
     */
    const PURCHASERS = 3;
    /**
     * Customer type category for high value purchasers.
     *
     * Generated from protobuf enum <code>HIGH_VALUE_CUSTOMERS = 4;</code>
     */
    const HIGH_VALUE_CUSTOMERS = 4;
    /**
     * Customer type category for disengaged purchasers.
     *
     * Generated from protobuf enum <code>DISENGAGED_CUSTOMERS = 5;</code>
     */
    const DISENGAGED_CUSTOMERS = 5;
    /**
     * Customer type category for qualified leads.
     *
     * Generated from protobuf enum <code>QUALIFIED_LEADS = 6;</code>
     */
    const QUALIFIED_LEADS = 6;
    /**
     * Customer type category for converted leads.
     *
     * Generated from protobuf enum <code>CONVERTED_LEADS = 7;</code>
     */
    const CONVERTED_LEADS = 7;
    /**
     * Customer type category for paid subscribers.
     *
     * Generated from protobuf enum <code>PAID_SUBSCRIBERS = 8;</code>
     */
    const PAID_SUBSCRIBERS = 8;
    /**
     * Customer type category for loyalty signups.
     *
     * Generated from protobuf enum <code>LOYALTY_SIGN_UPS = 9;</code>
     */
    const LOYALTY_SIGN_UPS = 9;
    /**
     * Customer type category for cart abandoners.
     *
     * Generated from protobuf enum <code>CART_ABANDONERS = 10;</code>
     */
    const CART_ABANDONERS = 10;

    private static $valueToName = [
        self::UNSPECIFIED => 'UNSPECIFIED',
        self::UNKNOWN => 'UNKNOWN',
        self::ALL_CUSTOMERS => 'ALL_CUSTOMERS',
        self::PURCHASERS => 'PURCHASERS',
        self::HIGH_VALUE_CUSTOMERS => 'HIGH_VALUE_CUSTOMERS',
        self::DISENGAGED_CUSTOMERS => 'DISENGAGED_CUSTOMERS',
        self::QUALIFIED_LEADS => 'QUALIFIED_LEADS',
        self::CONVERTED_LEADS => 'CONVERTED_LEADS',
        self::PAID_SUBSCRIBERS => 'PAID_SUBSCRIBERS',
        self::LOYALTY_SIGN_UPS => 'LOYALTY_SIGN_UPS',
        self::CART_ABANDONERS => 'CART_ABANDONERS',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(UserListCustomerTypeCategory::class, \Google\Ads\GoogleAds\V17\Enums\UserListCustomerTypeCategoryEnum_UserListCustomerTypeCategory::class);

