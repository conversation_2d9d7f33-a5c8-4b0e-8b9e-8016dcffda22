<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/common/asset_types.proto

namespace Google\Ads\GoogleAds\V16\Common;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A call to action asset.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.common.CallToActionAsset</code>
 */
class CallToActionAsset extends \Google\Protobuf\Internal\Message
{
    /**
     * Call to action.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType call_to_action = 1;</code>
     */
    protected $call_to_action = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $call_to_action
     *           Call to action.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Common\AssetTypes::initOnce();
        parent::__construct($data);
    }

    /**
     * Call to action.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType call_to_action = 1;</code>
     * @return int
     */
    public function getCallToAction()
    {
        return $this->call_to_action;
    }

    /**
     * Call to action.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType call_to_action = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setCallToAction($var)
    {
        GPBUtil::checkEnum($var, \Google\Ads\GoogleAds\V16\Enums\CallToActionTypeEnum\CallToActionType::class);
        $this->call_to_action = $var;

        return $this;
    }

}

