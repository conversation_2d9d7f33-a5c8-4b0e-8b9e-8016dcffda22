<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/conversion_custom_variable_status.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible statuses of a conversion custom
 * variable.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.ConversionCustomVariableStatusEnum</code>
 */
class ConversionCustomVariableStatusEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\ConversionCustomVariableStatus::initOnce();
        parent::__construct($data);
    }

}

