<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/common/matching_function.proto

namespace Google\Ads\GoogleAds\V17\Common\Operand;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A constant operand in a matching function.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.common.Operand.ConstantOperand</code>
 */
class ConstantOperand extends \Google\Protobuf\Internal\Message
{
    protected $constant_operand_value;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $string_value
     *           String value of the operand if it is a string type.
     *     @type int|string $long_value
     *           Int64 value of the operand if it is a int64 type.
     *     @type bool $boolean_value
     *           Boolean value of the operand if it is a boolean type.
     *     @type float $double_value
     *           Double value of the operand if it is a double type.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Common\MatchingFunction::initOnce();
        parent::__construct($data);
    }

    /**
     * String value of the operand if it is a string type.
     *
     * Generated from protobuf field <code>string string_value = 5;</code>
     * @return string
     */
    public function getStringValue()
    {
        return $this->readOneof(5);
    }

    public function hasStringValue()
    {
        return $this->hasOneof(5);
    }

    /**
     * String value of the operand if it is a string type.
     *
     * Generated from protobuf field <code>string string_value = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setStringValue($var)
    {
        GPBUtil::checkString($var, True);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * Int64 value of the operand if it is a int64 type.
     *
     * Generated from protobuf field <code>int64 long_value = 6;</code>
     * @return int|string
     */
    public function getLongValue()
    {
        return $this->readOneof(6);
    }

    public function hasLongValue()
    {
        return $this->hasOneof(6);
    }

    /**
     * Int64 value of the operand if it is a int64 type.
     *
     * Generated from protobuf field <code>int64 long_value = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setLongValue($var)
    {
        GPBUtil::checkInt64($var);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * Boolean value of the operand if it is a boolean type.
     *
     * Generated from protobuf field <code>bool boolean_value = 7;</code>
     * @return bool
     */
    public function getBooleanValue()
    {
        return $this->readOneof(7);
    }

    public function hasBooleanValue()
    {
        return $this->hasOneof(7);
    }

    /**
     * Boolean value of the operand if it is a boolean type.
     *
     * Generated from protobuf field <code>bool boolean_value = 7;</code>
     * @param bool $var
     * @return $this
     */
    public function setBooleanValue($var)
    {
        GPBUtil::checkBool($var);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * Double value of the operand if it is a double type.
     *
     * Generated from protobuf field <code>double double_value = 8;</code>
     * @return float
     */
    public function getDoubleValue()
    {
        return $this->readOneof(8);
    }

    public function hasDoubleValue()
    {
        return $this->hasOneof(8);
    }

    /**
     * Double value of the operand if it is a double type.
     *
     * Generated from protobuf field <code>double double_value = 8;</code>
     * @param float $var
     * @return $this
     */
    public function setDoubleValue($var)
    {
        GPBUtil::checkDouble($var);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getConstantOperandValue()
    {
        return $this->whichOneof("constant_operand_value");
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ConstantOperand::class, \Google\Ads\GoogleAds\V17\Common\Operand_ConstantOperand::class);

