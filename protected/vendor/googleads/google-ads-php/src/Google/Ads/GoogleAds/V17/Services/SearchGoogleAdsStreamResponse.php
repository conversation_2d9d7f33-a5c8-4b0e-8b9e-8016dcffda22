<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/services/google_ads_service.proto

namespace Google\Ads\GoogleAds\V17\Services;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response message for
 * [GoogleAdsService.SearchStream][google.ads.googleads.v17.services.GoogleAdsService.SearchStream].
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.services.SearchGoogleAdsStreamResponse</code>
 */
class SearchGoogleAdsStreamResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * The list of rows that matched the query.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.GoogleAdsRow results = 1;</code>
     */
    private $results;
    /**
     * FieldMask that represents what fields were requested by the user.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 2;</code>
     */
    protected $field_mask = null;
    /**
     * Summary row that contains summary of metrics in results.
     * Summary of metrics means aggregation of metrics across all results,
     * here aggregation could be sum, average, rate, etc.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.services.GoogleAdsRow summary_row = 3;</code>
     */
    protected $summary_row = null;
    /**
     * The unique id of the request that is used for debugging purposes.
     *
     * Generated from protobuf field <code>string request_id = 4;</code>
     */
    protected $request_id = '';
    /**
     * The amount of resources consumed to serve the query.
     * query_resource_consumption for the Summary row and non-Summary responses
     * are returned separately in their respective rows.
     * query_resource_consumption for non-Summary responses is returned in the
     * final batch of results.
     *
     * Generated from protobuf field <code>int64 query_resource_consumption = 6;</code>
     */
    protected $query_resource_consumption = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<\Google\Ads\GoogleAds\V17\Services\GoogleAdsRow>|\Google\Protobuf\Internal\RepeatedField $results
     *           The list of rows that matched the query.
     *     @type \Google\Protobuf\FieldMask $field_mask
     *           FieldMask that represents what fields were requested by the user.
     *     @type \Google\Ads\GoogleAds\V17\Services\GoogleAdsRow $summary_row
     *           Summary row that contains summary of metrics in results.
     *           Summary of metrics means aggregation of metrics across all results,
     *           here aggregation could be sum, average, rate, etc.
     *     @type string $request_id
     *           The unique id of the request that is used for debugging purposes.
     *     @type int|string $query_resource_consumption
     *           The amount of resources consumed to serve the query.
     *           query_resource_consumption for the Summary row and non-Summary responses
     *           are returned separately in their respective rows.
     *           query_resource_consumption for non-Summary responses is returned in the
     *           final batch of results.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Services\GoogleAdsService::initOnce();
        parent::__construct($data);
    }

    /**
     * The list of rows that matched the query.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.GoogleAdsRow results = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getResults()
    {
        return $this->results;
    }

    /**
     * The list of rows that matched the query.
     *
     * Generated from protobuf field <code>repeated .google.ads.googleads.v17.services.GoogleAdsRow results = 1;</code>
     * @param array<\Google\Ads\GoogleAds\V17\Services\GoogleAdsRow>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setResults($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Ads\GoogleAds\V17\Services\GoogleAdsRow::class);
        $this->results = $arr;

        return $this;
    }

    /**
     * FieldMask that represents what fields were requested by the user.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 2;</code>
     * @return \Google\Protobuf\FieldMask|null
     */
    public function getFieldMask()
    {
        return $this->field_mask;
    }

    public function hasFieldMask()
    {
        return isset($this->field_mask);
    }

    public function clearFieldMask()
    {
        unset($this->field_mask);
    }

    /**
     * FieldMask that represents what fields were requested by the user.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask field_mask = 2;</code>
     * @param \Google\Protobuf\FieldMask $var
     * @return $this
     */
    public function setFieldMask($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\FieldMask::class);
        $this->field_mask = $var;

        return $this;
    }

    /**
     * Summary row that contains summary of metrics in results.
     * Summary of metrics means aggregation of metrics across all results,
     * here aggregation could be sum, average, rate, etc.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.services.GoogleAdsRow summary_row = 3;</code>
     * @return \Google\Ads\GoogleAds\V17\Services\GoogleAdsRow|null
     */
    public function getSummaryRow()
    {
        return $this->summary_row;
    }

    public function hasSummaryRow()
    {
        return isset($this->summary_row);
    }

    public function clearSummaryRow()
    {
        unset($this->summary_row);
    }

    /**
     * Summary row that contains summary of metrics in results.
     * Summary of metrics means aggregation of metrics across all results,
     * here aggregation could be sum, average, rate, etc.
     *
     * Generated from protobuf field <code>.google.ads.googleads.v17.services.GoogleAdsRow summary_row = 3;</code>
     * @param \Google\Ads\GoogleAds\V17\Services\GoogleAdsRow $var
     * @return $this
     */
    public function setSummaryRow($var)
    {
        GPBUtil::checkMessage($var, \Google\Ads\GoogleAds\V17\Services\GoogleAdsRow::class);
        $this->summary_row = $var;

        return $this;
    }

    /**
     * The unique id of the request that is used for debugging purposes.
     *
     * Generated from protobuf field <code>string request_id = 4;</code>
     * @return string
     */
    public function getRequestId()
    {
        return $this->request_id;
    }

    /**
     * The unique id of the request that is used for debugging purposes.
     *
     * Generated from protobuf field <code>string request_id = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setRequestId($var)
    {
        GPBUtil::checkString($var, True);
        $this->request_id = $var;

        return $this;
    }

    /**
     * The amount of resources consumed to serve the query.
     * query_resource_consumption for the Summary row and non-Summary responses
     * are returned separately in their respective rows.
     * query_resource_consumption for non-Summary responses is returned in the
     * final batch of results.
     *
     * Generated from protobuf field <code>int64 query_resource_consumption = 6;</code>
     * @return int|string
     */
    public function getQueryResourceConsumption()
    {
        return $this->query_resource_consumption;
    }

    /**
     * The amount of resources consumed to serve the query.
     * query_resource_consumption for the Summary row and non-Summary responses
     * are returned separately in their respective rows.
     * query_resource_consumption for non-Summary responses is returned in the
     * final batch of results.
     *
     * Generated from protobuf field <code>int64 query_resource_consumption = 6;</code>
     * @param int|string $var
     * @return $this
     */
    public function setQueryResourceConsumption($var)
    {
        GPBUtil::checkInt64($var);
        $this->query_resource_consumption = $var;

        return $this;
    }

}

