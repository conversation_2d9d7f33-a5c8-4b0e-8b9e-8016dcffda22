<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/enums/feed_item_quality_disapproval_reason.proto

namespace Google\Ads\GoogleAds\V16\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible quality evaluation disapproval reasons
 * of a feed item.
 *
 * Generated from protobuf message <code>google.ads.googleads.v16.enums.FeedItemQualityDisapprovalReasonEnum</code>
 */
class FeedItemQualityDisapprovalReasonEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V16\Enums\FeedItemQualityDisapprovalReason::initOnce();
        parent::__construct($data);
    }

}

