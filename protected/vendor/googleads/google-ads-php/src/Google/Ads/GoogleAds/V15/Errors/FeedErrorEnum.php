<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v15/errors/feed_error.proto

namespace Google\Ads\GoogleAds\V15\Errors;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing possible feed errors.
 *
 * Generated from protobuf message <code>google.ads.googleads.v15.errors.FeedErrorEnum</code>
 */
class FeedErrorEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V15\Errors\FeedError::initOnce();
        parent::__construct($data);
    }

}

