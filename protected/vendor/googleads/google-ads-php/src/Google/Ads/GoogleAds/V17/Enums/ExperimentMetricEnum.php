<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/experiment_metric.proto

namespace Google\Ads\GoogleAds\V17\Enums;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Container for enum describing the type of experiment metric.
 *
 * Generated from protobuf message <code>google.ads.googleads.v17.enums.ExperimentMetricEnum</code>
 */
class ExperimentMetricEnum extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Ads\GoogleAds\V17\Enums\ExperimentMetric::initOnce();
        parent::__construct($data);
    }

}

