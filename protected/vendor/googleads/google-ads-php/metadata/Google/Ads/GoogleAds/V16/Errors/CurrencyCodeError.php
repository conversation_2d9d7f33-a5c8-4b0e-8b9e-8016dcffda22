<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v16/errors/currency_code_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V16\Errors;

class CurrencyCodeError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
9google/ads/googleads/v16/errors/currency_code_error.protogoogle.ads.googleads.v16.errors"[
CurrencyCodeErrorEnum"B
CurrencyCodeError
UNSPECIFIED 
UNKNOWN
UNSUPPORTEDB�
#com.google.ads.googleads.v16.errorsBCurrencyCodeErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v16/errors;errors�GAA�Google.Ads.GoogleAds.V16.Errors�Google\\Ads\\GoogleAds\\V16\\Errors�#Google::Ads::GoogleAds::V16::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

