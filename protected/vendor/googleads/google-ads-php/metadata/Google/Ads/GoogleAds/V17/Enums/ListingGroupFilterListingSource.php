<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/enums/listing_group_filter_listing_source.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Enums;

class ListingGroupFilterListingSource
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
Hgoogle/ads/googleads/v17/enums/listing_group_filter_listing_source.protogoogle.ads.googleads.v17.enums"�
#ListingGroupFilterListingSourceEnum"Z
ListingGroupFilterListingSource
UNSPECIFIED 
UNKNOWN
SHOPPING
WEBPAGEB�
"com.google.ads.googleads.v17.enumsB$ListingGroupFilterListingSourceProtoPZCgoogle.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums�GAA�Google.Ads.GoogleAds.V17.Enums�Google\\Ads\\GoogleAds\\V17\\Enums�"Google::Ads::GoogleAds::V17::Enumsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

