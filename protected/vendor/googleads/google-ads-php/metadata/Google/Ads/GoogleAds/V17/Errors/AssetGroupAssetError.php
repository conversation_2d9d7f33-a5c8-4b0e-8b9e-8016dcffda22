<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/ads/googleads/v17/errors/asset_group_asset_error.proto

namespace GPBMetadata\Google\Ads\GoogleAds\V17\Errors;

class AssetGroupAssetError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();
        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
=google/ads/googleads/v17/errors/asset_group_asset_error.protogoogle.ads.googleads.v17.errors"�
AssetGroupAssetErrorEnum"�
AssetGroupAssetError
UNSPECIFIED 
UNKNOWN
DUPLICATE_RESOURCE.
*EXPANDABLE_TAGS_NOT_ALLOWED_IN_DESCRIPTION
AD_CUSTOMIZER_NOT_SUPPORTED/
+HOTEL_PROPERTY_ASSET_NOT_LINKED_TO_CAMPAIGNB�
#com.google.ads.googleads.v17.errorsBAssetGroupAssetErrorProtoPZEgoogle.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors�GAA�Google.Ads.GoogleAds.V17.Errors�Google\\Ads\\GoogleAds\\V17\\Errors�#Google::Ads::GoogleAds::V17::Errorsbproto3'
        , true);
        static::$is_initialized = true;
    }
}

