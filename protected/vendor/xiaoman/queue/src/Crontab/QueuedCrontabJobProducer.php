<?php


namespace <PERSON><PERSON>\Queue\Crontab;


use <PERSON><PERSON>\Queue\Contract\Queue;
use <PERSON><PERSON>\Queue\Job\StatefulJob;
use <PERSON><PERSON>\Queue\ShouldUseQueue;

class QueuedCrontabJobProducer implements ShouldUseQueue
{

    protected $jobClass;

    protected $queue;

    public function setQueue(Queue $queue)
    {
        $this->queue = $queue;
        return $this;
    }

    public function getQueue()
    {
        return $this->queue;
    }

    /**
     * @return CrontabJobSeed
     */
    public function seeder()
    {
        return $this->getJobClass()::seeder();
    }
//
//    public function execute($jobClass, $params)
//     {
//        $this->setJobClass($jobClass);
//        $seeder = $this->seeder();
//        $seeds = $seeder->getSeeds();
//
//        foreach ($seeds as $seederName => $seed) {
//            $seedParams = $seed['extra_params'];
//            if (is_array($seedParams) && !empty($seedParams)) {
//                $job = new $this->jobClass(...$seedParams);
//            } else {
//                $job = new $this->jobClass();
//            }
//            if ($job instanceof StatefulJob) {
//                $job->setStateList((array)$seed['ids']);
//            }
//
//            $job = $this->prepareJob($job);
//            $this->getQueue()->push($job);
//        }
//    }
//
//    public function prepareJob($job)
//    {
//        return $job;
//    }
//
//    public function setJobClass($jobClass)
//    {
//        $this->jobClass = $jobClass;
//
//        return $this;
//    }
//
//    public function getJobClass()
//    {
//        return $this->jobClass;
//    }

}