diff --git a/src/Framework/MockObject/Generator.php b/src/Framework/MockObject/Generator.php
index 3df3abb..fc76d5d 100644
--- a/src/Framework/MockObject/Generator.php
+++ b/src/Framework/MockObject/Generator.php
@@ -1031,16 +1031,29 @@ protected function getMethodParameters(ReflectionMethod $method, $forCall = fals
             $typeDeclaration = '';
 
             if (!$forCall) {
+                if (PHP_VERSION_ID >= 80000) {
+                    $isArray = $parameter->getType() && $parameter->getType()->getName() === 'array';
+                    $isCallable = $parameter->getType() && $parameter->getType()->getName() === 'callable';
+                } else {
+                    $isArray = $parameter->isArray();
+                    $isCallable = version_compare(PHP_VERSION, '5.4.0', '>=') ? $parameter->isCallable() : false;
+                }
+
                 if ($this->hasType($parameter)) {
-                    $typeDeclaration = (string) $parameter->getType() . ' ';
-                } elseif ($parameter->isArray()) {
+                    $type = $parameter->getType();
+                    $typeDeclaration = (PHP_VERSION_ID >= 70100 ? $type->getName() : (string) $type) . ' ';
+                } elseif ($isArray) {
                     $typeDeclaration = 'array ';
                 } elseif ((defined('HHVM_VERSION') || version_compare(PHP_VERSION, '5.4.0', '>='))
-                          && $parameter->isCallable()) {
+                          && $isCallable) {
                     $typeDeclaration = 'callable ';
                 } else {
                     try {
-                        $class = $parameter->getClass();
+                        if (PHP_VERSION_ID >= 80000) {
+                            $class = $parameter->getType();
+                        } else {
+                            $class = $parameter->getClass();
+                        }
                     } catch (ReflectionException $e) {
                         throw new PHPUnit_Framework_MockObject_RuntimeException(
                             sprintf(
