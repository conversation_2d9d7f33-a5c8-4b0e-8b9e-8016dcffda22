<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

if (false) {
    /**
     * This class is deprecated. Use Google\Protobuf\Internal\EnumDescriptorProto\EnumReservedRange instead.
     * @deprecated
     */
    class EnumDescriptorProto_EnumReservedRange {}
}
class_exists(EnumDescriptorProto\EnumReservedRange::class);
@trigger_error('Google\Protobuf\Internal\EnumDescriptorProto_EnumReservedRange is deprecated and will be removed in the next major release. Use Google\Protobuf\Internal\EnumDescriptorProto\EnumReservedRange instead', E_USER_DEPRECATED);

