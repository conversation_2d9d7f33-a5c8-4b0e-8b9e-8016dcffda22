<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_AdExchangeBuyerII_AddNoteRequest extends Google_Model
{
  protected $noteType = 'Google_Service_AdExchangeBuyerII_Note';
  protected $noteDataType = '';

  /**
   * @param Google_Service_AdExchangeBuyerII_Note
   */
  public function setNote(Google_Service_AdExchangeBuyerII_Note $note)
  {
    $this->note = $note;
  }
  /**
   * @return Google_Service_AdExchangeBuyerII_Note
   */
  public function getNote()
  {
    return $this->note;
  }
}
