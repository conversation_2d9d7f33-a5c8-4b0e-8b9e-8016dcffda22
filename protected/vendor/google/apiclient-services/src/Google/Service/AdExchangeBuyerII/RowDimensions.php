<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_AdExchangeBuyerII_RowDimensions extends Google_Model
{
  public $publisherIdentifier;
  protected $timeIntervalType = 'Google_Service_AdExchangeBuyerII_TimeInterval';
  protected $timeIntervalDataType = '';

  public function setPublisherIdentifier($publisherIdentifier)
  {
    $this->publisherIdentifier = $publisherIdentifier;
  }
  public function getPublisherIdentifier()
  {
    return $this->publisherIdentifier;
  }
  /**
   * @param Google_Service_AdExchangeBuyerII_TimeInterval
   */
  public function setTimeInterval(Google_Service_AdExchangeBuyerII_TimeInterval $timeInterval)
  {
    $this->timeInterval = $timeInterval;
  }
  /**
   * @return Google_Service_AdExchangeBuyerII_TimeInterval
   */
  public function getTimeInterval()
  {
    return $this->timeInterval;
  }
}
