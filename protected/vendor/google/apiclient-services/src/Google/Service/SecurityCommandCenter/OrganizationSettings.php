<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_SecurityCommandCenter_OrganizationSettings extends Google_Model
{
  protected $assetDiscoveryConfigType = 'Google_Service_SecurityCommandCenter_AssetDiscoveryConfig';
  protected $assetDiscoveryConfigDataType = '';
  public $enableAssetDiscovery;
  public $name;

  /**
   * @param Google_Service_SecurityCommandCenter_AssetDiscoveryConfig
   */
  public function setAssetDiscoveryConfig(Google_Service_SecurityCommandCenter_AssetDiscoveryConfig $assetDiscoveryConfig)
  {
    $this->assetDiscoveryConfig = $assetDiscoveryConfig;
  }
  /**
   * @return Google_Service_SecurityCommandCenter_AssetDiscoveryConfig
   */
  public function getAssetDiscoveryConfig()
  {
    return $this->assetDiscoveryConfig;
  }
  public function setEnableAssetDiscovery($enableAssetDiscovery)
  {
    $this->enableAssetDiscovery = $enableAssetDiscovery;
  }
  public function getEnableAssetDiscovery()
  {
    return $this->enableAssetDiscovery;
  }
  public function setName($name)
  {
    $this->name = $name;
  }
  public function getName()
  {
    return $this->name;
  }
}
