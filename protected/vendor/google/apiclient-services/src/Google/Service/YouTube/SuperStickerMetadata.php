<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_YouTube_SuperStickerMetadata extends Google_Model
{
  public $altText;
  public $altTextLanguage;
  public $stickerId;

  public function setAltText($altText)
  {
    $this->altText = $altText;
  }
  public function getAltText()
  {
    return $this->altText;
  }
  public function setAltTextLanguage($altTextLanguage)
  {
    $this->altTextLanguage = $altTextLanguage;
  }
  public function getAltTextLanguage()
  {
    return $this->altTextLanguage;
  }
  public function setStickerId($stickerId)
  {
    $this->stickerId = $stickerId;
  }
  public function getStickerId()
  {
    return $this->stickerId;
  }
}
