<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: infra/ai/recommend/v2/recommend_service.proto

namespace Infra\Ai\Recommend\V2;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>infra.ai.recommend.v2.RecommendCompanyResponse</code>
 */
class RecommendCompanyResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.RecommendCompanyResponse.Company companies = 1;</code>
     */
    private $companies;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Infra\Ai\Recommend\V2\RecommendCompanyResponse\Company[]|\Google\Protobuf\Internal\RepeatedField $companies
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Infra\Ai\Recommend\V2\RecommendService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.RecommendCompanyResponse.Company companies = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getCompanies()
    {
        return $this->companies;
    }

    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.RecommendCompanyResponse.Company companies = 1;</code>
     * @param \Infra\Ai\Recommend\V2\RecommendCompanyResponse\Company[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setCompanies($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Infra\Ai\Recommend\V2\RecommendCompanyResponse\Company::class);
        $this->companies = $arr;

        return $this;
    }

}

