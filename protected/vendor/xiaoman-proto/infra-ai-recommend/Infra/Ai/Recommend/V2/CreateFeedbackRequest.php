<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: infra/ai/recommend/v2/recommend_service.proto

namespace Infra\Ai\Recommend\V2;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>infra.ai.recommend.v2.CreateFeedbackRequest</code>
 */
class CreateFeedbackRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     */
    private $client_id = 0;
    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     */
    private $user_id = 0;
    /**
     * Generated from protobuf field <code>string company_hash_id = 3;</code>
     */
    private $company_hash_id = '';
    /**
     * Generated from protobuf field <code>.infra.ai.recommend.v2.CreateFeedbackRequest.Type type = 4;</code>
     */
    private $type = 0;
    /**
     * Generated from protobuf field <code>string queue = 5;</code>
     */
    private $queue = '';
    /**
     * Generated from protobuf field <code>string recaller_id = 6;</code>
     */
    private $recaller_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $client_id
     *     @type int|string $user_id
     *     @type string $company_hash_id
     *     @type int $type
     *     @type string $queue
     *     @type string $recaller_id
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Infra\Ai\Recommend\V2\RecommendService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     * @return int|string
     */
    public function getClientId()
    {
        return $this->client_id;
    }

    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setClientId($var)
    {
        GPBUtil::checkInt64($var);
        $this->client_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     * @return int|string
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setUserId($var)
    {
        GPBUtil::checkInt64($var);
        $this->user_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string company_hash_id = 3;</code>
     * @return string
     */
    public function getCompanyHashId()
    {
        return $this->company_hash_id;
    }

    /**
     * Generated from protobuf field <code>string company_hash_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setCompanyHashId($var)
    {
        GPBUtil::checkString($var, True);
        $this->company_hash_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.infra.ai.recommend.v2.CreateFeedbackRequest.Type type = 4;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Generated from protobuf field <code>.infra.ai.recommend.v2.CreateFeedbackRequest.Type type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkEnum($var, \Infra\Ai\Recommend\V2\CreateFeedbackRequest_Type::class);
        $this->type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string queue = 5;</code>
     * @return string
     */
    public function getQueue()
    {
        return $this->queue;
    }

    /**
     * Generated from protobuf field <code>string queue = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setQueue($var)
    {
        GPBUtil::checkString($var, True);
        $this->queue = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string recaller_id = 6;</code>
     * @return string
     */
    public function getRecallerId()
    {
        return $this->recaller_id;
    }

    /**
     * Generated from protobuf field <code>string recaller_id = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setRecallerId($var)
    {
        GPBUtil::checkString($var, True);
        $this->recaller_id = $var;

        return $this;
    }

}

