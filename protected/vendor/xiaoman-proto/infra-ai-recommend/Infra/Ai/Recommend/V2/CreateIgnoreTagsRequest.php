<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: infra/ai/recommend/v2/recommend_service.proto

namespace Infra\Ai\Recommend\V2;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>infra.ai.recommend.v2.CreateIgnoreTagsRequest</code>
 */
class CreateIgnoreTagsRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     */
    private $client_id = 0;
    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     */
    private $user_id = 0;
    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.CreateIgnoreTagsRequest.Tag tags = 3;</code>
     */
    private $tags;
    /**
     * Generated from protobuf field <code>string queue = 4;</code>
     */
    private $queue = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $client_id
     *     @type int|string $user_id
     *     @type \Infra\Ai\Recommend\V2\CreateIgnoreTagsRequest\Tag[]|\Google\Protobuf\Internal\RepeatedField $tags
     *     @type string $queue
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Infra\Ai\Recommend\V2\RecommendService::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     * @return int|string
     */
    public function getClientId()
    {
        return $this->client_id;
    }

    /**
     * Generated from protobuf field <code>int64 client_id = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setClientId($var)
    {
        GPBUtil::checkInt64($var);
        $this->client_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     * @return int|string
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * Generated from protobuf field <code>int64 user_id = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setUserId($var)
    {
        GPBUtil::checkInt64($var);
        $this->user_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.CreateIgnoreTagsRequest.Tag tags = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     * Generated from protobuf field <code>repeated .infra.ai.recommend.v2.CreateIgnoreTagsRequest.Tag tags = 3;</code>
     * @param \Infra\Ai\Recommend\V2\CreateIgnoreTagsRequest\Tag[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTags($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Infra\Ai\Recommend\V2\CreateIgnoreTagsRequest\Tag::class);
        $this->tags = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string queue = 4;</code>
     * @return string
     */
    public function getQueue()
    {
        return $this->queue;
    }

    /**
     * Generated from protobuf field <code>string queue = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setQueue($var)
    {
        GPBUtil::checkString($var, True);
        $this->queue = $var;

        return $this;
    }

}

