[简体中文](./README.md) | English

<p align="center">
<a href=" https://www.aliyun.com"><img src="https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg"></a>
</p>

<h1 align="center">Alibaba Cloud Hitsdb SDK for PHP</h1>

If [Alibaba Cloud SDK for PHP][sdk] is installed, there is no need to install the product dependency package. This product dependency package is only part of the synchronization from [Alibaba Cloud SDK for PHP][sdk], and its namespace and usage are consistent with [Alibaba Cloud SDK for PHP][sdk].

If you don't care about file size, we recommend that you install [Alibaba Cloud SDK for PHP][sdk] and update it regularly so as to maintain the latest and most complete product support:
```
composer require alibabacloud/sdk
```

The product can also be installed only:
> The version of this product is always synchronized with [Alibaba Cloud SDK for PHP][sdk] to ensure that it can switch with [Alibaba Cloud SDK for PHP][sdk] at any time without changing the business code, although the code between different versions of this product may not change.
```
composer require alibabacloud/hitsdb
```

***
Refer to document [Alibaba Cloud SDK for PHP][sdk] for more SDK usage.

[sdk]: https://github.com/aliyun/openapi-sdk-php
