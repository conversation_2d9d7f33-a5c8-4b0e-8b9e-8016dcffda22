<?php

use common\library\account\Client;
use common\library\privilege_v3\PrivilegeService;

class ErpServiceReadController extends Controller
{

    /**
     * ERP服务列表
     * @return array
     */
    public function actionErpServiceList()
    {
        $user     = User::getLoginUser();
        $clientId = $user->getClientId();

        // 若有配置，找出来不展示
        /*
        $client = Client::getClient($clientId);
        $extentConfig = $client->getExtentAttributes([Client::EXTERNAL_KEY_ERP_SERVICE_NOT_SHOW, Client::EXTERNAL_KEY_ERP_GALAXY_SHOW]);
        $notShowConfig = explode(',', $extentConfig[Client::EXTERNAL_KEY_ERP_SERVICE_NOT_SHOW] ?? '');
        $galaxyShow = $extentConfig[Client::EXTERNAL_KEY_ERP_GALAXY_SHOW] ?? 0;
        */

        $erpServiceList = new \common\library\erp_service\ErpServiceList($clientId);
        if (!empty($notShowConfig)) {
            $erpServiceList->setNotShowType($notShowConfig);
        }
        $erpServiceList->setEnableFlag(1);
        $erpServiceList->setOrderBy('type');
        $erpServiceList->setOrder('asc');
        $erpServiceList->getFormatter()->setSpecifyFields(['service_id', 'service_name', 'type', 'auth_status', 'last_sync_end_time', 'qcloud_flag']);
        $erpServiceList->getFormatter()->setShowHasUserMappingFlag(true);
//        $erpServiceList->getFormatter()->setShowErpAdminAccountInfoFlag(true);
        $list = $erpServiceList->find();

        // 如果列表为空,则需要初始化列表
        // 本期由于产品需要默认生成金蝶erp记录,但由于后期规划不明,先不考虑挪到initClient处,后续可迁移
        $initDefaultServiceTypeList = [ErpService::TYPE_KINGDEE, ErpService::TYPE_CHANGJET, ErpService::TYPE_GALAXY];
        $uniqueListType             = array_unique(array_column($list, 'type'));
        $needInitServiceTypeList    = array_diff($initDefaultServiceTypeList, $uniqueListType);

        foreach ($needInitServiceTypeList as $type) {
            if (!empty($notShowConfig) && in_array($type, $notShowConfig)) {
                continue;
            }
            /*
            // start云星空特别显示要求，临时，后面开量要删除：原来有的云星空企业无影响，没有云星空的根据白名单展示
            if ($type == ErpService::TYPE_GALAXY && !$galaxyShow) {
                continue;
            }
            // end云星空
            */

//            $list[] = \common\library\erp_service\Helper::getDefaultServiceInfo($type);
            $erpService               = new \common\library\erp_service\ErpService($clientId, 0);
            $erpService->type         = $type;
            $erpService->service_name = \common\library\erp_service\Constant::ERP_SERVICE_NAME_MAP[$type] ?? '';
            $erpService->sync_rule    = json_encode(\common\library\erp_service\Helper::getErpSyncDefaultRule($type));
            $erpService->save();

            $list[] = [
                'service_id'         => $erpService->service_id,
                'service_name'       => \Yii::t('erpserver', $erpService->service_name),
                'type'               => $erpService->type,
                'auth_status'        => $erpService->auth_status,
                'last_sync_end_time' => $erpService->last_sync_end_time,
            ];
        }

        $ret = [
            'list' => $list,
        ];


        return $this->success($ret);
    }

    /**
     * ERP服务规则
     * @param int $type
     * @return array
     */
    public function actionErpServiceRuleConfig(int $type = ErpService::TYPE_KINGDEE)
    {
        $typeEnumListStr = implode(',', \common\library\erp_service\Constant::ERP_SERVICE_TYPE_ENUM_LIST);
        $this->validate([
            "type" => "integer|in:{$typeEnumListStr}"
        ]);

        $ret = \common\library\erp_service\Helper::getErpSyncDefaultRule($type);
        return $this->success($ret);
    }

    /**
     * 用户映射关系
     * @param int $type erp服务类型
     * @param int $service_id 配置id,不传配置id则取默认规则
     * @return array
     */
    public function actionUserMappingList(int $type = ErpService::TYPE_KINGDEE, int $service_id = 0)
    {
        $typeEnumListStr = implode(',', \common\library\erp_service\Constant::ERP_SERVICE_TYPE_ENUM_LIST);
        $this->validate([
            "type"       => "integer|in:{$typeEnumListStr}",
            "service_id" => 'integer'
        ]);

        $user     = User::getLoginUser();
        $clientId = $user->getClientId();

        $ret = \common\library\erp_service\Helper::getUserMappingList($clientId, $type, $service_id);
        return $this->success($ret);
    }

    /**
     * erp服务配置详情
     * @param int $service_id
     * @return array
     */
    public function actionErpServiceDetail(int $service_id)
    {
        $this->validate([
            "service_id" => 'required|integer|not_empty'
        ]);

        $user     = User::getLoginUser();
        $clientId = $user->getClientId();

        $erpService = new \common\library\erp_service\ErpService($clientId, $service_id);
        $erpService->getFormatter()->setSpecifyFields(['service_id', 'service_name', 'type', 'sync_rule', 'auth_status', 'last_sync_end_time', 'enable_flag', 'qcloud_flag', 'erp_order_status_flag']);
        $erpService->getFormatter()->setShowUserMapping(true);
        $erpService->getFormatter()->setShowErpAdminAccountInfo(true);
        $erpService->getFormatter()->setShowExtraInfo(true);
        $data = $erpService->getAttributes();

        // 组装主账号信息
        $adminUserId                 = PrivilegeService::getInstance($clientId)->getAdminUserId();
        $crmAdminUser                = \User::getUserObject($adminUserId);
        $data['crm_admin_user_info'] = [
            'user_id'  => $crmAdminUser->getUserId(),
            'nickname' => $crmAdminUser->getNickname(),
            'email'    => $crmAdminUser->getRealEmail(),
        ];

        return $this->success($data);
    }

    /**
     * 获取erp服务数据最新一次同步时间
     * @return false|string
     */
    public function actionErpServiceSyncTime()
    {
        $user     = User::getLoginUser();
        $clientId = $user->getClientId();
        $result   = [];
        $clientId && $result = \common\library\erp_service\Helper::getErpLastSyncTime($clientId);
        // $result = [['client_id' => 1, 'service_id' => 2,'last_sync' => '2022-10-19 11:11:11', 'name' => 'xxx']]
        return $this->success($result);
    }

    /**
     * 获取授权链接
     * @param int $type
     * @return void
     */
    public function actionGetAuthUrl(int $type = ErpService::TYPE_KINGDEE)
    {
        $user     = User::getLoginUser();
        $clientId = $user->getClientId();

        $authParams              = [];
        $authParams['client_id'] = $clientId;

        $url = \common\library\erp_service\Helper::getAuthUrl($type, $authParams);
        $ret = [
            'url' => $url
        ];
        return $this->success($ret);
    }

    /**
     * 用于授权后关闭窗口
     * @return array|false|mixed|string|string[]|null
     */
    public function actionAuthCallback()
    {
        return $this->render('/erpService/authCallback');
    }
}