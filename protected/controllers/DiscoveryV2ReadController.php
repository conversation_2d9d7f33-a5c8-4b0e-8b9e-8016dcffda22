<?php

use common\library\api\InnerApi;
use common\library\ciq\CiqService;
use common\library\ciq\Constant;
use common\library\custom_field\field_item\migration\FieldItemSetting;
use common\library\discovery\api\Search;
use common\library\discovery\Helper;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\user_company_unrelated\UserCompanyUnrelated;

/**
 * The file is part of the php-crm.
 *
 * (c) anhoder <<EMAIL>>.
 *
 * 2022/10/4 14:52
 */

class DiscoveryV2ReadController extends DiscoveryV2Controller
{
    const PAGE_SIZE = 25;

    const SCENE_MINING = 'mining';
    const SCENE_SEARCH = 'search';

    /**
     * 社媒主页
     * @param string $id
     * @param string $domain
     * @param array $urls
     * @return false|string|null
     * @throws \Exception
     */
    public function actionProfileSocialPages(string $id = '', string $domain = '', array $urls = [])
    {
        $this->validate([
            'domain' => ['string', new \common\library\validation\rules\Domain()],
            'urls' => 'array',
            'urls.*' => 'url',
        ]);
        if (!$id && !$domain && !$urls) {
            throw new RuntimeException('id、domain、urls必填一个');
        }
        $user = User::getLoginUser();
        $company = new \common\library\discovery\api\Company($user->getClientId(), $user->getUserId());
        $company->companyHashId = $id;
        $company->domain = $domain;
        $company->urls = $urls;
        $result = $company->getProfileSocialPages();
        return $this->success($result);
    }



    /**
     * 社媒主页
     * @param string $page_id
     * @return false|string|null
     */
    public function actionSocialPageDetail(string $page_id)
    {
        $this->validate([
            'page_id' => 'required',
        ]);
        $user = User::getLoginUser();
        $company = new \common\library\discovery\api\Company($user->getClientId(), $user->getUserId());
        $company->pageId = $page_id;
        $result = $company->getFacebookPageDetail();
        return $this->success($result);
    }

    /***
     * Dx详情页获取公司linkedin数据和Facebook数据
     *
     * @param string $domain
     * @param string $linkedin_url
     * @return false|string|void
     */
    public function actionSocialMediaDetail(string $company_hash_id='' , string $domain='', array $facebook_urls = [], array $linkedin_urls = [])
    {
        $this->validate([
            'domain' => ['string', new \common\library\validation\rules\Domain()],
            'facebook_urls' => 'array',
            'facebook_urls.*' => 'url',
            'linkedin_urls' => 'array',
            'linkedin_urls.*' => 'url',
        ]);
        if (!$company_hash_id && !$domain ) {
            throw new RuntimeException('company_hash_id、domain 必填一个');
        }

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $companyService = new \common\library\discovery\CompanyService($clientId, $userId);
        $unlockStatus = $companyService->getUnlockStatus($company_hash_id);

        if (!$unlockStatus) {
            return $this->success(['facebookDetail' => [], 'linkedinDetail' => []]);
        }

        $linkedin = new \common\library\discovery\api\Linkedin($clientId,$userId);
        if($domain) $linkedin->setDomain($domain);;
        if($company_hash_id) $linkedin->setCompanyHashId($company_hash_id);
        if($facebook_urls) $linkedin->setFacebookUrls($facebook_urls);
        if($linkedin_urls) $linkedin->setLinkedinUrls($linkedin_urls);
        if($company_hash_id){
            $companyUnrelated = new UserCompanyUnrelated($clientId, $userId, $company_hash_id);
            $excludeCompanyHashIds = $companyUnrelated->getUnrelatedCompanyHashIds();
            $linkedin->setMergeCompanyInfo($excludeCompanyHashIds);
        }
        $result = $linkedin->socialMediaDetail();
        return $this->success($result);
    }

    /**
     * 搜索列表
     */
    public function actionSearchList(
        $scene = self::SCENE_MINING,
        $searchType = 'company',
        $keyword = '',
        $countryCode = '',
        $sales = [],
        $sicCode = '',
        $existUrl = '',
        $viewed = '',
        $deduplicated = '',
        $businessType = '',
        $tradeType = '',
        $page = 1,
        $pageSize = self::PAGE_SIZE,
        $order_by = '',
        $sort = '',
        $excludeArchiveFlag = 'false',
        $existEmail = '',
        $industryType = 'sic',
        $foundedYear = '',
        $employees = '',
        $netIncome = '',
        $asserts = '',
        $marketValue = '',
        $ranking = '',
        $fiscalYearEnd = '',
        $ipoType = '',
        $naicsCode = '',
        $industryCode = '',
        $emailValue = '',
        array $matchingScope = [],
        array $primaryIndustry = [],
        $needSocialMedia = false,
    ) {
        $user = \User::getLoginUser();

        Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keyword);

        if ($order_by && $sort == '') {
            $sort = 'desc';
        }

        switch ($scene) {
            case self::SCENE_SEARCH:
                if ($existUrl !== '') {
                    $existUrl = $existUrl ? 'true' : 'false';
                }
                $search = new Search($user->getClientId(), $user->getUserId());
                $search->setParams([
                    'searchType' => $searchType,
                    'keyword' => $keyword,
                    'countryCode' => !empty($countryCode) ? explode(',', $countryCode) : '',
                    'sales' => Helper::formatSales($sales),
                    'siccode' => !empty($sicCode) ? explode(',', $sicCode) : '',
                    'existUrl' => $existUrl,
                    'viewed' => $viewed,
                    'deduplicated' => $deduplicated,
                    'businessType' => !empty($businessType) ? explode(',', $businessType) : '',
                    'tradeType' => $tradeType,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'orderBy' => $order_by,
                    'direction' => $sort,
                    'excludeArchiveFlag' => $excludeArchiveFlag,
                    'existEmail' => $existEmail,
                    'matchingScope' => $matchingScope,
                    'primaryIndustry' => $primaryIndustry,
                    'needSocialMedia' => $needSocialMedia,
                ]);
                $data = $search->searchCompanyProducts();
                break;
            case self::SCENE_MINING:
                $deduplicated = $deduplicated === '' ? null : $deduplicated;
                $existUrl = $existUrl === '' ? null : $existUrl;
                $viewed = $viewed === '' ? null : $viewed;
                $mining = Helper::miningListFactory($industryType, $page, $pageSize,
                    $countryCode, $sicCode, $keyword,
                    $sales, $foundedYear, $employees,
                    $netIncome, $asserts, $marketValue,
                    $ranking, $fiscalYearEnd, $ipoType,
                    $deduplicated, $naicsCode, $existUrl,
                    $viewed, $industryCode, $order_by, $sort, $excludeArchiveFlag, $emailValue);
                $data = $mining->getList();
                break;
            default:
                throw new RuntimeException('未知scene');
        }

        if (!empty($data['list'])) {
            $companyHashIds = array_column($data['list'], 'companyHashId');
            $list = \common\library\customer\Helper::getHashIdAssocStatus($user->getUserId(), $companyHashIds);
            $statusMap = array_column($list, null, 'company_hash_id');
            foreach ($data['list'] as $k => $v) {
                $data['list'][$k]['archive_type'] = $statusMap[$v['companyHashId']]['status'] ?? 0;
                $data['list'][$k]['status_info'] = $statusMap[$v['companyHashId']] ?? [];
            }

            // 公司名及描述信息 风控处理
            $getTexts = function ($i, $item) {
                return [
                    ['id' => "{$i}_companyName", 'text' => $item['companyName'] ?? ''],
                    ['id' => "{$i}_description", 'text' => $item['description'] ?? ''],
                ];
            };
            $backFill = function ($i, $item, $maskTexts) {
                $nameKey = "{$i}_companyName";
                $descKey = "{$i}_description";
                isset($maskTexts[$nameKey]) && $item['companyName'] = $maskTexts[$nameKey];
                isset($maskTexts[$descKey]) && $item['description'] = $maskTexts[$descKey];
                return $item;
            };
            $data['list'] = Helper::maskRiskTexts($user->getClientId(), $user->getUserId(), $data['list'], $getTexts, $backFill);
        }

        if (isset($data['total']) && empty($data['total_count'])) {
            $data['total_count'] = $data['total'];
            unset($data['total']);
        }

        $this->success($data);
    }

    /**
     * 搜索列表
     */
    public function actionSearchListV2(
        $scene = self::SCENE_MINING,
        $searchType = 'company',
        $keyword = '',
        $countryCode = '',
        array $sales = [],
        $sicCode = '',
        $existUrl = '',
        $viewed = '',
        $deduplicated = '',
        $businessType = '',
        $tradeType = '',
        $page = 1,
        $pageSize = self::PAGE_SIZE,
        $order_by = '',
        $sort = '',
        $excludeArchiveFlag = 'false',
        $existEmail = '',
        $industryType = 'sic',
        $foundedYear = '',
        $employees = '',
        $netIncome = '',
        $asserts = '',
        $marketValue = '',
        $ranking = '',
        $fiscalYearEnd = '',
        $ipoType = '',
        $naicsCode = '',
        $industryCode = '',
        $emailValue = '',
        $tagFilter = '',
        $showUnViewed = '',
        $showUnArchivedAndUnClue = '',
        $showUnFollowed = '',
        array $matchingScope = [],
        array $primaryIndustry = [],
        $needSocialMedia = false,
        $keywordOperator = 'OR'
    ) {
        $user = \User::getLoginUser();

        Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keyword);

        if ($order_by && $sort == '') {
            $sort = 'desc';
        }

        switch ($scene) {
            case self::SCENE_SEARCH:
                if ($existUrl !== '') {
                    $existUrl = $existUrl ? 'true' : 'false';
                }
                $search = new Search($user->getClientId(), $user->getUserId());
                $search->setParams([
                    'searchType' => $searchType,
                    'keyword' => $keyword,
                    'countryCode' => !empty($countryCode) ? explode(',', $countryCode) : '',
                    'sales' => Helper::formatSales($sales),
                    'siccode' => !empty($sicCode) ? explode(',', $sicCode) : '',
                    'existUrl' => is_string($existUrl) ? (strtolower($existUrl) == 'true' ? true : null) : $existUrl,
                    'viewed' => $viewed,
                    'deduplicated' => is_string($deduplicated) ? (strtolower($deduplicated) == 'true' ? true : null) : $deduplicated,
                    'businessType' => !empty($businessType) ? explode(',', $businessType) : '',
                    'industries' => !empty($industryCode) ? explode(',', $industryCode) : '',
                    'tradeTypeList' => !empty($tradeType) ? explode(',', $tradeType) : '',
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'orderBy' => $order_by,
                    'direction' => $sort,
                    'excludeArchiveFlag' => $excludeArchiveFlag,
                    'existEmail' => is_string($existEmail) ? (strtolower($existEmail) == 'true' ? true : null) : $existEmail,
                    'matchingScope' => $matchingScope,
                    'primaryIndustry' => $primaryIndustry,
                    'needSocialMedia' => $needSocialMedia,
                    'tagFilter' => $tagFilter,
                    'keywordOperator' => $keywordOperator,
                    'showUnViewed' => is_string($showUnViewed) ? strtolower($showUnViewed) == 'true' : $showUnViewed,
                    'showUnArchivedAndUnClue' => is_string($showUnArchivedAndUnClue) ? strtolower($showUnArchivedAndUnClue) == 'true' : $showUnArchivedAndUnClue,
                    'showUnFollowed'=> is_string($showUnFollowed) ? strtolower($showUnFollowed) == 'true' : $showUnFollowed,
                    'bizId' => 'web',
                ]);
                $data = $search->searchCompanyProductsV2();
                break;
            case self::SCENE_MINING:
                $deduplicated = $deduplicated === '' ? null : $deduplicated;
                $existUrl = $existUrl === '' ? null : $existUrl;
                $viewed = $viewed === '' ? null : $viewed;
                $mining = Helper::miningListFactory($industryType, $page, $pageSize,
                    $countryCode, $sicCode, $keyword,
                    $sales, $foundedYear, $employees,
                    $netIncome, $asserts, $marketValue,
                    $ranking, $fiscalYearEnd, $ipoType,
                    $deduplicated, $naicsCode, $existUrl,
                    $viewed, $industryCode, $order_by, $sort, $excludeArchiveFlag, $emailValue);
                $data = $mining->getList();
                break;
            default:
                throw new RuntimeException('未知scene');
        }

        if (!empty($data['list'])) {
            $companyHashIds = array_column($data['list'], 'companyHashId');
            $list = \common\library\customer\Helper::getHashIdAssocStatus($user->getUserId(), $companyHashIds);
            $statusMap = array_column($list, null, 'company_hash_id');

            // TODO: leads go上线临时处理，从redis获取emailCount
            $redis = RedisService::sf();
            $systemId = PrivilegeService::getInstance(\User::getLoginUser()->getClientId())->getMainSystemId();
            $isCNVersion = \User::getLoginUser()->client()->isCNVersion();
            $isHKVersion = in_array($systemId, [PrivilegeConstants::HK_LITE_AI_ID, PrivilegeConstants::HK_SMART_AI_ID, PrivilegeConstants::HK_PRO_AI_ID]);
            $isTWVersion = in_array($systemId, [PrivilegeConstants::TW_LITE_AI_ID, PrivilegeConstants::TW_SMART_AI_ID]);
            foreach ($data['list'] as $k => $v) {
                $emailCount = $redis->get("company:{$v['companyHashId']}:emailCount") ?: ($data['list'][$k]['emailCount'] ?? 0);
                // 屏蔽国内联系人
                $blockCNContact = $data['list'][$k]['countryCode'] == 'CN' && ($isCNVersion || $isHKVersion || $isTWVersion);
                $blockHKContact = $data['list'][$k]['countryCode'] == 'HK' && $isHKVersion;
                $blockTWContact = $data['list'][$k]['countryCode'] == 'TW' && $isTWVersion;
                if ($blockCNContact || $blockHKContact || $blockTWContact) {
                    $emailCount = 0;
                }
                $data['list'][$k]['emailCount'] = $emailCount;
                $data['list'][$k]['contactCount'] = $redis->get("company:{$v['companyHashId']}:contactCount") ?: ($data['list'][$k]['contactCount'] ?? 0);

                $data['list'][$k]['archive_type'] = $statusMap[$v['companyHashId']]['status'] ?? 0;
                $data['list'][$k]['status_info'] = $statusMap[$v['companyHashId']] ?? [];
            }

            // 公司名及描述信息 风控处理
            $getTexts = function ($i, $item) {
                return [
                    ['id' => "{$i}_companyName", 'text' => $item['companyName'] ?? ''],
                    ['id' => "{$i}_description", 'text' => $item['description'] ?? ''],
                ];
            };
            $backFill = function ($i, $item, $maskTexts) {
                $nameKey = "{$i}_companyName";
                $descKey = "{$i}_description";
                isset($maskTexts[$nameKey]) && $item['companyName'] = $maskTexts[$nameKey];
                isset($maskTexts[$descKey]) && $item['description'] = $maskTexts[$descKey];
                return $item;
            };
            $data['list'] = Helper::maskRiskTexts($user->getClientId(), $user->getUserId(), $data['list'], $getTexts, $backFill);
        }

        if (isset($data['total']) && empty($data['total_count'])) {
            $data['total_count'] = $data['total'];
            unset($data['total']);
        }

        $this->success($data);
    }

    /**
     * 搜索聚合
     */
    public function actionSearchAggregation(
        $scene = self::SCENE_MINING,
        $searchType = 'company',
        $keyword = '',
        $countryCode = '',
        $sales = [],
        $sicCode = '',
        $existUrl = '',
        $viewed = '',
        $deduplicated = '',
        $businessType = '',
        $tradeType = '',
        $page = 1,
        $pageSize = self::PAGE_SIZE,
        $order_by = '',
        $sort = '',
        $excludeArchiveFlag = 'false',
        $existEmail = '',
        $industryType = 'sic',
        $foundedYear = '',
        $employees = '',
        $netIncome = '',
        $asserts = '',
        $marketValue = '',
        $ranking = '',
        $fiscalYearEnd = '',
        $ipoType = '',
        $naicsCode = '',
        $industryCode = '',
        $emailValue = ''
    ) {
        $user = \User::getLoginUser();

        Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keyword);

        switch ($scene) {
            case self::SCENE_SEARCH:
                if ($existUrl !== '') {
                    $existUrl = $existUrl ? 'true' : 'false';
                }
                $search = new Search($user->getClientId(), $user->getUserId());
                $search->setParams([
                    'searchType' => $searchType,
                    'keyword' => $keyword,
                    'countryCode' => !empty($countryCode) ? explode(',', $countryCode) : '',
                    'sales' => Helper::formatSales($sales),
                    'sicCode' => !empty($sicCode) ? [$sicCode] : '',
                    'existUrl' => $existUrl,
                    'viewed' => $viewed,
                    'deduplicated' => $deduplicated,
                    'businessType' => !empty($businessType) ? explode(',', $businessType) : '',
                    'tradeType' => $tradeType,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'orderBy' => $order_by,
                    'direction' => $sort,
                    'excludeArchiveFlag' => $excludeArchiveFlag,
                    'existEmail' => $existEmail,
                    'aggSize' => 10, // 新版只取前10个
                ]);
                $data = $search->aggregation();
                break;
            case self::SCENE_MINING:
                $deduplicated = $deduplicated === '' ? null : $deduplicated;
                $existUrl = $existUrl === '' ? null : $existUrl;
                $viewed = $viewed === '' ? null : $viewed;
                $mining = Helper::miningListFactory($industryType, $page, $pageSize,
                    $countryCode, $sicCode, $keyword,
                    $sales, $foundedYear, $employees,
                    $netIncome, $asserts, $marketValue,
                    $ranking, $fiscalYearEnd, $ipoType,
                    $deduplicated, $naicsCode, $existUrl,
                    $viewed, $industryCode, $order_by, $sort, $excludeArchiveFlag, $emailValue, 10); // 新版只取前10个
                $data = $mining->getAggregation();
                break;
            default:
                throw new RuntimeException('未知scene');
        }
        $this->success($data);
    }

    /**
     * 合并社媒账号
     * @param $id
     * @return false|string|null
     */
    public function actionProfileSocial($id)
    {
        if (!$id) {
            throw new RuntimeException('id不能为空');
        }
        $user = User::getLoginUser();

        $result = [];

        $company = new \common\library\discovery\api\Company($user->getClientId(), $user->getUserId());
        $companyUnrelated = new UserCompanyUnrelated($user->getClientId(), $user->getUserId(), $id);
        $excludeCompanyHashIds = $companyUnrelated->getUnrelatedCompanyHashIds();
        $company->setMergeCompanyInfo($excludeCompanyHashIds);
        $companyInfo = $company->companySocialMedia($id);
        return $this->success($companyInfo);
    }

    /**
     * 合并社媒账号  废弃
     * @param $id
     * @return false|string|null
     */
    public function actionProfileSocialOld($id)
    {
        if (!$id) {
            throw new RuntimeException('id不能为空');
        }
        $user = User::getLoginUser();

        $result = [];

        $company = new \common\library\discovery\api\Company($user->getClientId(), $user->getUserId());
        $companyInfo = $company->getCompanyInfo($id);
        $socialProfileInfo = $company->getSocialProfiles($id);
        foreach ($socialProfileInfo ?: [] as $item) {
            if($item['typeId'] == 'google'){
                continue;
            }
            // 优先粉丝数大的
            if (empty($result[$item['typeId']]) || ($item['followers'] ?? 0) > ($result[$item['typeId']]['followers'] ?? 0)) {
                $result[$item['typeId']] = $item;
            }
        }

        $officialSiteInfo = empty($companyInfo['homepage']) ? [] : $company->getOfficialSiteParsedInfo($id, $companyInfo['homepage']);
        foreach ($officialSiteInfo['socialAccounts'] ?? [] as $item) {
            if($item['typeId'] == 'google'){
                continue;
            }
            if (empty($result[$item['typeId']]) || ($item['followers'] ?? 0) > ($result[$item['typeId']]['followers'] ?? 0)) {
                $result[$item['typeId']] = $item;
            }
        }

        return $this->success(array_values($result));
    }

    public function actionProfile($id, $brief_only = true)
    {
        $this->validate([
            'id'         => 'required|string',
            'brief_only' => 'boolean',
        ]);
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $companyService = new \common\library\discovery\CompanyService($clientId, $userId, $id);
        $companyUnrelated = new UserCompanyUnrelated($clientId, $userId, $id);
        $excludeCompanyHashIds = $companyUnrelated->getUnrelatedCompanyHashIds();
        $companyInfo = $companyService->getCompanyProfile($brief_only,true, $excludeCompanyHashIds);
        if ($brief_only) {
            $history = new \common\library\discovery\History($clientId, $userId);
            $history->addHistory($id, $companyInfo['companyCustomsName']);
        }

        $search = new Search($clientId, $userId);
        $search->setParams(['companyHashId' => $id]);
        $companyInfo['merge_companies'] = $search->getMergeCompanies();

        // 公司名及描述信息 风控处理
        $getTexts = function ($i, $item) {
            return [
                ['id' => 'companyName', 'text' => $item['companyName'] ?? ''],
                ['id' => "description", 'text' => $item['description'] ?? ''],
            ];
        };
        $backFill = function ($i, $item, $maskTexts) {
            isset($maskTexts['companyName']) && $item['companyName'] = $maskTexts['companyName'];
            isset($maskTexts['description']) && $item['description'] = $maskTexts['description'];
            return $item;
        };
        [$companyInfo] = Helper::maskRiskTexts($clientId, $userId, [$companyInfo], $getTexts, $backFill);

        $companyHashId = $companyInfo['companyHashId'];
        $list = \common\library\customer\Helper::getHashIdAssocStatus($userId, [$companyHashId]);
        $status = 0;
        if (!empty($list[0]['status'])) {
            $status = $list[0]['status'];
        }
        $companyInfo['status'] = $status;
        $companyInfo['status_info'] = $list[0] ?? [];

        $favariteEvent = new \common\library\recommend_plaza\behavior\UserBehavior($this->getLoginUserClientId());
        $favariteEvent->load([
            'refer_id' => $id,
            'refer_type' => \common\library\recommend_plaza\Constant::EVENT_TYPE_COMPANY,
            'client_id' => $this->getLoginUserClientId(),
            'user_id' => $this->getLoginUserId(),
            'enable_flag' => \common\components\BaseObject::ENABLE_FLAG_TRUE
        ]);
        $companyInfo['favorite_flag'] = $favariteEvent->isNew()?0:1;

        return $this->success($companyInfo);
    }

    public function actionBriefProfile($id) {
        $this->validate([
            'id' => 'required|string'
        ]);

        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();

        $companyService = new \common\library\discovery\CompanyService($clientId, $userId, $id);
        $companyInfo = $companyService->getCompanyBaseProfile(true);

        return $this->success($companyInfo);
    }

    public function actionBingSearch(
        $query,
        array $exclude_keywords = [],
        array $exclude_domains = [],
        array $naicsList = [],
        array $country = [],
        array $cacheKeys = [],
        $page = 1,
        $count = 20
    )
    {
        $user = User::getLoginUser();
        $userId = $user->getUserId();
        $clientId = $user->getClientId();
        if ($query) {
            Helper::checkRiskOfText($clientId, $userId, $query);
        }

        $bing = \common\library\ai\service\RecommendService::bingSearch($query, $exclude_keywords, $exclude_domains, $country, $page, $count, $naicsList, $cacheKeys,$clientId, $userId);

        return $this->success($bing);
    }

    public function actionCiqRecordSearch(
        $keyword = '',
        $lang_keywords = "",
        $keyword_operator = Constant::KEYWORD_OPERATOR,
        $company_type = Constant::COMPANY_IMPORTER_TYPE,
        $search_type = Constant::PRODUCT_SEARCH_TYPE,
        $importer = '',
        $importer_country_codes = '',
        $exporter = '',
        $exporter_country_codes = '',
        $brand = '',
        $latest_months = '',
        $page = 1,
        $size = 20,
        $sort = 'HIT_COUNT',
        $hsCode ='',
        $exclude_logistics = 0,
        $has_contacts = 0
    )
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if(!in_array($search_type,\common\library\ciq\Constant::SEARCH_TYPE_LIST)){
            throw new RuntimeException(\Yii::t('common', 'Unsupported type'));
        }
        //根据关键词搜索
        if($keyword){
            Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keyword);
            CiqService::saveSearchHistory($clientId,$userId,$keyword,$search_type);
        }

        $customsRecordList = new \common\library\ciq\CiqRecordSearch();
        $customsRecordList->setKeyword($keyword);
        $customsRecordList->setKeywordOperator($keyword_operator);
        $customsRecordList->setLangKeywords($lang_keywords);
        $customsRecordList->setCompanyType($company_type);
        $customsRecordList->setSearchType($search_type);
        $customsRecordList->setImporter($importer);
        $customsRecordList->setImporterCountryCodes($importer_country_codes);
        $customsRecordList->setExporter($exporter);
        $customsRecordList->setExporterCountryCodes($exporter_country_codes);
        $customsRecordList->setBrand($brand);
        $customsRecordList->setLatestMonths($latest_months);
        $customsRecordList->setPage($page);
        $customsRecordList->setPageSize($size);
        $customsRecordList->setSort($sort);
        $customsRecordList->setHsCode($hsCode);
        $customsRecordList->setHasContacts($has_contacts);
        $customsRecordList->setExcludeLogistics($exclude_logistics);
        $customsRecordList->showCompanyStatus($userId);
        $list = $customsRecordList->find();

        if (!empty($list['list'])) {
            // 风控处理
            $getTexts = function ($i, $item) {
                $texts = [
                    ['id' => "{$i}_name", 'text' => $item['name'] ?? ''],
                    ['id' => "{$i}_address", 'text' => $item['address'] ?? ''],
                ];
                if (!empty($item['records'])) {
                    foreach ($item['records'] as $j => $record) {
                        !empty($record['productDesc']) && $texts[] = [
                            'id' => "{$i}_records_{$j}_desc",
                            'text' => $record['productDesc'],
                        ];
                    }
                }
                return $texts;
            };
            $backFill = function ($i, $item, $maskTexts) {
                $nameKey = "{$i}_name";
                $addressKey = "{$i}_address";
                $item['rname'] = $item['name'];
                isset($maskTexts[$nameKey]) && $item['name'] = $maskTexts[$nameKey];
                isset($maskTexts[$addressKey]) && $item['address'] = $maskTexts[$addressKey];
                if (!empty($item['records'])) {
                    foreach ($item['records'] as $j => $record) {
                        $k = "{$i}_records_{$j}_desc";
                        isset($maskTexts[$k]) && $item['records'][$j]['productDesc'] = $maskTexts[$k];
                    }
                }
                return $item;
            };
            $list['list'] = Helper::maskRiskTexts($user->getClientId(), $user->getUserId(), $list['list'], $getTexts, $backFill);
        }

        $this->success($list);
    }

    public function actionCiqRecordSearchV2(
        $keyword = '',
        array $lang_keywords = [],
        $keyword_operator = Constant::KEYWORD_OPERATOR,
        $company_type = Constant::COMPANY_IMPORTER_TYPE,
        $search_type = Constant::PRODUCT_SEARCH_TYPE,
        $importer = '',
        array $importer_country_codes = [],
        $exporter = '',
        array $exporter_country_codes = [],
        $brand = '',
        $latest_months = '',
        $page = 1,
        $size = 20,
        $sort = 'HIT_COUNT',
        $hs_code ='',
        $exclude_logistics = 0,
        $has_contacts = 0,
        $company_keyword = '',
        $update_date = '',
    )
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        if(!in_array($search_type,\common\library\ciq\Constant::SEARCH_TYPE_LIST)){
            throw new RuntimeException(\Yii::t('common', 'Unsupported type'));
        }
        //根据关键词搜索
        if($keyword){
            Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keyword);
            CiqService::saveSearchHistory($clientId,$userId,$keyword,$search_type);
        }

        if($company_keyword){
            Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $company_keyword);
        }

        $customsRecordList = new \common\library\ciq\CiqRecordSearch();
        $customsRecordList->setKeyword($keyword);
        $customsRecordList->setKeywordOperator($keyword_operator);
        $customsRecordList->setLangKeywords($lang_keywords);
        $customsRecordList->setCompanyType($company_type);
        $customsRecordList->setSearchType($search_type);
        $customsRecordList->setImporter($importer);
        $customsRecordList->setImporterCountryCodes($importer_country_codes);
        $customsRecordList->setExporter($exporter);
        $customsRecordList->setExporterCountryCodes($exporter_country_codes);
        $customsRecordList->setBrand($brand);
        $customsRecordList->setLatestMonths($latest_months);
        $customsRecordList->setPage($page);
        $customsRecordList->setPageSize($size);
        $customsRecordList->setSort($sort);
        $customsRecordList->setHsCode($hs_code);
        $customsRecordList->setHasContacts($has_contacts);
        $customsRecordList->setExcludeLogistics($exclude_logistics);
        $customsRecordList->setCompanyName($company_keyword);
        $customsRecordList->setUpdateDate($update_date);
        $customsRecordList->setVersion(\common\library\ciq\CiqRecordSearch::VERSION_V3);
        $customsRecordList->showCompanyStatus($userId);
        $customsRecordList->setHttpMethodPost();
        $customsRecordList->setClientId($clientId);
        $customsRecordList->setUserId($userId);
        $list = $customsRecordList->find();

        if (!empty($list['list'])) {
            // 风控处理
            $getTexts = function ($i, $item) {
                $texts = [
                    ['id' => "{$i}_name", 'text' => $item['name'] ?? ''],
                    ['id' => "{$i}_address", 'text' => $item['address'] ?? ''],
                ];
                if (!empty($item['records'])) {
                    foreach ($item['records'] as $j => $record) {
                        !empty($record['productDesc']) && $texts[] = [
                            'id' => "{$i}_records_{$j}_desc",
                            'text' => $record['productDesc'],
                        ];
                    }
                }
                return $texts;
            };
            $backFill = function ($i, $item, $maskTexts) {
                $nameKey = "{$i}_name";
                $addressKey = "{$i}_address";
                $item['rname'] = $item['name'];
                isset($maskTexts[$nameKey]) && $item['name'] = $maskTexts[$nameKey];
                isset($maskTexts[$addressKey]) && $item['address'] = $maskTexts[$addressKey];
                if (!empty($item['records'])) {
                    foreach ($item['records'] as $j => $record) {
                        $k = "{$i}_records_{$j}_desc";
                        isset($maskTexts[$k]) && $item['records'][$j]['productDesc'] = $maskTexts[$k];
                    }
                }
                return $item;
            };
            $list['list'] = Helper::maskRiskTexts($user->getClientId(), $user->getUserId(), $list['list'], $getTexts, $backFill);
        }

        $this->success($list);
    }

    public function actionCiqSourceSearch(
        $keyword = '',
        $keyword_operator = Constant::KEYWORD_OPERATOR,
        array $data_country_codes = [],
        $data_type = '',
        $end_date = '',
        $exporter = '',
        array $exporter_country_codes = [],
        $hs_code = '',
        $importer = '',
        array $importer_country_codes = [],
        array $lang_keywords = [],
        $search_type = '',
        $sort_field = Constant::SOURCE_SORT_FIELD_DATE,
        $sort_type = Constant::SOURCE_SORT_TYPE_DESC,
        $start_date = '',
        $company_keyword = '',
        $update_date = '',
        $source_type = '',
        $page = 1,
        $size = 20
    ){
        $this->validate([
            'keyword' => 'string',
            'company_keyword' => 'string',
            'keyword_operator' => 'string|in:'. Constant::KEYWORD_OPERATOR . ',' . Constant::KEYWORD_OPERATOR_AND,
            'data_country_codes' => 'array',
            'data_country_codes.*' => 'string',
            'data_type' => 'string|in:' . Constant::SOURCE_DATA_TYPE_EXPORT . ',' . Constant::SOURCE_DATA_TYPE_IMPORT,
            'end_date' => 'date_format:Y-m-d',
            'exporter' => 'string',
            'exporter_country_codes.*' => 'string',
            'exporter_country_codes' => 'array',
            'hsCode' => 'string',
            'source_type' => 'string|in:CUSTOMS,SHIPPING',
            'importer' => 'string',
            'importer_country_codes' => 'array',
            'importer_country_codes.*' => 'string',
            'lang_keywords' => 'array',
            'lang_keywords.*' => 'string',
            'search_type' => 'string',
            'update_date' => 'string',
            'sort_field' => 'string|in:' . Constant::SOURCE_SORT_FIELD_DATE . ',' . Constant::SOURCE_SORT_FIELD_PKG_NUM . ',' . Constant::SOURCE_SORT_FIELD_PRICE . ',' . Constant::SOURCE_SORT_FIELD_WEIGHT,
            'sort_type' => 'string|in:' . Constant::SOURCE_SORT_TYPE_DESC . ',' . Constant::SOURCE_SORT_TYPE_ASC,
            'start_date' => 'date_format:Y-m-d',
            'page' => 'numeric',
            'size' => 'numeric'
        ]);

        $loginUser = User::getLoginUser();
        $clientId = $loginUser->getClientId();
        $userId = $loginUser->getUserId();

        //根据关键词搜索
        if($search_type && ($keyword || $company_keyword || $hs_code)){
            switch ($search_type) {
                case Constant::SEARCH_TYPE_HSCODE:
                    CiqService::saveSearchHistory($clientId,$userId, $hs_code, $search_type);
                    break;
                case Constant::SEARCH_TYPE_COMPANY:
                    CiqService::saveSearchHistory($clientId,$userId, $company_keyword, $search_type);
                    break;
                case Constant::SEARCH_TYPE_PRODUCT:
                    CiqService::saveSearchHistory($clientId,$userId, $keyword, $search_type);
                    break;
            }
        }

        if($keyword){
            Helper::checkRiskOfText($clientId, $userId, $keyword);
        }

        if($company_keyword){
            Helper::checkRiskOfText($clientId, $userId, $company_keyword);
        }

        $list  = new \common\library\ciq\SourceSearch();
        $list->setKeyword($keyword);
        $list->setKeywordOperator($keyword_operator);
        $list->setDataCountryCodes($data_country_codes);
        $list->setDataType($data_type);
        $list->setEndDate($end_date);
        $list->setExporter($exporter);
        $list->setExporterCountryCodes($exporter_country_codes);
        $list->setHsCode($hs_code);
        $list->setImporter($importer);
        $list->setImporterCountryCodes($importer_country_codes);
        $list->setLangKeywords($lang_keywords);
        $list->setSearchType($search_type);
        $list->setSortField($sort_field);
        $list->setSortType($sort_type);
        $list->setStartDate($start_date);
        $list->setCompanyKeyword($company_keyword);
        $list->setSourceType($source_type);
        $list->showCompanyStatus($userId);
        $list->setUpdateDate($update_date);
        $list->setClientId($clientId);
        $list->setUserId($userId);
        $list->setPage($page);
        $list->setPageSize($size);
        $list->setHttpMethodPost();
        $result = $list->find();
        return $this->success($result);
    }


    public function actionCiqSourceSearchCount(
        $keyword = '',
        $keyword_operator = Constant::KEYWORD_OPERATOR,
        array $data_country_codes = [],
        $data_type = '',
        $end_date = '',
        $exporter = '',
        array $exporter_country_codes = [],
        $hs_code = '',
        $importer = '',
        array $importer_country_codes = [],
        array $lang_keywords = [],
        $search_type = '',
        $sort_field = Constant::SOURCE_SORT_FIELD_DATE,
        $sort_type = Constant::SOURCE_SORT_TYPE_DESC,
        $start_date = '',
        $company_keyword = '',
        $update_date = '',
        $source_type = '',
        $page = 1,
        $size = 0
    ){
        $this->validate([
            'keyword' => 'string',
            'company_keyword' => 'string',
            'keyword_operator' => 'string|in:'. Constant::KEYWORD_OPERATOR . ',' . Constant::KEYWORD_OPERATOR_AND,
            'data_country_codes' => 'array',
            'data_country_codes.*' => 'string',
            'data_type' => 'string|in:' . Constant::SOURCE_DATA_TYPE_EXPORT . ',' . Constant::SOURCE_DATA_TYPE_IMPORT,
            'end_date' => 'date_format:Y-m-d',
            'exporter' => 'string',
            'exporter_country_codes.*' => 'string',
            'exporter_country_codes' => 'array',
            'hsCode' => 'string',
            'source_type' => 'string|in:CUSTOMS,SHIPPING',
            'importer' => 'string',
            'importer_country_codes' => 'array',
            'importer_country_codes.*' => 'string',
            'lang_keywords' => 'array',
            'lang_keywords.*' => 'string',
            'search_type' => 'string',
            'update_date' => 'string',
            'sort_field' => 'string|in:' . Constant::SOURCE_SORT_FIELD_DATE . ',' . Constant::SOURCE_SORT_FIELD_PKG_NUM . ',' . Constant::SOURCE_SORT_FIELD_PRICE . ',' . Constant::SOURCE_SORT_FIELD_WEIGHT,
            'sort_type' => 'string|in:' . Constant::SOURCE_SORT_TYPE_DESC . ',' . Constant::SOURCE_SORT_TYPE_ASC,
            'start_date' => 'date_format:Y-m-d',
            'page' => 'numeric',
            'size' => 'numeric'
        ]);

        $loginUser = User::getLoginUser();
        $clientId = $loginUser->getClientId();
        $userId = $loginUser->getUserId();

        if($keyword){
            Helper::checkRiskOfText($clientId, $userId, $keyword);
        }

        if($company_keyword){
            Helper::checkRiskOfText($clientId, $userId, $company_keyword);
        }

        $list  = new \common\library\ciq\SourceSearch();
        $list->setKeyword($keyword);
        $list->setKeywordOperator($keyword_operator);
        $list->setDataCountryCodes($data_country_codes);
        $list->setDataType($data_type);
        $list->setEndDate($end_date);
        $list->setExporter($exporter);
        $list->setExporterCountryCodes($exporter_country_codes);
        $list->setHsCode($hs_code);
        $list->setImporter($importer);
        $list->setImporterCountryCodes($importer_country_codes);
        $list->setLangKeywords($lang_keywords);
        $list->setSearchType($search_type);
        $list->setSortField($sort_field);
        $list->setSortType($sort_type);
        $list->setStartDate($start_date);
        $list->setCompanyKeyword($company_keyword);
        $list->setSourceType($source_type);
        $list->showCompanyStatus($userId);
        $list->setUpdateDate($update_date);

        $list->setPage($page);
        $list->setPageSize($size);
        $list->setHttpMethodPost();
        $result = $list->find();
        return $this->success($result);
    }

    public function actionCiqCompanySearch(
        $keyword = '',
        $company_type = Constant::COMPANY_IMPORTER_TYPE,
        $country_codes = '',
        $keyword_operator = Constant::KEYWORD_OPERATOR,
        $from_price = '',
        $to_price = '',
        $from_trade_count = '',
        $to_trade_count = '',
        $latest_months = '',
        $update_date = '',
        $sort = Constant::SEARCH_SORT,
        $page = 1,
        $size = 20,
        $has_contacts = 0,
        $exclude_logistics = 0,
        $has_china_trader = 0,
        $is_new_company = 0)
    {

        if (!in_array($company_type, Constant::COMPANY_EXPORTER_MAP)) {
            $this->fail(-1, \Yii::t('common', 'Please enter correct company type'));
        }

        $loginUser = User::getLoginUser();
        $clientId = $loginUser->getClientId();
        $userId = $loginUser->getUserId();

        //根据关键词搜索
        if($keyword){
            Helper::checkRiskOfText($clientId, $userId, $keyword);
            CiqService::saveSearchHistory($clientId,$userId,$keyword,Constant::SEARCH_TYPE_COMPANY);
        }

        $customsCompanyList = new \common\library\ciq\CiqCompanyList();
        $customsCompanyList->setKeyword($keyword);
        $customsCompanyList->setCompanyType($company_type);
        $customsCompanyList->setCountryCodes($country_codes);
        $customsCompanyList->setKeywordOperator($keyword_operator);
        $customsCompanyList->setFromPrice($from_price);
        $customsCompanyList->setToPrice($to_price);
        $customsCompanyList->setFromTradeCount($from_trade_count);
        $customsCompanyList->setToTradeCount($to_trade_count);
        $customsCompanyList->setLatestMonths($latest_months);
        $customsCompanyList->setUpdateDate($update_date);
        $customsCompanyList->setSort($sort);
        $customsCompanyList->setPage($page);
        $customsCompanyList->setPageSize($size);
        $customsCompanyList->setViewUserId($loginUser->getUserId());
        $customsCompanyList->setHasContacts($has_contacts);
        $customsCompanyList->setExcludeLogistics($exclude_logistics);
        $customsCompanyList->setHasChinaTrader($has_china_trader);
        $customsCompanyList->setIsNewCompany($is_new_company);

        $list = $customsCompanyList->find();

        if (!empty($list['list'])) {
            // 风控处理
            $getTexts = function ($i, $item) {
                return [
                    ['id' => "{$i}_name", 'text' => $item['name'] ?? ''],
                    ['id' => "{$i}_address", 'text' => $item['address'] ?? ''],
                ];
            };
            $backFill = function ($i, $item, $maskTexts) {
                $nameKey = "{$i}_name";
                $addressKey = "{$i}_address";
                $item['rname'] = $item['name'];
                isset($maskTexts[$nameKey]) && $item['name'] = $maskTexts[$nameKey];
                isset($maskTexts[$addressKey]) && $item['address'] = $maskTexts[$addressKey];
                return $item;
            };
            $list['list'] = Helper::maskRiskTexts($clientId, $userId, $list['list'], $getTexts, $backFill);
        }

        $this->success($list);

    }

    public function actionProfileEmails($id, $sort = '', $keyword = '', $page = 1, $pageSize = 9, $viewed = 0, array $includePositions = [], array $excludePositions = [], $employeeId = 0)
    {
        $this->validate([
            'id'                    => 'required|string',
            'sort'                  => 'string',
            'keyword'               => 'string',
            'page'                  => 'integer',
            'pageSize'              => 'integer',
            'viewed'                => 'integer',
            'includePositions'      => 'array',
            'includePositions.*'    => 'string',
            'excludePositions'      => 'array',
            'excludePositions.*'    => 'string',
            'employeeId'            => 'integer'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $key = "dx:emails:interest:{$userId}:{$id}";
        $redis = RedisService::sf();
        $isInterest = $redis->get($key);
        if ($viewed) {
            $redis->set($key, date('Y-m-d H:i:s'));
            $redis->expire($key, 86400);
        } else {
            if (!$isInterest) {
                throw new RuntimeException('no viewed', -7);
            }
        }

        $includeNotEmail = !empty($sort) ? false : true;

        $companyUnrelated = new \common\library\user_company_unrelated\UserCompanyUnrelated($clientId, $userId, $id);
        $relateCompanyHashIds = $companyUnrelated->getRelatedCompanyHashIds();

        $contacts = new \common\library\discovery\api\ContactsList($clientId, $userId);
        $contacts->setCompanyHashId($id);
        $contacts->setPage($page);
        $contacts->setSize($pageSize);
        $contacts->setSort('desc');
        $contacts->setIncludeNotEmail($includeNotEmail);
        $contacts->setQ($keyword);
        $contacts->setDefaultSortMode('v2');
        $contacts->setIncludePositions($includePositions);
        $contacts->setExcludePositions($excludePositions);
        $contacts->setIsNewVersion(true);
		$contacts->setQualityFlag(true);
        $contacts->setChildCompanyHashIds($relateCompanyHashIds);
        $contacts->setEmployeeId($employeeId);

        // 屏蔽国内联系人
        $systemId = PrivilegeService::getInstance(\User::getLoginUser()->getClientId())->getMainSystemId();
        $isHKVersion = in_array($systemId, [PrivilegeConstants::HK_LITE_AI_ID, PrivilegeConstants::HK_SMART_AI_ID, PrivilegeConstants::HK_PRO_AI_ID]);
        $isTWVersion = in_array($systemId, [PrivilegeConstants::TW_LITE_AI_ID, PrivilegeConstants::TW_SMART_AI_ID]);
        if ($isHKVersion) {
            $contacts->setExcludeCountryCodes(['HK', 'CN']);
        } else if ($isTWVersion) {
            $contacts->setExcludeCountryCodes(['TW', 'CN']);
        } else if (\User::getLoginUser()->client()->isCNVersion()) {
            $contacts->setExcludeCountryCodes(['CN']);
        }

        $result = $contacts->find();
        $result['page'] = $page;

        return $this->success($result);
    }

    /**
     * 展会搜索
     * @return false|string
     */
    public function actionExhibitionInfoSearchList($keywords='', $countryRegion='',$countryCode='', $purchaseProductCategory='', $exhibitionDate='', $page = 1, $pageSize = 20){
        $user = \User::getLoginUser();

        Helper::checkRiskOfText($user->getClientId(), $user->getUserId(), $keywords);

        $exhibition = new \common\library\discovery\api\Exhibition($user->getClientId(), $user->getUserId());
        $exhibition->setKeywords($keywords);
        if($countryRegion) $exhibition->setCountryRegion($countryRegion);
        if($countryCode) $exhibition->setCountryCode($countryCode);
        if($purchaseProductCategory) $exhibition->setPurchaseProductCategory($purchaseProductCategory);
        if($exhibitionDate) $exhibition->setExhibitionDate($exhibitionDate);
        $exhibition->setPage($page);
        $exhibition->setPageSize($pageSize);
        $data = $exhibition->getExhibitionInfoSearchList();
        return $this->success($data);
    }

    /**
     * 上线展会数据统计
     */
    public function actionExhibitionDataTotal(){

        $user = \User::getLoginUser();
        $exhibition = new \common\library\discovery\api\Exhibition($user->getClientId(), $user->getUserId());
        $data = $exhibition->getExhibitionDataTotal();
        return $this->success($data);
    }


    /**
     * 展会下拉框数据
     */
    public function actionExhibitionDownBoxData(){
        $user = \User::getLoginUser();
        $exhibition = new \common\library\discovery\api\Exhibition($user->getClientId(), $user->getUserId());
        $data = $exhibition->getExhibitionDownBoxData();
        return $this->success($data);
    }

    /**
     * DX详情页-公司展会信息列表
     *
     */
    public function actionCompanyExhibitionInfo(string $companyHashId,int $page = 1,int $pageSize = 20){
        $this->validate([
            'companyHashId'                    => 'required|string',
        ]);

        $user = \User::getLoginUser();
        $exhibition = new \common\library\discovery\api\Exhibition($user->getClientId(), $user->getUserId());
        $exhibition->setCompanyHashId($companyHashId);
        $exhibition->setPage($page);
        $exhibition->setPageSize($pageSize);
        $companyUnrelated = new UserCompanyUnrelated($user->getClientId(), $user->getUserId(), $companyHashId);
        $excludeCompanyHashIds = $companyUnrelated->getUnrelatedCompanyHashIds();
        $exhibition->setMergeCompanyInfo($excludeCompanyHashIds);
        $data = $exhibition->getCompanyExhibitionInfo();
        return $this->success($data);
    }

    /**
     * 客户背景接口
     * 3 潜客运营
     * @param int $type
     * @param $relate_id
     * @return string
     */
    public function actionBackground(int $type, $relate_id)
    {
        $this->validate([
            'type' => 'required|in:' . implode(',', \common\library\discovery\Constant::BACKGROUND_TYPE_LIST),
            'relate_id' => 'required'
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $data = [];
        switch ($type) {
            case \common\library\discovery\Constant::BACKGROUND_TYPE_COMPANY_HASH_ID:
                $data = \common\library\ai\service\RecommendService::getCompanyById($relate_id);
                break;
            case \common\library\discovery\Constant::BACKGROUND_TYPE_DOMAIN:
                $data = \common\library\ai\service\RecommendService::getCompanyByDomain($relate_id);
                break;
            case \common\library\discovery\Constant::BACKGROUND_TYPE_IDENTITY_ID:
                $email = \common\library\track\TrackHelper::getSessionByIdentity($clientId, $relate_id)['email'] ?? '';
                $domain = \EmailUtil::getDomain($email);
                $data = \common\library\ai\service\RecommendService::getCompanyByDomain($domain);
                break;
            default:
                break;
        }

        return $this->success($data);
    }

    /**
     * 背景调查相关数量统计
     * @param $company_hash_id
     * @return false|string
     */
    public function actionBackgroundProfileBriefInfo($company_hash_id)
    {

        $this->validate([
            'company_hash_id' => 'required'
        ]);
        $user = User::getLoginUser();
        $companyService = new \common\library\discovery\CompanyService($user->getClientId(), $user->getUserId());
        $result = $companyService->getProfileBriefInfo($company_hash_id);
        return $this->success($result);
    }
    /**
     * 排行榜
     * POTENTIAL_BUYER：潜力买家
     * ACTIVE_BUYER  ：活跃买家
     * TRANSACTION_VOLUME：交易规模
     */
    public function actionRecommendCompanyRanking(string $rankingType = 'TRANSACTION_VOLUME'){

        $this->validate([
            'rankingType'                    => 'required|string',
        ]);

        $user = \User::getLoginUser();
        $companyRanking = new \common\library\discovery\api\CompanyRanking($user->getClientId(), $user->getUserId());
        $companyRanking->setRankingType($rankingType);
        $data = $companyRanking->recommendCompanyRanking();

        if (!empty($data)) {
            $companyHashIds = array_column($data, 'companyHashId');
            $list = \common\library\customer\Helper::getHashIdAssocStatus($user->getUserId(), $companyHashIds);
            $statusMap = array_column($list, null, 'company_hash_id');
            foreach ($data as $k => $v) {
                $data[$k]['archive_type'] = $statusMap[$v['companyHashId']]['status'] ?? 0;
                $data[$k]['status_info'] = $statusMap[$v['companyHashId']] ?? [];
                $data[$k]['message'] = Helper::translateToZh($v['message']);
            }
        }
        return $this->success($data);
    }


    /**
     * 智能获客 初始页 公司汇总信息
     */
    public function actionCompanyAggregate(){

        $user = \User::getLoginUser();
        $company = new \common\library\discovery\api\Company($user->getClientId(), $user->getUserId());
        $data = $company->companyAggregate();
        if(isset($data['tolConcatCnt'])){ //临时写死，后续调整 @alex
            $data['tolConcatCnt'] = '*********';
        }
        return $this->success($data);
    }

    /**
    * 联想词条及数量
    *
    * @param string $keyword
    * @throws ProcessException
    */
    public function actionAssociateSearch($keyword = '')
    {
        $this->validate([
            'keyword' => 'required|string'
        ]);

        $user = \User::getLoginUser();
        $search = new Search($user->getClientId(), $user->getUserId());
        $search->setParams(['keyword' => $keyword]);
        $data = $search->associateSearch();
        $this->success($data);
    }

    /**
     * 查看合并公司列表
     *
     * @param string $company_hash_id
     */
    public function actionGetMergeCompanies(string $company_hash_id)
    {
        $this->validate([
            'company_hash_id' => 'required|string'
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $search = new Search($clientId, $userId);
        $search->setParams(['companyHashId' => $company_hash_id]);
        $data = $search->getMergeCompanies();

        $this->success($data);
    }


    /**
     *   推荐数据   精简版 v3
     */
    public function actionRecommendList($refresh = 'false',string $type = 'NORMAL',int $size = 6)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $recommend = new \common\library\discovery\api\Recommend($clientId, $userId);
        $recommend->setParams([
            'refresh' => $refresh,
            'type' => $type,
            'size' => $size,
        ]);
        $data = $recommend->getListV3();

        if (!empty($data['list'])) {
            $companyHashIds = array_column($data['list'], 'companyHashId');
            $list = \common\library\customer\Helper::getHashIdAssocStatus($user->getUserId(), $companyHashIds);
            $statusMap = array_column($list, null, 'company_hash_id');
            foreach ($data['list'] as $k => $v) {
                $data['list'][$k]['archive_type'] = $statusMap[$v['companyHashId']]['status'] ?? 0;
                $data['list'][$k]['status_info'] = $statusMap[$v['companyHashId']] ?? [];
                $data['list'][$k]['message'] = Helper::translateToZh($v['message']);
            }
        }

        return $this->success($data);
    }

    /**
     * 智能获客 列表页 搜索词推荐查询
     */
    public function actionRecommendRelationKeyWord(string $keyword){
        $this->validate([
            'keyword' => 'required|string'
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $recommend = new \common\library\discovery\api\Recommend($clientId, $userId);
        $data = $recommend->recommendRelationKeyWord($keyword);
        return $this->success($data);
    }


    /**
     * 客户推荐 主营产品 GPT匹配接口
     */
    public function actionIntelligentInferIndustryByProducts(string $products ,string $customProducts=''){
        $this->validate([
            'products'    => 'string',
        ]);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();
        $recommend = new \common\library\discovery\api\Recommend($clientId, $userId);
        $data = $recommend->intelligentInferIndustryByProducts($products ,$customProducts);
        return $this->success($data);
    }

    public function actionIpAddressInfo($address)
    {
        $this->validate([
            'address' => 'required',
        ]);
        $data = array_merge(\common\library\util\IP::getInstance()->find($address) ?? [], \common\library\util\IP::getInstance()->findIndustry($address) ?? []);
        $this->success($data);
    }

    public function actionGetCompanyProfile($company_hash_id)
    {
        $this->validate([
            'company_hash_id' => 'required',
        ]);

        $api = new InnerApi('okki_leads_service');
        $api->setHttpMethod(InnerApi::HTTP_METHOD_GET);
        $data = $api->call('getCompanyProfile', [
            'company_hash_id' => $company_hash_id,
        ]);

        $this->success($data['data'] ?? []);
    }

    public function actionContactList($keyword = '', array $origin = [], array $country = [], $order = '', $page = 1, $pageSize = 20)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $system = \common\library\privilege_v3\PrivilegeService::getInstance($clientId)->getMainSystemId();
        if (!in_array($system, \common\library\privilege_v3\PrivilegeConstants::PERSONAL_SYSTEMS)) {
            throw new \RuntimeException(\Yii::t('common', '暂无权限'));
        }

        $originApi = new \common\library\setting\library\origin\OriginApi($clientId);

        //联系人-线索来源映射：解锁-OKKI_LEADS，导入-自主开发
        if (empty($origin)){
            $originIds = [FieldItemSetting::SYS_ORIGIN_ID_OKKI_LEADS, $originApi->getIdByName('自动挖掘'), $originApi->getIdByName('自主开发'), $originApi->getIdByName('AI背调')];
        }else{
            $originIds = [];
            foreach ($origin as $item){
                switch ($item){
                    case 1: //潜客发现
                        $originIds[] = FieldItemSetting::SYS_ORIGIN_ID_OKKI_LEADS;
                        $originIds[] = $originApi->getIdByName('AI背调');
                        break;
                    case 2: //自动挖掘
                        $originIds[] = $originApi->getIdByName('自动挖掘');
                        break;
                    case 3: //导入
                        $originIds[] = $originApi->getIdByName('自主开发');
                        break;
                    default:
                        break;
                }
            }
        }

        $list = new \common\library\lead_v3\LeadList($userId);
        $list->setOriginList($originIds);
        $list->setUserNum([0]);

        if ($keyword){
            $list->setKeyword($keyword);
        }

        if ($country){
            $list->setCountry($country);
        }

        if ($order){
            $list->setOrder($order);
        }

        $list->setOffset(($page - 1) * $pageSize);
        $list->setLimit($pageSize);

        $data = $list->find();

        //自定义字段
        $customFieldList = new \common\library\custom_field\FieldList($clientId);

        //邮箱
        $customFieldList->setType(Constants::TYPE_LEAD_CUSTOMER);
        $customFieldData = $customFieldList->rawData();
        $emailCustomField = collect($customFieldData)->where('name', 'emails')->first();
        $emailCustomFieldId = $emailCustomField['id'];

        //已背调邮箱列表
        $backgroundCheckEmailsField = collect($customFieldData)->where('name', '已背调邮箱')->first();
        $backgroundCheckEmailsFieldId = $backgroundCheckEmailsField['id'];
        //已背调联系人employee_id
        $backgroundCheckEmployeeField = collect($customFieldData)->where('name', '已背调联系人')->first();
        $backgroundCheckEmployeeFieldId = $backgroundCheckEmployeeField['id'] ?? '';

        //公司logo
        $customFieldList->setType(Constants::TYPE_LEAD);
        $customFieldData = $customFieldList->rawData();
        $logoCustomField = collect($customFieldData)->where('name', 'logo')->first();
        $logoCustomFieldId = $logoCustomField['id'];

        $originApi = new \common\library\setting\library\origin\OriginApi($clientId);

        $ret = [];
        $backgroundCheckEmailList = [];
        foreach ($data as $datum) {
            //线索联系人
            $customerList = new \common\library\lead\LeadCustomerList($clientId);
            $customerList->setLeadId($datum['lead_id']);
            $customerList->getFormatter()->setSpecifyFields(['customer_id', 'name', 'post', 'tel_list', 'external_field_data', 'plan_count', 'contact']);
            $customer = $customerList->find()[0] ?? [];

            $company_external_field_data = $datum['external_field_data'] ?? [];
            $customer_external_field_data = $customer['external_field_data'] ?? [];

            $company_external_field_data = collect($company_external_field_data)->keyBy('id')->toArray();
            $customer_external_field_data = collect($customer_external_field_data)->keyBy('id')->toArray();

            $logo = $company_external_field_data[$logoCustomFieldId]['value'] ?? '';
            $emails = json_decode($customer_external_field_data[$emailCustomFieldId]['value'] ?? '', true);
            $emails = is_array($emails)?$emails:[];
            $employee = json_decode($customer_external_field_data[$backgroundCheckEmployeeFieldId]['value'] ?? '', true);
            $employeeId = intval($employee['employee_id'] ?? 0) ?? 0;

            $backgroundCheckEmails = json_decode($customer_external_field_data[$backgroundCheckEmailsFieldId]['value'] ?? '', true);
            $backgroundCheckEmails = is_array($backgroundCheckEmails) ? $backgroundCheckEmails : [];
            if ($backgroundCheckEmails){
                $backgroundCheckEmailList[$datum['lead_id']] = $backgroundCheckEmails;
            }

            $tel_list = $customer['tel_list'] ?? [];
            $phone_numbers = [];
            if ($tel_list){
                foreach ($tel_list as $tel){
                    $phone_numbers[] = $tel[0]. $tel[1];
                }
            }

            $origin = $originApi->getOneNameById($datum['origin']);

            //ai背调邮箱有效性检测
            if ($origin == 'AI背调'){
                $backgroundCheckEmails = array_merge($backgroundCheckEmails, $emails);
            }

            $originName = match ($origin) {
                '自动挖掘' => '自动挖掘',
                'AI背调' => 'AI背调',
                'OKKI Leads' => '潜客发现',
                default => '手动导入',
            };

            foreach (($customer['contact'] ?? []) as $contact) {
                if (($contact['type'] ?? '') == 'twitter') {
                    $twitterUrl = $contact['value'];
                }
                if (($contact['type'] ?? '') == 'linkedin') {
                    $linkedinUrl = $contact['value'];
                }
                if (($contact['type'] ?? '') == 'facebook') {
                    $facebookUrl = $contact['value'];
                }
            }
            $ret[] = [
                'nickname' => $customer['name'] ?? '',
                'position' => $customer['post'] ?? '',
                'emails' => $emails,
                'phone_numbers' => $phone_numbers ?? [],
                'company_name' => $datum['company_name'] ?? '',
                'origin' => $originName,
                'create_time' => $datum['create_time'] ?? '',
                'company_hash_id' => $datum['company_hash_id'] ?? '',
                'country_code' => $datum['country'] ?? '',
                'plan_count' => $customer['plan_count'] ?? 0,
                'lead_id' => $datum['lead_id'] ?? '',
                'contact' => $customer['contact'] ?? [],
                'logo_url' => $logo,
                'identity_ids' => $identityIds ?? [],
                'customer_id' => $customer['customer_id'] ?? '',
                'employee_id' => $employeeId,
                'twitter_url' => $twitterUrl ?? '',
                'linkedin_url' => $linkedinUrl ?? '',
                'facebook_url' => $facebookUrl ?? '',
            ];
        }

        if ($backgroundCheckEmailList){
            $employeeApi = new \common\library\discovery\api\Employee($clientId, $userId);
            $checkEmails = array_values(array_unique(array_merge(...array_values($backgroundCheckEmailList))));
            $mailQualityMap = $employeeApi->getMailQualityRating($checkEmails);

            foreach ($ret as &$item){
                if (isset($backgroundCheckEmailList[$item['lead_id']])){
                    foreach ($item['emails'] as $email){
                        $item['mailQualityRating'][$email] = $mailQualityMap[$email] ?? 0;
                    }
                } else {
                    $item['mailQualityRating'] = array_fill_keys($item['emails'], 0);
                }
            }
        }

        $ret = array_map(function ($item) {
            $item['origin'] = \Yii::t('field', $item['origin']);
            return $item;
        }, $ret);

        $count = $list->count();

        //自动挖掘联系人数量
        $originId = $originApi->getIdByName('自动挖掘');
        $list->setOriginList([$originId]);
        $auto_dig_count = $list->count();

        $result = [
            'count' => $count,
            'auto_dig_count' => $auto_dig_count,
            'list' => $ret
        ];

        return $this->success($result);
    }

    public function actionContactCount()
    {
        $user = User::getLoginUser();

        $list = new \common\library\lead_v3\LeadList($user->getUserId());
        return $this->success(['total' => $list->count()]);
    }

    public function actionBackgroundCheckAdoptionStatus(int $report_id, array $emails)
    {
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        $ret = array_fill_keys($emails, 0);

        $customerList = new \common\library\lead\LeadCustomerList($clientId);
        $customerList->setFields(['email']);
        $customerList->setEmail($emails);
        $customer = $customerList->find();

        if (!$customer){
            return $this->success($ret);
        }

        $api = new \common\library\okki_personal\OkkiIOApi($clientId, $userId);
        $adoptionStatus = $api->getBackgroundCheckAdoptionStatus($report_id);
        if (!$adoptionStatus){
            return $this->success($ret);
        }

        $customer = collect($customer)->pluck('email')->toArray();
        foreach ($emails as $email){
            $ret[$email] = (int)in_array($email, $customer);
        }

        return $this->success($ret);
    }

    public function actionQueryBackgroundTask(string $refer_id, int $refer_type)
    {
        $this->validate([
            'refer_id'   => 'required|string|not_empty',
            'refer_type' => 'required|integer|in:7',
        ]);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        $userId = $user->getUserId();

        try {
            $api = new InnerApi('okki_leads_service');
            $api->setHttpMethod(InnerApi::HTTP_METHOD_GET);
            $params = [
                'refer_type' => $refer_type,
                'refer_ids' => [$refer_id],
                'page_size' => 1,
                'page' => 1,
                'client_id' => $clientId,
                'user_id' => $userId,
            ];
            $result = $api->call('queryTaskByReferIds', $params);
        } catch (\Exception $e) {
            LogUtil::error('queryTaskByReferIds api failed', ['errorMsg' => $e->getMessage()]);
            return $this->fail(-1, '查询失败');
        }
        $this->success([
            'task_id' => $result['data'][0]['task_id'] ?? 0,
            'params' => $result['data'][0]['params'] ?? [],
            'refer_id' => $result['data'][0]['refer_id'] ?? '',
        ]);
    }

    public function actionGetEmployeeById(int $employee_id)
    {
        $this->validate([
            'employee_id' => 'required|integer',
        ]);
        $api = new InnerApi('leads_service');
        $api->setHttpMethod(InnerApi::HTTP_METHOD_GET);

        try {
            $data = $api->call('getEmployeeById', ['employee_id' => $employee_id]);
        } catch (\Exception $exception){
            \LogUtil::info('getEmployeeById api failed', ['employee_id' => $employee_id, 'message' => $exception->getMessage()]);
            $this->fail(-1, '查询失败');
        }

        $this->success($data['data'] ?? []);
    }
}


