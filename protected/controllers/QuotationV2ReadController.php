<?php

use common\library\ai\classify\product\ProductApplyV2;
use common\library\export_v2\ExportReadAction;
use common\library\foundation\field\FieldReadActions;
use common\library\import\ImportReadActions;
use common\library\invoice\export\InvoiceExportFileList;
use common\library\invoice\Invoice;
use common\library\invoice\InvoiceProductImportReadActions;
use common\library\oms\quotation\QuotationConstants;
use common\library\oms\quotation\download\QuotationExportExecutor;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeFieldV2;
use common\library\setting\library\import_template\OrderProductImportTemplate;

class QuotationV2ReadController extends Controller
{
    use FieldReadActions;
    use ExportReadAction;
    use ImportReadActions;
    use InvoiceProductImportReadActions;

    public function initFieldSetting()
    {
        $this->module = Constants::TYPE_QUOTATION;
    }

    public function initImportRead()
    {
        // 订单产品导入
        $this->importType = Constants::TYPE_PRODUCT;
    }

    public function initExportRead()
    {
        $this->exportType = Constants::TYPE_QUOTATION;
        $this->exportScene = Constants::TYPE_QUOTATION;
        $this->exportPrivilege = PrivilegeConstants::PRIVILEGE_SETTING_QUOTATION_EXPORT;
        $this->exportExecutor = QuotationExportExecutor::class;
    }

    public function beforeAction($action)
    {
        $this->initProductImportRead(Constants::TYPE_QUOTATION, OrderProductImportTemplate::QUOTATION_PRODUCT_IMPORT_TEMPLATE);
        return parent::beforeAction($action);
    }

    public function actionInfo(
        $quotation_id = null,
        $quotation_no = null,
        $scene = Invoice::SYSTEM_MODE_CRM,
        $skip_view_privilege = false,
        $apply_form_id = 0
    ) {
        $this->validate([
            'quotation_id' => 'numeric|required_without:quotation_no',
            'quotation_no' => 'required_without:quotation_id',
            'groups' => 'array',
            'groups.*' => 'integer',
            'skip_view_privilege' => 'bool',
            'apply_form_id' => 'integer',
        ]);

        $user = \User::getLoginUser();
        if ($quotation_no) {
            $quotation = new \common\library\oms\quotation\Quotation($user->getClientId());
            $quotation->loadByNo($quotation_no);
        } else {
            $quotation = new \common\library\oms\quotation\Quotation($user->getClientId(), $quotation_id);
        }

        if (empty($scene)){
            $scene = Invoice::SYSTEM_MODE_CRM;
        }

        $quotation->setScene($scene);

        if (!$quotation->isExist() && $scene!= QuotationConstants::SCENE_RECYCLE) {
            throw new \RuntimeException(\Yii::t('invoice', '报价单不存在'));
        }

        if (!$quotation->canView() && !$skip_view_privilege) {
            throw new RuntimeException(\Yii::t('account', 'No permission to view'), ErrorCode::CODE_INVOICE_NOT_PRIVILEGE_VIEW);
        }

        $fieldPrivilegeFormatterFlag = !$skip_view_privilege;
        $quotation->approvalFieldPrivilege($apply_form_id);
        if ($apply_form_id > 0) {
            $applyForm = new \common\library\approval_flow\ApplyForm($apply_form_id, $this->getLoginUserId(), true);
            //只有审批中的才需要展示审批内容
            if (!$applyForm->isNew() && $applyForm->isApproving()) {
                $fieldPrivilegeFormatterFlag = true;
                $scene = \common\library\oms\quotation\QuotationConstants::SCENE_APPROVAL;
            }
        }

        switch ($scene) {
            case \common\library\oms\quotation\QuotationConstants::SCENE_APPROVAL:
                $quotation->bindApplyForm($applyForm);
                $quotation->getFormatter()->approvalInfoSetting();
                $quotation->getFormatter()->setFormatterV2Privilege(true, $fieldPrivilegeFormatterFlag, PrivilegeFieldV2::SCENE_OF_VIEW);
                break;
            default:
                $quotation->getFormatter()->detailInfoSetting($scene);
                $quotation->getFormatter()->setFormatterV2Privilege(true, $fieldPrivilegeFormatterFlag, PrivilegeFieldV2::SCENE_OF_VIEW);
        }

        $data = $quotation->getAttributes();
//      $data['manageable_flag'] 这个参数应该可以不要
        $data['can_unlock'] = $quotation->canUnlock();

        $this->success($data);
    }

    public function actionList(
        $is_fetch_subordinate = 0,
        array $create_user = [],
        array $handler = [],
        $sort_field = 'update_time',
        $sort_type = 'desc',
        $pin = 0,
        $keyword = '',//名称筛选
        $quotation_no_keyword = '',
        $approval_status = '',
        array $status = [],
        $company_keyword = '',
        $customer_keyword = '',
        $serial_keyword='',
        $start_date = null,
        $end_date = null,
        $page_no = 1,
        $page_size = 20,
        $recent_flag = 0,
        $start_create_time = null,
        $end_create_time = null,
        $start_quotation_date = null,
        $end_quotation_date = null,
        $opportunity_id = 0,
        $scene = 'quotation',
        $report_item_unique_key ='',
        $product_no_keyword = '',
        $product_name_keyword = '',
        $product_model_keyword = '',
        array $query_filters = [],
    ) {
        $this->validate([
            'page' => 'integer',
            'page_size' => 'integer',
            'sort_field' => 'regex:/^\w{2,32}$/',
            'sort_type' => 'string|in:asc,desc',
        ]);

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter)
        {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }

        $api = new \common\library\oms\quotation\QuotationApi();
        $list = $api->webList($params);

        $this->success($list);
    }


    /**
     * 报价单列表金额统计
     * 筛选条件参数需与列表接口保持一致
     * post 请求
     */
    public function actionListStatistics(
        $is_fetch_subordinate = 0,
        array $create_user = [],
        array $handler = [],
        $sort_field = 'update_time',
        $sort_type = 'desc',
        $pin = 0,
        $keyword = '',//名称筛选
        $quotation_no_keyword = '',
        $approval_status = '',
        array $status = [],
        $company_keyword = '',
        $customer_keyword = '',
        $serial_keyword='',
        $start_date = null,
        $end_date = null,
        $recent_flag = 0,
        $start_create_time = null,
        $end_create_time = null,
        $start_quotation_date = null,
        $end_quotation_date = null,
        $opportunity_id = 0,
        $scene = 'quotation',
        $report_item_unique_key ='',
        $product_no_keyword = '',
        $product_name_keyword = '',
        $product_model_keyword = '',
        array $query_filters = [],
    )
    {

        $params = [];
        $method = new \ReflectionMethod(__METHOD__);
        foreach ($method->getParameters() as $parameter)
        {
            $params[$parameter->name] = ${$parameter->name} ?? null;
        }
        $params['show_quote_fields'] = true;

        $user = User::getLoginUser();

        $params['scene'] = 'statistics';
//        $req = new \common\library\purchase\purchase_order\PurchaseOrderListReq($params, $user->getClientId());
//        $data = \common\library\purchase\purchase_order\API::webList($user->getUserId(), $req);

        $api = new \common\library\oms\quotation\QuotationApi();
        $list = $api->webStatistics($params);
        return $this->success($list);
    }


    /**
     * 获取功能字段属性值
     * @param string $data
     *
     * @return void
     */
    public function actionFunctionFieldValue($data, $modified_fields = null)
    {
        $this->validate([
            'data' => 'required',
        ]);
        $data = json_decode($data,true);
        $modified_fields = is_array($modified_fields) ? $modified_fields : json_decode($modified_fields, true);

        $user = \User::getLoginUser();
        $clientId = $user->getClientId();

        $functionFieldVal = \common\library\oms\quotation\QuotationApi::functionFieldValue($data, $clientId, $modified_fields);
        $this->success($functionFieldVal);
    }

    public function actionExportFileList($quotation_id = 0, $user_id = 0, $type = 0, $recent_flag = 0, $page = 1, $page_size = 20)
    {
        $loginUser = User::getLoginUser();
        $listObj = new InvoiceExportFileList($loginUser->getUserId(), Constants::TYPE_QUOTATION);
        $listObj->setReferId($quotation_id);
        $listObj->setType($type);
        $listObj->setUserId($user_id);
        if ($recent_flag) {
            $listObj->setRecentFlag($recent_flag);
        }
        $listObj->setOffset(($page - 1) * $page_size);
        $listObj->setLimit($page_size);

        $list = [];
        $count = $listObj->count();
        if ($count > 0) {
            $list = $listObj->find();
        }
        $return = [
            'total' => $count,
            'list' => $list
        ];
        return $this->success($return);
    }

    public function actionQuotationProductImportList($file_id, $external_field_data)
    {
        $this->actionProductImportList($file_id, $external_field_data);
    }
}
