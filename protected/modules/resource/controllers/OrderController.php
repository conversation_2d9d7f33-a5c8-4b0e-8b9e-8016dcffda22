<?php
/**
 * Created by PhpStor<PERSON>.
 * User: Tony
 * Date: 2019-07-23
 * Time: 17:01
 */

use common\library\account\Client;
use common\library\custom_field\CustomFieldFormatter;
use common\library\custom_field\FieldList;
use common\library\exchange_rate\ExchangeRateService;
use common\library\invoice\Helper;
use common\library\invoice\OrderList;
use common\library\invoice\Order;
use common\library\invoice\status\InvoiceStatusService;
use common\library\cash_collection\CashCollectionList;
use common\library\custom_field\CustomFieldService;
use common\library\oms\common\OmsConstant;
use common\library\oms\order_link\Constant;
use common\library\setting\library\fund\relation\CostItemInvoiceRelationApi;

class OrderController extends ResourceController
{

    public function actionFields($client_id, $user_id)
    {
        User::setLoginUserById($user_id);
        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_ORDER);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();

        $newFields = [
            [
                "id" => "collection_date",
                "name" => "回款日期",
                "base" => 1,
                "field_type" => 4,
                "ext_info" => null,
                "require"=> 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "amount",
                "name" => "本次回款金额",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "amount_rmb",
                "name" => "本次回款金额（RMB）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "amount_usd",
                "name" => "本次回款金额（USD）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "collect_amount_rmb",
                "name" => "已回款（RMB）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "collect_amount_usd",
                "name" => "已回款（美元）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "not_collect_amount_rmb",
                "name" => "待回款（RMB）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "not_collect_amount_usd",
                "name" => "待回款（美元）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "percentage_rmb",
                "name" => "回款百分比（RMB）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "percentage_usd",
                "name" => "回款百分比（美元）",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "status_name",
                "name" => "回款状态",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 0
            ],
            [
                "id" => "percent_amount",
                "name" => "附加费用计算金额或占比",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 1,
                "group_id" => 2,
            ],
            [
                "id" => "percent_type",
                "name" => "附加费用计算类型",
                "base" => 1,
                "field_type" => 5,
                "ext_info" => null,
                "require" => 1,
                "default" => "",
                "is_list" => 1,
                "group_id" => 2,
            ]
        ];

        $list = array_merge($list, $newFields);

        foreach ($list as $key => $item) {
            if ($item['is_list'] ?? 0 == 1) {
                $list[$key]['group_id'] = 2;
            } else {
                $list[$key]['group_id'] = 1;
            }
        }

        $this->success($list);
    }

    public function actionList($client_id, $user_id, $page_size = 20, $page = 1, $start_time = '', $end_time = '', $time_type = 1, $status = '', $removed = 0, $approval = null, $approval_with_draft = 0)
    {
        User::setLoginUserById($user_id);
        $user = User::getLoginUser();

        $this->validate([
            'start_index' => 'integer',
            'count'       => 'integer|max:1000',
            'start_time'  => 'date',
            'end_time'    => 'date',
            'time_type'   => 'integer|in:1,2,3',
        ], [
            'start_index' => $page,
            'count'       => $page_size,
            'start_time'  => $start_time,
            'end_time'    => $end_time,
            'time_type'   => $time_type,
        ]);
        $page_size = !empty($page_size) && is_numeric($page_size) ? $page_size : 20;
        $list = new OrderList($user->getUserId());
        $list->setSkipPermissionCheck(true);
        $list->setLimit($page_size);
        $list->setOffset(($page - 1) * $page_size);
        $list->setApprovalStatus($approval);
        $list->getFormatter()->setIgnorePrivilege(true);
        $list->getFormatter()->openApiListInfoSetting();

        if ($time_type == 1) {
            $list->setStartUpdateDate($start_time);
            $list->setEndUpdateDate($end_time);
        } else if ($time_type == 2) {
            $list->setStartCreateTime($start_time);
            $list->setEndCreateTime($end_time);
        } else if ($time_type == 3) {
            $list->setStartAccountDate($start_time);
            $list->setEndAccountDate($end_time);
        }

        if (!empty($status)) {
            //订单状态
            $status = explode(',', $status);
            $list->setStatus($status);
        }

        if ($approval && !$approval_with_draft) {
            //草稿状态
            $service = new InvoiceStatusService($client_id, Constants::TYPE_ORDER);
            $beginStatus = $service->beginStatus();
            $status = $beginStatus['id'];
            $list->setNotDraft($status);
        }

        if ($removed) {
            $list->setEnableFlag(Order::ENABLE_FLAG_FALSE);
        }

        $data = [
            'list' => $list->find(),
            'totalItem' => $list->count(),
            'count' => $list->count(),
        ];

        $this->success($data);
    }

    public function actionPush($client_id, $user_id, $data)
    {
        $allHeader = Util::getAllHeader();
        $uniqueId  = uniqid();
        $platform  = $allHeader['Platform'] ?? '';
        LogUtil::info("push client_id={$client_id} user_id={$user_id} data={$data} platform={$platform} uniqueId={$uniqueId}");
        User::setLoginUserById($user_id);
        $data = json_decode($data, true, 512, JSON_BIGINT_AS_STRING);
        $this->validate([
            'order_id' => "numeric|min:0|regex:/^[0-9]+$/",//非负整数0
            'user_id' => 'integer',
            'product_list' => 'array',
            'product_list.*.product_id' => 'numeric',
            'product_list.*.sku_id' => 'numeric',
            'status' => 'numeric',
            'departments' => 'array',
            'departments.*.department_id'=> 'required|numeric',
            'departments.*.rate'=> 'numeric',
            'users' => 'array',
            'users.*.user_id' => 'numeric',
            'users.*.rate' => 'numeric',
            'account_date' => 'date',
            'create_time' => 'date',
            'create_user' => 'numeric',
            'country' => 'string',
            'receive_remittance_way' => 'string',
            'opportunity_id' => 'numeric',
            'shipment_deadline_remark' => 'string',
            'cost_list' => 'array',// 额外费用列表
            'cost_list.*.cost_name' => 'string',
            'cost_list.*.percent_type' => 'numeric',
            'cost_list.*.percent_amount' => 'numeric',
            'cost_list.*.cost' => 'numeric',
            'source_type' => 'numeric',// 订单类型
            'file_list' => 'array',// 订单附件
            'remark' => 'string', // 订单备注
            'handler' => 'array', // 订单处理人，不填默认创建人
            'handler.*' => 'numeric', // 订单处理人，不填默认创建人
            'customer_id' => 'numeric',
            'company_id' => 'numeric',
            'tax_refund_type' => 'numeric|in:' . implode(',', array_keys(OmsConstant::ORDER_TAX_REFUND_TYPE_MAP)),
            'capital_account_id' => 'numeric',
        ], $data);
        $orderId = $data['order_id'] ?? '';
        $orderNo = $data['order_no'] ?? '';
        $companyId = $data['company_id'] ?? 0;
        $operatorUserId = $data['user_id'] ?? $user_id;
        $productList = $data['product_list'] ?? [];
        $fileList = $data['file_list'] ?? [];
        $status = $data['status'] ?? null;
        $refreshProduct = $data['refresh_product'] ?? null;

        $data = $this->filterPushFields($client_id, $data);
        $isNew = false;

        // 防止个别客户只传order_no不传order_id的问题
        if (empty($orderId) && !empty($orderNo)) {
            try {
                $order = new Order($operatorUserId);
                $order->loadByNo($orderNo);
                $orderId = $order->order_id;
                $isNew = false;
            } catch (\RuntimeException $e) {}
        }

        $validateFields = [
            'create_user',
            'company_id',
            'customer_id',
            'user_id',
            'capital_account_id',
        ];
        foreach ($validateFields as $field) {
            if (isset($data[$field]) && empty(trim($data[$field]))) {
                throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>$field]),404);
            }
        }

        // 由于validate 无法验证product_id=" "的情况
        foreach ($productList as $productItem) {
            if (isset($productItem['product_id']) && empty(trim($productItem['product_id']))) {
                throw new \RuntimeException(\Yii::t('common','{param} cannot be empty',['{param}'=>"product_list.*.product_id"]),404);
            }
        }

        // 客户存在才允许关联
        if (!empty($companyId)) {
            $company = new \common\library\customer_v3\company\orm\Company($client_id, $companyId);
            if (!$company->isExist()) {
                throw new \RuntimeException('company_id 对应的客户不存在，无法关联客户', 404);
            }
        }

        // 有传入才校验是不是这个client的部门
        if (!empty($data['departments']) && isset($data['departments'][0]['department_id'])) {
            $departmentIds = array_filter(array_column($data['departments'], 'department_id'));
            $clientDepartmentIds = array_column(\common\library\department\Helper::getDepartmentList($client_id), 'id');
            if ($noIds = array_diff($departmentIds, $clientDepartmentIds)) {
                throw new \RuntimeException('departments.*.department_id does not exist :' . implode(',', $noIds), 404);
            }
        }

        // 新增或者编辑都如此 业绩归属部门 如果有值，但是传的数组个数为1，且rate没传，那么rate赋值100
        if (isset($data['departments']) && is_array($data['departments']) && count($data['departments']) == 1 && !isset($data['departments'][0]['rate'])) {
            $data['departments'][0]['rate'] = 100;
        }
        if (isset($data['users']) && is_array($data['users']) && count($data['users']) == 1 && !isset($data['users'][0]['rate'])) {
            $data['users'][0]['rate'] = 100;
        }

        // 新建订单初始化处理
        if (empty($orderId)) {
            $isNew = true;

            // 创建人
            if (empty($data['create_user'])) {
                $data['create_user'] = $operatorUserId;
            }

            // 业绩归属人
            if (!isset($data['users'])) {
                $data['users'] = [
                    [
                        'user_id' => $operatorUserId,
                        'rate' => 100
                    ]
                ];
            }

            // 处理人
            if (!isset($data['handler'])) {
                $data['handler'] = [$operatorUserId];
            }

            // 初始化草稿状态
            if (empty($status)) {
                $beginStatus = (new InvoiceStatusService($client_id, Constants::TYPE_ORDER))->beginStatus();
                $data['status'] = $beginStatus['id'];
                $status = $data['status'];
            } else {
                $data['status'] = $status;
            }
        } elseif (empty($order)) {
            // 若编辑时$order为空，后面需要判断编辑时当前订单币种是否与原来一致，所以需要加载下订单
            try {
                $order = new Order($operatorUserId);
                $order->loadById($orderId);
            } catch (\RuntimeException $e) {}
        }

        //编辑状态下，在审批中，不能继续编辑
        if (!$isNew && \common\library\approval_flow\Helper::getReferApprovalInfos($client_id, $order->order_id, \common\library\approval_flow\Constants::APPROVAL_STATUS_APPROVING)) {
            throw new \RuntimeException('审批中的销售订单不可以修改', 404);
        }

        $productIds = array_column($productList, 'product_id') ?? [];
        $productIds = array_filter($productIds, function($item) {
            return !empty($item);
        });
        if (count($productIds)) {
            $productApi  = new \common\library\product_v2\ProductAPI($client_id, $user_id);
            $list = $productApi->items($productIds, \common\library\APIConstant::SCENE_OPEN_API_INVOICE);

            $productMap = [];
            foreach ($list as $item) {
                $productMap[$item['product_id']] = [
                    'name' => $item['name'],
                    'product_no' => $item['product_no'],
                    'image' => $item['image'],
                    'image_info' => $item['image_info'] ?? [],
                    'model' => $item['model'],
                    'product_type' => $item['product_type'],
                    'unit' => $item['unit'],
                    'sku_id' => array_column($item['sku_items'], 'sku_id')[0] ?? 0
                ];
            }

            foreach ($productList as $key => $item) {
                if (empty($item['product_id']) || !isset($productMap[$item['product_id']])) {
                    unset($productList[$key]);
                    continue;
                }

                if ($productMap[$item['product_id']]['product_type'] == \common\library\product_v2\ProductConstant::PRODUCT_TYPE_SKU && (!isset($item['sku_id']) || empty($item['sku_id']))) {
                    throw new \RuntimeException("product_id:{$item['product_id']} name:{$productMap[$item['product_id']]['name']} 是多规格类型的产品，请设置sku_id才允许保存", 404);
                }

                if ($productMap[$item['product_id']]['product_type'] == \common\library\product_v2\ProductConstant::PRODUCT_TYPE_SPU && !empty($productMap[$item['product_id']]['sku_id'])) {
                    $productList[$key]['sku_id'] = $productMap[$item['product_id']]['sku_id'];
                }

                $productList[$key]['product_name'] = $item['product_name'] ?? $productMap[$item['product_id']]['name'] ?? '';
                $productList[$key]['product_image'] = $item['product_image'] ?? $productMap[$item['product_id']]['image'] ?? '';
                $productList[$key]['product_images'] = [];
                // product_images 这个字段在开放平台原来没有定义，这里为了兼容存储了产品图片的
                if (!empty($productMap[$item['product_id']]['image_info']['id'])) {
                    $productList[$key]['product_images'] = [[
                        'file_id'     => (string)$productMap[$item['product_id']]['image_info']['id'],
                        'user_id'     => (string)$operatorUserId,
                        'create_time' => xm_function_now(),
                    ]];
                }
                $productList[$key]['product_no'] = $item['product_no'] ?? $productMap[$item['product_id']]['product_no'] ?? '';
                $productList[$key]['product_model'] = $item['product_model'] ?? $productMap[$item['product_id']]['model'] ?? '';
                $productList[$key]['unit'] = $item['unit'] ?? $productMap[$item['product_id']]['unit'] ?? '';
                $productList[$key]['product_type'] = $productMap[$item['product_id']]['product_type'] ?? $item['product_type'] ?? 0;

                foreach ($item as $id => $value) {
                    if (is_numeric($id)) {
                        $productList[$key]['external_field_data'][$id] = $value;
                    }
                }
            }
        } else {
            $productList = [];
        }

        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_ORDER);
        $service->setFormatterType(CustomFieldFormatter::TYPE_WITH_DEFAULT);
        $list = $service->find();
        $fields = [
            'order' => ['exchange_rate_usd', 'cost_list'],
            'product' => []
        ];

        foreach ($list as $key => $item) {
            if ($item['name'] == '交易产品') {
                $fields['product'] = array_merge($fields['product'], array_column($item['fields'], 'id'));
            } else {
                $fields['order'] = array_merge($fields['order'], array_column($item['fields'], 'id'));
            }

            if ($item['name'] == '基本信息') {
                foreach ($item['fields'] as $k => $value) {
                    if ($isNew && !isset($data[$value['id']]) && !empty($value['default'])) {
                        $data[$value['id']] = $value['default'];
                    }

                    if ($value['id'] == 'currency' && $isNew) {
                        $data['currency'] = $data['currency'] ?? $value['default'];
                    }
                }
            } elseif ($item['name'] == '交易产品' && count($productList)) {
                foreach ($productList as $index => $product) {
                    foreach ($item['fields'] as $k => $value) {
                        if (!isset($product[$value['id']]) && $value['base'] == 1 && $value['is_list'] == 1) {
                            $productList[$index][$value['id']] = !empty($value['default']) ? $value['default'] : ($value['field_type'] == 5 ? 0 : '');
                        } elseif (!isset($product[$value['id']]) && $value['base'] == 0 && $value['is_list'] == 1) {
                            $productList[$index]['external_field_data'][$value['id']] = !empty($value['default']) ? $value['default'] : ($value['field_type'] == 5 ? 0 : '');
                        }
                    }
                }
            }
        }

        // ERP渠道流量：
        // a、创建订单时，如果客户不传汇率，则默认取实时汇率；如果客户传汇率，则以客户传的汇率为准。
        // b、修改订单时，如果客户不传汇率，则默认为创建订单时的汇率；如果客户传汇率，则以客户传的汇率为准
        // 新单 或者编辑订单，但订单币种变化，并且没有传汇率
        if (!empty($platform)
            && ($isNew || (!$isNew && !empty($data['currency']) && $data['currency'] != $order->currency))
            && empty($data['exchange_rate'])
        ) {
            $data['exchange_rate'] = (new ExchangeRateService($client_id))->cnyRateForCurrency($data['currency']);
            $data['exchange_rate_usd'] = (new ExchangeRateService($client_id))->usdRateForCurrency($data['currency']);
        }

        // 初始化币种和汇率
        if (isset($data['currency']) && !isset($data['exchange_rate'])) {
            $data['exchange_rate'] = (new ExchangeRateService($client_id))->cnyRateForCurrency($data['currency']);
            $data['exchange_rate_usd'] = (new ExchangeRateService($client_id))->usdRateForCurrency($data['currency']);
        }

        // 额外费用
        if (!empty($data['cost_list'])) {
            $data['addition_cost_amount'] = 0;
            foreach ($data['cost_list'] as $datum) {
                // 验证额外费用传入为字符串情况
                if (!isset($datum['cost']) || (isset($datum['cost']) && !is_numeric($datum['cost']))) {
                    throw new \RuntimeException('cost_list.*.cost must be a number', 404);
                }
                $data['addition_cost_amount'] += $datum['cost'];
            }
            $data['addition_cost_amount'] = round($data['addition_cost_amount'], 5);
        }

        foreach ($data as $key => $item) {
            if (is_numeric($key)) {
                $data['external_field_data'][$key] = $item;
            }
        }

        try {
            (new \common\library\validation\Validator(
                [
                    'order' => $data,
                    'product' => $productList
                ],
                [
                    'order' => !$isNew ? [] : [
                        'required',
                        'group_field:' . implode(',', [$client_id, Constants::TYPE_ORDER, 0, 1]),
                    ],
                    'product.*' => [
                        'required',
                        'group_field:' . implode(',', [$client_id, Constants::TYPE_ORDER, 0, 2]),
                    ]
                ]
            ))->validate();

        } catch (\RuntimeException $e) {
            throw new \RuntimeException('Input data validate error' . $e->getMessage(), 404);
        }

        if(isset($data['external_field_data'])) {
            foreach($data['external_field_data'] as $key=>$item) {
                if(is_numeric($key)) {
                    $data[$key] = $item;
                }
            }
            unset($data['external_field_data']);
        }

        $order = new Order($operatorUserId);
        $order->setSkipInsertCheckFlag(true);//做下兼容，字段权限时使用老模型会报错
        $order->setSkipManageable(true);
        $order->client_id = $client_id;
        $order->enable_flag = $isNew ? 1 : $order->enable_flag;

        try {
            if (!$isNew) {
                $order->loadById($orderId);
                $productList = $this->dealOldProductList($platform, $productList, $order, $refreshProduct);
            }

            $productList = array_filter(array_values($productList), function($item) {
                return !($item['remove'] ?? false);
            });

            if (isset($data['user_id'])) {
                $data['user_id'] = is_array($data['user_id']) ? $data['user_id'] : [$data['user_id']];
                $order->user_id = $data['user_id'];
                // $order->handler = $data['user_id'];
            }

            if (!empty($data['handler'])) {
                $order->handler = $data['handler'];
            } else {
                $order->handler = $order->user_id;
            }

            if (isset($data['users'])) {
                $order->users = $data['users'];
            }

            if ($isNew) {
                $order->archive_type = Order::ARCHIVE_TYPE_OPEN_API;
                // $platform 其实这个可以知道是哪个平台的，但是目前不用区别那么细
                if (!empty($platform) && $platform == \common\library\erp_service\qcloud\QCloudConstants::XERP_KINGDEE) {
                    $order->archive_type = Order::ARCHIVE_TYPE_KINGDEE;
                    $order->source_type = Order::TYPE_ERP;
                }
                if (!empty($platform) && $platform == \common\library\erp_service\qcloud\QCloudConstants::XERP_CHANJET) {
                    $order->archive_type = Order::ARCHIVE_TYPE_CHANJET;
                    $order->source_type = Order::TYPE_ERP;
                }
                if (!empty($platform) && $platform == \common\library\erp_service\qcloud\QCloudConstants::XERP_GALAXY) {
                    $order->archive_type = Order::ARCHIVE_TYPE_GALAXY;
                    $order->source_type = Order::TYPE_ERP;
                }
            }

            $this->pushData(
                $order,
                [
                    'order' => $data,
                    'product' => $productList
                ],
                $fields,
                $status
            );
        } catch (\Throwable $e) {
            \LogUtil::error('resourceOrderPush', ['uniqueId' => $uniqueId, $e->getTraceAsString()]);
            throw new \RuntimeException('Not Found Resource' . $e->getMessage(), 404);
        }

        // 订单附件处理
        if (!empty($fileList) && !empty($order->order_id)) {
            $uploadFileList = [];
            foreach ($fileList as $fileUrl) {
                try {
                    $temp = $this->uploadImage($fileUrl);
                    $temp && $uploadFileList[] = $temp;
                } catch (\Throwable $t){}
            }
            $oldFileIds = array_column($order->file_list ?? [], 'file_id');
            $newFileIds = array_column($uploadFileList, 'id');
            $order->removeFile($oldFileIds, $order->order_id);
            $order->addFile($newFileIds, $order->order_id);
        }


        // 保存 ERP 日志记录
        if (!empty($order->order_id)) {
            \common\library\erp\Helper::pushErpResourceLog(
                $client_id,
                $order->order_id,
                $order->order_no,
                \Constants::TYPE_ORDER,
                \common\library\erp\ErpConstant::TRANS_TYPE_OF_ERP_TO_CRM
            );
        }
        
        // 获取明细返回
        $returnProductList = [];
        if (!empty($order->product_list)) {
            foreach ($order->product_list as $productItem) {
               $returnProductList[] = [
                   'unique_id'     => $productItem['unique_id'] ?? 0,
                   'product_name'  => $productItem['product_name'] ?? '',
                   'product_no'    => $productItem['product_no'] ?? '',
                   'product_model' => $productItem['product_model'] ?? '',
                   'product_id'    => $productItem['product_id'] ?? 0,
                   'sku_id'        => $productItem['sku_id'] ?? 0,
                   'count'         => $productItem['count'] ?? 0,
                   'unit_price'    => $productItem['unit_price'] ?? 0,
                ]; 
            }
        }

        $this->success([
            'order_id'     => intval($order->order_id),
            'product_list' => $returnProductList,
        ]);
    }

    private function dealOldProductList($platform, $productList, Order $order, $refreshProduct)
    {
        $haveUniqueId = false;
        // 检查下传入是否有unique_id，product_id，sku_id
        // 1、最准确是unique_id，次准确是product_id+sku_id，次次准确是product_id-无法保证产品记录
        $productIdCount = $uniqueIdCount = $skuIdCount = 0;
        foreach ($productList as $item) {
            // 大坑，使用isset会有可能是我们自己的加的默认值
            if (!empty($item['unique_id'])) {
                $haveUniqueId = true;
            }
            if (!empty($item['product_id'])) {
                $productIdCount += 1;
            }
            if (!empty($item['unique_id'])) {
                $uniqueIdCount += 1;
            }
            if (!empty($item['sku_id'])) {
                $skuIdCount += 1;
            }
        }

        // 轻易云对接，轻易云对接第三方没办法保存unique_id和告诉我们要删除产品的remove标识位，只能跟之前的方式分开两个逻辑维护
        // 轻易云的对接方式是创建传AB产品，编辑传A产品，保留A，删除B；若AB产品sku一样，会保留原来的unique_id，目前我们也没保存
        if (!empty($platform) && $uniqueIdCount != $productIdCount
            && $skuIdCount == $productIdCount) {
            // 需要处理，相同产品ID和SKU_ID则保留历史unique_id
            $getOldUniqueId = function ($pId, $sId, $lists) {
                if (empty($pId) || empty($sId)) return [null, null];
                foreach ($lists as $key => $item) {
                    // 保证历史数据里面都有才对比
                    if (!empty($item['product_id']) && !empty($item['sku_id'])
                        && $item['product_id'] == $pId
                        && $item['sku_id'] == $sId) {
                        return [$key, $item['unique_id'] ?? ''];
                    }
                }
                return [null, null];
            };
            $oldProductList = $order->product_list ?? [];
            foreach ($productList as $key => $item) {
                list($index, $oldUniqueId) = $getOldUniqueId($item['product_id'], $item['sku_id'], $oldProductList);
                // 只处理这种情况，其他情况不处理，当新增或者删除
                if (is_numeric($index) && !empty($oldUniqueId)) {
                    unset($oldProductList[$index]);
                    $productList[$key]['unique_id'] = $oldUniqueId;
                }
            }
        } elseif ($order->isNew()) {
            // 增加新插入订单不再限制相同行传入CRM，这里一直存在的缺陷
            return $productList;
        } elseif (empty($refreshProduct) && !empty($productList)) {
            // 非轻易云对接，对方有开发能力可以传入unique_id，remove等标志
            // 更新操作，需合并订单里的产品列表数据
            // 这里考虑两种情况，没有unique_id的话用product_id聚合
            // 有unique_id的情况，用unique_id做映射，需要考虑一种特殊场景是有多个新增的产品，它们的unique_id都是空，对于这一部分，直接append到最后的product_list即可
            if (!$haveUniqueId) {
                $productList = ArrayUtil::index($productList, 'product_id');
                $oldProductList = ArrayUtil::index($order->product_list, 'product_id');
                foreach ($productList as $key => $item) {
                    $oldProductList[$key] = $item;
                }
                $productList = array_values($oldProductList);
            } else {
                // 有unique_id的情况，用unique_id做映射，需要考虑一种特殊场景是有多个新增的产品，它们的unique_id都是空，对于这一部分，直接append到最后的product_list即可
                $oldProductList = ArrayUtil::index($order->product_list, 'unique_id');
//                $productList = ArrayUtil::index($productList, 'unique_id');
                $newProductList = [];
                foreach ($productList as $product) {
                    $key = $product['unique_id'] ?? '';
                    // 空key不参与合并，而是最后追加
                    if (!empty($key)) {
                        $oldProductList[$key] = $product;
                    } else {
                        $newProductList[] = $product;
                    }
                }
                $productList = array_values($oldProductList);
                foreach ($newProductList as $newProduct) {
                    $productList[] = $newProduct;
                }
            }

        }
        return $productList;
    }

    public function actionInfo($client_id, $user_id, $order_id = 0, $order_no = '')
    {
        User::setLoginUserById($user_id);
        $user = User::getLoginUser();

        $this->validate([
            'order_id' => 'integer',
            'order_no' => 'string',
        ], compact('order_id', 'order_no'));

        $order = new Order($user->getUserId());
        
        try {
            !empty($order_id) ? $order->loadById($order_id) : $order->loadByNo($order_no);
            $order->getFormatter()->setIsOpen(true);
            $order->getFormatter()->showQuoteFieldFlag(true);
            $order->getFormatter()->setShowApiQuoteField(true);
            $order->getFormatter()->setShowFileList(true);
            $order->getFormatter()->setShowCompanyInfo(true);
            $order->getFormatter()->setShowLastApprovalInfo(true);
            $order->getFormatter()->setShowHandlerInfo(true);
            $order->getFormatter()->setShowLink([
                Constant::ORDER_LINK_STOCK_UP,
                Constant::ORDER_LINK_OUTBOUND,
                Constant::ORDER_LINK_END
            ]);
            $order->getFormatter()->setShowCapitalAccountInfo(true);
            $order->getFormatter()->setShowProductQuoteField(true);
            $order->getFormatter()->setShowAlibabOrderInfo(true);
            $data = $order->getAttributes();

            // 兼容tbl_extend字段返回
            $orderInfoNew = [];
            if (!empty($order->order_id)) {
                $orderNew = new common\library\oms\order\Order($client_id, $order->order_id);
                $orderNew->getFormatter()->displayQuotationInfo(true);
                $orderNew->getFormatter()->openInfoSetting();
                $orderInfoNew = $orderNew->getAttributes();
                $data['quotation_info'] = $orderInfoNew['quotation_info'] ?? [];
            }


            if (isset($data['last_approval_info']) && !empty($data['last_approval_info'])) {
                $data['approval_status'] = $data['last_approval_info']['status'];
            }

            $service = new FieldList($client_id);
            $service->setFields([
                'id', 'name', 'base', 'field_type', 'ext_info', 'require', 'default', 'disable_flag', 'is_list', 'relation_field_type'
            ]);
            $service->setType(Constants::TYPE_ORDER);
            //$service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
            $list = $service->find();
            $fieldList = ArrayUtil::index($list, 'id');
            $externalFields = array_filter($list, function($item) {
                return is_numeric($item['id']) && $item['is_list'] == 0;
            });
            // 订单自定义字段
            $externalFields = array_column($externalFields, 'id');
            $externalProductFields = array_filter($list, function($item) {
                return is_numeric($item['id']) && $item['is_list'] == 1;
            });
            // 订单明细自定义字段
            $externalProductFields = array_column($externalProductFields, 'id');
            // 获取订单tbl_extend的字段，兼容逻辑
            $newProductExternalFieldDataMap = [];
            $existFieldIds = array_column($data['external_field_data'] ?? [], 'id');
            foreach ($orderInfoNew as $infoKey => $infoValue) {
                if (is_numeric($infoKey) && in_array($infoKey, $externalFields) && !isset($data[$infoKey]) && !in_array($infoKey, $existFieldIds)) {
                    $data['external_field_data'][$infoKey] = $infoValue;
                }
                if ($infoKey == 'product_list' && is_array($infoValue)) {
                    foreach ($infoValue as $productItem) {
                        $productId = $productItem['id'] ?? 0;
                        if (empty($productId)) continue;
                        foreach ($productItem as $productKey => $productValue) {
                            if (is_numeric($productKey) && in_array($productKey, $externalProductFields)) {
                                $newProductExternalFieldDataMap[$productId][$productKey] = $productValue;
                            }
                        }
                    }
                }
            }
            // 兼容订单关联引用字段图片附件字段，但是这里肯定要重构下才行
            foreach (($data['external_field_data'] ?? []) as $fieldIndex => $fieldItem) {
                $key  = $fieldItem['id'] ?? '';
                $item = $fieldItem['value'] ?? '';
                if (!in_array($fieldIndex, $externalFields)) {
                    unset($data['external_field_data'][$fieldIndex]);
                }
                if (!in_array($key, $externalFields)) {
                    continue;
                }
                $data['external_field_data'][$key] = !empty($item) ? $item : ($fieldList[$key]['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER ? 0 : '');
                if (array_intersect([$fieldList[$key]['field_type'], $fieldList[$key]['relation_field_type']], [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE]) && isset($item[0]['file_id'])) {
                    if (count($item) == 1) {
                        $file = new \AliyunUpload();
                        $file->loadByFileId($item[0]['file_id']);
                        $data['external_field_data'][$key] = $file->getFileUrl();
                    } else {
                        $file = new \AliyunUpload();
                        $files = [];
                        foreach ($item as $value) {
                            $file->loadByFileId($value['file_id']);
                            $files[] = $file->getFileUrl();
                        }
                        $data['external_field_data'][$key] = $files;
                    }
                }
            }

            foreach ($externalFields as $key) {
                if (!isset($data[$key]) && !isset($data['external_field_data'][$key])) {
                    $data['external_field_data'][$key] = $fieldList[$key]['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER ? 0 : '';
                }
            }
            

            // 兼容最新订单产品逻辑
            foreach (($data['product_list'] ?? []) as $productIndex => $productItem) {
                // @todo 这里还是用老的引用字段external_field_data-这里才是最新的，用来合并覆盖，后面改用基建的话才改掉
                if (isset($data['product_list'][$productIndex]['external_field_data'])) {
                    if (is_array($data['product_list'][$productIndex]['external_field_data'])) {
                       $productItem += $data['product_list'][$productIndex]['external_field_data'];
                    }
                    unset($data['product_list'][$productIndex]['external_field_data']);
                }
                $productId = $productItem['id'] ?? 0;
                $newProductExternalFieldData = $newProductExternalFieldDataMap[$productId] ?? [];
                $productItem = $productItem + $newProductExternalFieldData;
                foreach ($productItem as $fieldKey => $fieldValue) {
                    if (is_numeric($fieldKey) && in_array($fieldKey, $externalProductFields)) {
                        $data['product_list'][$productIndex][$fieldKey] = !empty($fieldValue) ? $fieldValue : ($fieldList[$fieldKey]['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER ? 0 : '');
                        if (array_intersect([$fieldList[$fieldKey]['field_type'], $fieldList[$fieldKey]['relation_field_type']], [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE])) {
                            if (!empty($fieldValue[0]['file_id'])) {
                                if (count($fieldValue) == 1) {
                                    $file = new \AliyunUpload();
                                    $file->loadByFileId($fieldValue[0]['file_id']);
                                    $data['product_list'][$productIndex][$fieldKey] = $file->getFileUrl();
                                } elseif (count($fieldValue) > 1) {
                                    $file = new \AliyunUpload();
                                    $files = [];
                                    foreach ($fieldValue as $value) {
                                        $file->loadByFileId($value['file_id']);
                                        $files[] = $file->getFileUrl();
                                    }
                                    $data['product_list'][$productIndex][$fieldKey] = $files;
                                }
                            } elseif (!empty($fieldValue['file_id'])) {
                                $file = new \AliyunUpload();
                                $file->loadByFileId($fieldValue['file_id']);
                                $data['product_list'][$productIndex][$fieldKey] = $file->getFileUrl();
                            }
                        }
                    }
                }
            }

            $list = new CashCollectionList($client_id);
            $list->setSkipPermission(true);
            $list->setFields(['collection_date']);
            $list->setViewingUserId($user_id);
            $list->setOrderId($data['order_id']);
            $list->setOrderBy('collection_date');
            $list->setOrder('desc');
            $list->setOffset(0);
            $list->setLimit(5);
//            $list->getFormatter()->listInfoSetting();
            $cashList = $list->find();
            $stats = $list->stats();
            $data['collection_date'] = $cashList[0]['collection_date'] ?? '';
            $data['status_name'] = $stats['status_name'];
            $data['amount_usd'] = $stats['amount_usd'];
            $data['collect_amount_rmb'] = $stats['collect_amount_rmb'];
            $data['collect_amount_usd'] = $stats['collect_amount_usd'];
            $data['not_collect_amount_rmb'] = $stats['not_collect_amount_rmb'];
            $data['not_collect_amount_usd'] = $stats['not_collect_amount_usd'];
            $data['percentage_rmb'] = $stats['percentage_rmb'];
            $data['percentage_usd'] = $stats['percentage_usd'];
            // 保存 ERP 日志记录
            if (!empty($order->order_id)) {
                \common\library\erp\Helper::pushErpResourceLog(
                    $client_id,
                    $order->order_id,
                    $order->order_no,
                    \Constants::TYPE_ORDER,
                    \common\library\erp\ErpConstant::TRANS_TYPE_OF_CRM_TO_ERP
                );
            }
            $data = $this->formatInfo($client_id, $data);
            $this->success($data);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException('Not Found Resource', 404);
        }
    }

    public function actionRemove($client_id, $user_id, $order_id = 0, $order_no = '')
    {
        $this->validate([
            'order_id'    => 'integer', // 订单id
        ]);

        User::setLoginUserById($user_id);
        $user = User::getLoginUser();
        $order = new Order($user->getUserId());

        try {
            !empty($order_id) ? $order->loadById($order_id) : $order->loadByNo($order_no);

            //存在关联的不删除，不算报错
            $hasReferDownStreamInvoiceArr = Helper::hasReferDownStreamInvoice($user->getClientId(), [$order->order_id]);
            if (!empty($hasReferDownStreamInvoiceArr)) {
                throw new \RuntimeException(\Yii::t('invoice', 'Failed to delete, the downstream invoice/task has been associated'));
            }

            $result = $order->delete();
        } catch (\RuntimeException $e) {
            if (!empty($hasReferDownStreamInvoiceArr)) {
                throw new \RuntimeException(\Yii::t('invoice', 'Failed to delete, the downstream invoice/task has been associated'), 404);
            }
            throw new \RuntimeException('Not Found Resource', 404);
        }

        $this->success('success');
    }

    public function actionSelector($client_id, $user_id, $field = '') {
        User::setLoginUserById($user_id);
        $fields = [
            0 => 'status',
            1 => 'currency',
            2 => 'main_currency',
        ];
        switch ($field) {
            case $fields[0] :
                $data = $this->getOrderStatus($client_id, $user_id);
                break;
            case $fields[1] :
                $data = $this->getOrderCurrency($client_id, $user_id);
                break;
            case $fields[2] :
                $data['main_currency'] = strtoupper(Client::getClient($client_id)->getMainCurrency());
                break;
            default :
                $data = [];
                break;
        }
        $this->success($data);
    }

    private function getOrderStatus($client_id, $user_id) {
        $invoice_status_service = new InvoiceStatusService($client_id, Constants::TYPE_ORDER);
        $data = $invoice_status_service->list();
        return $data;
    }

    private function getOrderCurrency($client_id, $user_id) {
        $exchange_rate_service = new ExchangeRateService($client_id);
        $data = $exchange_rate_service->onlineRates();
        return $data;
    }

    /**
     * 更新字段过滤处理
     * @param $client_id
     * @param $data
     * @return array
     */
    private function filterPushFields($client_id, $data)
    {
        $fields = [
            'name',
            'order_no',
            'remark',
            'price_contract',
            'currency',
            'exchange_rate',
            'amount',
            'amount_rmb',
            'amount_usd',
            'account_date',
            'customer_id',
            'company_id',
            'addition_cost_amount',
            'order_contract',
            'bank_info',
            'receive_remittance_remark',
            'insurance_remark',
            'customer_phone',
            'customer_email',
            'customer_address',
            'customer_name',
            'company_name',
            'company_phone',
            'company_fax',
            'company_address',
            'transport_mode',
            'shipment_port',
            'shipment_deadline_remark',
            'shipment_deadline',
            'marked',
            'more_or_less',
            'target_port',
            'price_contract_remark',
            'package_gross_weight_amount',
            'package_volume_amount',
            'package_remark',
            'receive_remittance_way',
            'exchange_rate_usd',
            'cost_list',
            'user_id',
            'users',
            'departments',
            'country',
            'order_gross_margin',
            'product_total_amount',
            'create_time',
            'create_user',
            'handler',
            'source_type',
            'opportunity_id',
            'capital_account_id',
            'tax_refund_type',
        ];

        $result = $data;
        $data = [];

        $service = new FieldList($client_id);
        $service->setType(Constants::TYPE_ORDER);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $list = $service->find();
        $fieldList = ArrayUtil::index($list, 'id');

        foreach ($result as $key => $item) {
            //过滤null请求参数
            if (is_null($item)) {
                continue;
            }
            if (in_array($key, $fields)) {
                $data[$key] = $item;
                // 外面校验类型是date，那就有可能传入2022-11-01或2022-11-01 09:22:22，但是数据库是Y-m-d H:i:s存储的
                // 若编辑是传入是2022-11-01，数据库是2022-11-01 00:00:00会造成一个订单日期修改的操作记录，下面优化下
                if ($key == 'account_date') {
                    $data[$key] = strlen($item) == 10 ? $item . ' 00:00:00' : $item;
                }
            } elseif(is_numeric($key) && isset($fieldList[$key]) && !in_array($fieldList[$key]['field_type'], [CustomFieldService::FIELD_TYPE_ATTACH, CustomFieldService::FIELD_TYPE_IMAGE])) {
                if (!\Util::isDate($item) && in_array($fieldList[$key]['field_type'], [CustomFieldService::FIELD_TYPE_DATE, CustomFieldService::FIELD_TYPE_DATETIME])) {
                    throw new \RuntimeException($key . ' 格式不正确', 404);
                }

                $data['external_field_data'][$key] = $item;
            }
        }

        return $data;
    }

    private function formatInfo($clientId, $data)
    {
        $fields = [
            'order_id',
            'order_no',
            'name',
            'user_id',
            'users',
            'create_user',
            'update_user',
            'handler',
            'handler_info',
            'company_id',
            'customer_id',
            'opportunity_id',
            'remark',
            'account_date',
            'price_contract',
            'currency',
            'exchange_rate',
            'amount',
            'amount_rmb',
            'amount_usd',
            'product_list',
            'product_total_amount',
            'product_total_count',
            'addition_cost_amount',
            'order_contract',
            'bank_info',
            'receive_remittance_remark',
            'insurance_remark',
            'customer_phone',
            'customer_email',
            'customer_address',
            'customer_name',
            'company_name',
            'company_phone',
            'company_fax',
            'company_address',
            'transport_mode',
            'shipment_port',
            'shipment_deadline_remark',
            'shipment_deadline',
            'marked',
            'more_or_less',
            'target_port',
            'price_contract_remark',
            'package_gross_weight_amount',
            'package_volume_amount',
            'package_remark',
            'receive_remittance_way',
            'exchange_rate_usd',
            'departments',
            'create_time',
            'update_time',
            'collection_date',
            'status_name',
            'amount_usd',
            'collect_amount_rmb',
            'collect_amount_usd',
            'not_collect_amount_rmb',
            'not_collect_amount_usd',
            'percentage_rmb',
            'percentage_usd',
            'product_total_count',
            'status',
            'approval_status',
            'gross_profit_margin',
            'file_list',
            'cost_list',
            'country',
            'remark',
            'link_status',
            'source_type',
            'archive_type',
            'cost_with_tax_total',
            'capital_account_id',
            'capital_account_name',//资金账户
            'capital_name',//开户名
            'capital_bank',//开户行
            'bank_account',//银行账号
            'capital_account_address',//开户行地址
            'capital_account_remark',//更多信息
            'company',// 返回公司信息
            'tax_refund_type',
            'alibaba_order_info',
            'quotation_id',
            'quotation_info',
        ];

        $result = $data;
        $data = [];
        $externalFieldData = $result['external_field_data'];

        $service = new FieldList($clientId);
        $service->setType(Constants::TYPE_ORDER);
        $service->setFormatterType(CustomFieldFormatter::TYPE_COLUMN_MAP2);
        $service->setIsList(1);
        $list = $service->find();
        $productFields = array_column($list, 'id');
        $fieldList = ArrayUtil::index($list, 'id');
        $productFields[] = 'sku_code';//订单产品里面没有这个字段，但是需要返回
        $specialFields   = ['sku_code', 'product_no'];//编码中有特殊字符，这里跳过一下

        $costItemInvoiceRelationList = CostItemInvoiceRelationApi::getCostItemRelationByInvoice(\Constants::TYPE_ORDER, 1);
        $costItemRelationMap = array_column($costItemInvoiceRelationList, null, 'relation_id');

        foreach ($result as $key => $item) {
//            if (is_string($item)) {
//                $item = preg_replace('/[\"\']/', '', $item);
//            }

            if ($key == 'users') {
                $item = is_string($item) ? json_decode($item, true) : $item;
                foreach ($item as $k => $value) {
                    $item[$k]['nickname'] = \User::getUserObject($value['user_id'])->getNickname() ?? '';
                }
            }

            if ($key == 'departments') {
                $departmentIds = array_column($item, 'department_id');
                $departmentService = new \common\library\department\DepartmentService($clientId);
                $departmentMap = $departmentService->batchGetDepartmentListForIds($departmentIds);
                foreach ($item as $k => $value) {
                    $item[$k]['name'] = $departmentMap[$value['department_id']]['name'] ?? '';
                }
            }

            if ($key == 'cost_list') {
                foreach ($item as $k => $value) {
                    if (empty($value['cost_item_relation_id'])) {
                        $item[$k]['cost_description'] = '';
                    } else {
                        $item[$k]['cost_name'] = $costItemRelationMap[$value['cost_item_relation_id']]['item_name'] ?? '';
                        $item[$k]['cost_description'] = $costItemRelationMap[$value['cost_item_relation_id']]['description'] ?? '';
                    }
                }
            }

            if (in_array($key, ['customer_name', 'company_phone', 'customer_phone', 'customer_email']) && !empty($result['customer_id'])) {
                $customer = \common\library\customer\Helper::getCustomer($clientId, $result['customer_id']);
                if ($customer->isExist()) {
                    switch ($key) {
                        case 'customer_name':
                            $item = !empty($item) ? $item : (!empty($customer->name) ? $customer->name : $customer->email);
                            break;

                        // 以订单里面的为准
                        case 'company_phone':
                        case 'customer_phone':
                            $item = !empty($item) ? $item : (!empty($customer->full_tel_list) ? implode(', ', $customer->full_tel_list) : '');
                            break;

                        case 'customer_email':
                            $item = !empty($customer->email) ? $customer->email : '';
                            break;
                    }
                }
            }

            if (is_numeric($key) || in_array($key, $fields)) {
                $data[$key] = $item;
            }
        }

        foreach ($externalFieldData as $key => $item) {
            $data[$key] = $item;
        }

        $productList = [];
         foreach ($result['product_list'] as $key => $item) {
            foreach ($item as $k => $value) {
                $k = preg_replace("/[\"\[\]]/", '', $k);
                if (is_string($value) && empty($value) && isset($fieldList[$k])) {
                    $value = !empty($fieldList[$k]['default']) ? $fieldList[$k]['default'] : ($fieldList[$k]['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER ? 0 : '');
                }

                if (in_array($k, $productFields)) {
                    if (!in_array($k, $specialFields)) {
                        $productList[$key][$k] = is_string($value) ? preg_replace('/[\"\']/', '', $value) : $value;
                    } else {
                        $productList[$key][$k] = $value;
                    }
                }
            }

            foreach ($productFields as $k => $value) {
                if (!isset($productList[$key][$value])) {
                    $fieldValue = !empty($fieldList[$value]['default']) ? $fieldList[$value]['default'] : ($fieldList[$value]['field_type'] == CustomFieldService::FIELD_TYPE_NUMBER ? 0 : '');
                    $value = preg_replace("/[\"\[\]]/", '', $value);
                    $productList[$key][$value] = $fieldValue;
                }
            }

            if (isset($item['external_field_data'])) {
                foreach ($item['external_field_data'] as $k => $value) {
                    if (in_array($k, $productFields)) {
                        $productList[$key][$k] = $value;
                    }
                }
            }

            // 兼容平台产品字段改造后，还是返回平台产品信息
            if (in_array($data['source_type'] ?? 0, [Order::TYPE_ALI_ORDER, Order::TYPE_DIRECT_PAY_ORDER])
                && !empty($item['platform_product_info'])) {
                $productSnapshotFields = ['product_name', 'product_model', 'sku_attributes', 'product_image'];
                foreach ($productSnapshotFields as $field) {
                    if (isset($item[$field])) {
                        if ($field == 'product_image') {
                            $fieldValue = current($item['platform_product_info']['product_images'] ?? [])['file_url'] ?? null;
                        } else {
                            $fieldValue = $item['platform_product_info'][$field] ?? null;
                        }
                        if (!is_null($fieldValue)) {
                            $productList[$key][$field] = $fieldValue;
                        }
                    }
                }
            }
        }
        $data['product_list'] = $productList;

        return $data;
    }

    private function pushData(Order $model, array $data, array $fields, $status)
    {
        $flag = false;
        $externalData = $model->external_field_data;
        foreach ($data['order'] as $field => $value) {
            if (in_array($field, $fields['order'])) {
                if (is_numeric($field)) {
                    $externalData[$field] = $value;
                } else {
                    $model->$field = $value;
                }
                $flag = true;
            }
        }

        $model->external_field_data = $externalData;
        foreach ($data['product'] as $key => $item) {
            foreach ($item as $field => $value) {
                if (is_numeric($field) && in_array($field, $fields['product'])) {
                    $data['product'][$key]['external_field_data'][$field] = $value;
                    unset($data['product'][$key][$field]);
                } elseif (!in_array($field, $fields['product']) && !in_array($field, ['product_images', 'external_field_data'])) {
                    unset($data['product'][$key][$field]);
                }
            }
        }

        if (count($data['product'])) {
            $model->product_list = $data['product'];
            $product_total_amount = $product_total_count = $package_volume_amount = $package_gross_weight_amount = 0;
            foreach ($data['product'] as $key => $item) {
                $costAmount = $item['cost_amount'] ?? 0;
                $count = $item['count'] ?? 0;
                $product_total_amount += is_numeric($costAmount) ? $costAmount : 0;
                $product_total_count += is_numeric($count) ? $count : 0;
                $package_volume_amount += !empty($item['package_gross_weight_subtotal']) ? $item['package_gross_weight_subtotal'] : 0;
                $package_gross_weight_amount += !empty($item['package_gross_weight_subtotal']) ? $item['package_gross_weight_subtotal'] : 0;
            }
            // amount、amount_rmb、product_total_amount、product_total_count、addition_cost_amount底层都要求是5位小数
            $model->product_total_amount = round($product_total_amount, 5);
            $model->product_total_count = round($product_total_count, 5);
            $model->package_volume_amount = $package_volume_amount;
            $model->package_gross_weight_amount = $package_gross_weight_amount;
            $flag = true;
        }


        // 订单推送时：1. 创建/编辑订单，2. 变更订单状态
        // 原逻辑：
        //  i.   顺序执行 1、2，save 两次，触发两次 afterSave()，短时间内并发会造成数据异常（eg.绩效统计计算脏读）
        //  ii.  创建/编辑订单【不会】触发审批流，变更订单状态【会】触发审批流
        //  iii. 创建/编辑订单 + 变更订单状态时，如果触发订单状态审批流中断，订单基本信息会先保存成功
        //  iv.  无论如何都会正常触发工作流

        // 优化后逻辑：根据数据变动条件，调用相应的 save() 方法。如果同时有订单状态变更，则依靠 updateStatus 方法内的 save() 进行兜底。
        // 注：为了不改变原逻辑的审批流触发流程，所以需要判断当前请求【是否命中状态变更审批流】：
        //    i.  不命中 => 通过 changeStatus() 正常保存
        //    ii. 命中   => save()保存订单基本信息，在通过 changeStatus() 触发审批流中断

        $skipRefreshTotal = isset($data['order']['amount']); //如果指定了订单金额，更新状态时跳过金额刷新, todo 此处是原有逻辑，后期需要考虑是否加校验
        $status = is_numeric($status) ? $status : $model->status;
        $model->order_id = $model->isNew() ? \ProjectActiveRecord::produceAutoIncrementId() : $model->order_id;  //新建场景下需要先生成一个 order_id 提供给状态变更的审批流进行中断
        if ($flag) {
            //有订单信息变更
            if (
                empty($status) ||
                ($model->isNew() && ($status == $model->getStatusService()->beginStatus()['id'])) ||
                ((!$model->isNew()) && ($status == $model->status))
            ) {
                //无状态变更
                $model->setSkipRefreshTotal($skipRefreshTotal);
                $model->save();
            } else {
                //有状态变更
                $this->updateStatus($model, $status, $skipRefreshTotal, true);
            }
        } else {
            //无订单信息变更
            if (!empty($status) && $status != $model->status) {
                $this->updateStatus($model, $status, $skipRefreshTotal);
            }
        }
    }

    // 更改订单状态，开启审批流匹配模式
    private function updateStatus(Order $order, $status, $skipRefreshTotal = false, $needSaveOrder = false)
    {
            $cloneOrder = null;
            if ($needSaveOrder) {
                if ($this->isMatchStatusApproval($order, $status)) {
                    \LogUtil::info("order_id: {$order->order_id} 命中审批流中断");
                    $order->save(); //先保存订单，再触发状态变更审批流中断
                    $order = new Order($order->getPrivilegeFieldUserId(), $order->order_id); //新建订单持久化后重新加载订单，用于后面审批流中断
                } else {
                    // 未触发审批流中断的新建单据，按草稿态向后流转
                    if ($order->isNew()) {
                        $cloneOrder = \DeepCopy\deep_copy($order);
                        \LogUtil::info("order_id: {$order->order_id} 未触发审批流中断的新建单据");
                        $order->link_status = \common\library\oms\order_link\OrderLink::make()->toArray();
                        $order->setOldAttributes(['status' => $order->getStatusService()->beginStatus()['id']]);
                        $order->setSkipInitOrderStatus(true);
                    }
                }
            }

            if ($order->isNew()) {
                $eventType = Order::EVENT_AFTER_INSERT;
            } else {
                $eventType = $needSaveOrder ? Order::EVENT_AFTER_UPDATE : Order::EVENT_AFTER_STATUS;
            }

        try {
            Order::$enableForApprovalFlow = true;
            Order::$matchOnly = false; // 执行审批流中断
            $order->setSkipRefreshTotal($skipRefreshTotal);
            $order->changeStatus($status, null, [], $eventType);
        } catch (\RuntimeException $e) {
            \LogUtil::info("order_id: {$order->order_id} 变更状态失败 errorLog: " .$e->getMessage().$e->getTraceAsString());
            if (!empty($cloneOrder)) {
                $cloneOrder->save(); //新订单变更状态失败，也要保存其他数据
            }
            return true;
        }

        return true;
    }

    /**
     * 是否触发审批流中断
     * @param Order $order
     * @param $status
     * @return bool|void
     * @throws Throwable
     */
    public function isMatchStatusApproval(Order $order, $status)
    {
        try {
            Order::$enableForApprovalFlow = true;
            Order::$matchOnly = true; // 不执行审批流中断，只看是否有命中
            $signal = new \common\library\approval_flow\signal\interrupt\OrderChangeStatusInterrupt(
                $order->getPrivilegeFieldUserId(),
                $order->order_id,
                \common\library\approval_flow\Constants::ENTITY_TYPE_ORDER,
                $order->name,
                $order->getUpdateFields(),
                [
                    'order_id'  => $order->order_id,
                    'status_id' => $status,
                    'handler'   => $order->handler,
                ],
                $order
            );
            $interrupt = $order->setIncludeApprovingConfig(true)->runApprovalFlow($signal);

            return false;
        } catch (\Exception $e) {
            if ($e->getCode() == 9527) {
                return true;
            }
        }
    }

    public function actionOrderEnums(int $client_id = 0, int $user_id = 0)
    {
        // 货币
        $currencyList = \common\library\exchange_rate\Helper::getCurrencyList($client_id);
        // 付款方式
        $payList = array_map(function ($item) {
            return ['code' => $item, 'name' => $item];
        }, array_values(\common\library\cash_collection\CashCollection::TYPE_MAP));
        // 国家/地区
        $countryAreaList = array_map(function ($item) {
            return [
                'code' => $item['alpha2'],
                'name' => $item['country_name'],
            ];
        },
        $list = CountryService::getAllCountrys()
        );
        // 订单类型
        $sourceTypeList = array_map(function ($item) {
            return [
                'code' => $item['source_type'],
                'name' => $item['name'],
            ];
        }, array_values(\common\library\invoice\Helper::getOrderSourceTypeMaps()));
        // 订单状态
        $orderStatusList = array_map(function ($item) {
            return [
                'code' => $item['id'],
                'name' => $item['name'],
            ];
        }, (new InvoiceStatusService($client_id, Constants::TYPE_ORDER))->list());
        // 订单创建方式
        $archiveTypeList = array_map(function ($item) {
            return [
                'code' => $item['archive_type'],
                'name' => $item['name'],
            ];
        }, array_values(\common\library\invoice\Helper::getOrderArchiveTypeMaps()));
        // 价格条款
        $priceContracts = [];
        foreach (\common\library\object\field\formatter\field_formatter\PriceContractFormatter::PRICE_CONTRACT_MAP as $code => $name) {
            $priceContracts[] = [
                'code' => $code,
                'name' => $name,
            ];
        }
        return  $this->success([
            'currency_list' => $currencyList,
            'pay_list' => $payList,
            'country_area_list' => $countryAreaList,
            'source_type_list' => $sourceTypeList,
            'order_status_list' => $orderStatusList,
            'archive_type_list' => $archiveTypeList,
            'price_contract_list' => $priceContracts,
        ]);
    }
}
