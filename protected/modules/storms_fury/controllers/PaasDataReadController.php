<?php

use common\library\custom_field\CustomFieldService;
use common\library\object\field\FieldConstant;
use common\library\privilege_v3\Helper as PrivilegeHelper;
use protobuf\FlutterPaas\PaasObjectExtendOperate;
use protobuf\FlutterPaas\PaasObjectName;
use protobuf\FlutterPaas\PaasObjectOperate;
use protobuf\FlutterPaas\PaasObjectOperatePrivilege;
use protobuf\FlutterPaas\PbPaasFieldInfoRequest;
use protobuf\FlutterPaas\PbPaasFieldInfoResponse;
use protobuf\FlutterPaas\PbPaasFieldInfo;
use protobuf\FlutterPaas\PaasFieldExtInfoOption;
use protobuf\FlutterPaas\PaasFieldExtInfo;
use protobuf\FlutterPaas\PbTriggerField;
use protobuf\FlutterPaas\PbTriggerFieldListReq;
use protobuf\FlutterPaas\PbTriggerFieldListRsp;
use protobuf\FlutterPaas\PbFunctionFieldValueReq;
use protobuf\FlutterPaas\PbFunctionFieldValueRsp;
use protobuf\FlutterPaas\PaasFieldType;
use common\library\flutter\data\PaasData;
use common\library\object\field\service\FunctionFieldService;

class PaasDataReadController extends StormsFuryController
{

    public function actionTriggerField()
    {
        $req = new PbTriggerFieldListReq();
        $this->buildRequest($req);
        $objectName = $req->getObjectName();
        $user = User::getLoginUser();
        $triggerField = FunctionFieldService::triggerField($user->getClientId(), PaasData::OBJECT_DATA_API_MAP[$objectName]['object_name']);

        $objectFields = [];
        foreach ($triggerField as $objectName => $fields) {
            $field = new PbTriggerField();
            $field->setObjectName(\common\library\object\object_define\Constant::OLD_TYPE_MAP[$objectName]);
            $field->setFields($fields);
            $objectFields[] = $field;
        }

        $rsp = new PbTriggerFieldListRsp();
        $rsp->setFieldList($objectFields);

        $this->successPb($rsp);
    }

    public function actionFunctionFieldValue()
    {
        $req = new PbFunctionFieldValueReq();
        $this->buildRequest($req);
        $user = User::getLoginUser();
        $data = iterator_to_array($req->getData()->getIterator());
        $objectName = PaasData::OBJECT_DATA_API_MAP[$req->getObjectName()]['object_name'] ?? '';
        $modified_fields = [];

        $pidKey = \common\library\object\object_define\Constant::OBJ_PRIMARY_KEY_MAP[$objectName];
        $mainPid = $data[$pidKey] ?? "";
        foreach ($req->getMainModifiedFields()->getIterator() as $field) {
            $modified_fields[$objectName][$mainPid][] = $field;
        }

       \LogUtil::info('req='. json_encode(['data' => $data, 'modified_fields' => $modified_fields]));

        [$functionFieldVal, $subFunctionFieldVal] = FunctionFieldService::functionFieldValue($user->getClientId(), $objectName, $data, [], $modified_fields, true);

        $data = [];
        foreach ($functionFieldVal as $field => $value) {
            $data[$field] = $value ?? '';
        }

        $rsp = new PbFunctionFieldValueRsp();
        \LogUtil::info('rsp_data='.json_encode($data));
        $rsp->setData($data);

        $this->successPb($rsp);
    }

    public function actionFieldList()
    {
        $req = new PbPaasFieldInfoRequest();
        $this->buildRequest($req);
        $objectName = $req->getObjectName();
        $user = User::getLoginUser();

        if ($objectName == PaasObjectName::OBJ_INQUIRY_COLLABORATION_ATTACHMENTS) {
            $rsp = new PbPaasFieldInfoResponse();
            $rsp->setObjectName($objectName);
            $this->successPb($rsp);
            return;
        }

        $api = new \common\library\object\field\Api();

        // 业务场景
        $list = $api->fieldList($user->getClientId(), PaasData::OBJECT_DATA_API_MAP[$objectName]['object_name'], 'header', [], [],true);
        $quoteList = \common\library\custom_field\CustomFieldService::getFieldListByType($user->getClientId(),0, PaasData::OBJECT_DATA_API_MAP[$objectName]['module_type'], \common\library\custom_field\CustomFieldService::FIELD_TYPE_QUOTE_FIELDS);
        $quoteFields = array_column($quoteList, 'id');
        $quoteList = \ArrayUtil::index($quoteList, 'id');
        $fieldList = [];

        foreach ($list[PaasData::OBJECT_DATA_API_MAP[$objectName]['object_name']] as $item) {
            $fieldInfo = new PbPaasFieldInfo();
            $fieldInfo->setGroupId(1);
            $fieldInfo->setGroupName('基本信息');
            $fieldInfo->setFieldId($item['field_id']);
            $fieldInfo->setClientId($item['client_id']);
            $fieldInfo->setObjectName($objectName);
            $fieldInfo->setFieldKey($item['field']);
            $fieldInfo->setFieldName($item['field_name']);
            $fieldInfo->setFieldType($item['field_type']);
            $fieldInfo->setIsShow(boolval($item['show_flag'] ?? false));
            $fieldInfo->setIsWritable(boolval($item['is_writable'] ?? false));
            $fieldInfo->setRequired(boolval($item['required'] ?? false));
            $fieldInfo->setTips($item['tips'] ?? '');
            $fieldInfo->setSystemType(is_numeric($item['field']) ? 2 : 1);
            $fieldInfo->setIsArray(boolval($item['array_flag'] ?? false));

            if (in_array($item['field'], $quoteFields) && in_array($item['field_type'], [
                    FieldConstant::TYPE_OBJECT_ID,
                    FieldConstant::TYPE_OBJECT_MULTIPLE,
                    FieldConstant::TYPE_OBJECT_DYNAMIC,
            ]))
            {
                $fieldInfo->setFieldType(PaasFieldType::TYPE_UNKNOWN1);
            }

            if (in_array($item['field'], $quoteFields)) {
                $originType = $quoteList[$item['field']]['relation_origin_type'];
                $originField = $quoteList[$item['field']]['relation_origin_field'];
                $originFieldType = $quoteList[$item['field']]['relation_origin_field_type'];

                if (!in_array($originField, ['contact', 'tel_list']) && $originFieldType != CustomFieldService::FIELD_TYPE_MULTIPLE_SELECT) {
                    $fieldInfo->setFieldType($originFieldType);
                }

//                if (in_array($originField, (PaasData::SPECIAL_REFERENCE_FIELD[$originType] ?? []))) {
//                    $fieldInfo->setFieldType(PaasFieldType::TYPE_UNKNOWN1);
//                }
            }

            // 业务对象的字段配置不标准，这里转成标准化处理
            if ($objectName == PaasObjectName::OBJ_INQUIRY_COLLABORATION) {
                if ($item['field'] == 'social_media') {
                    $fieldInfo->setFieldType(FieldConstant::TYPE_SOCIAL_PLATFORM);
                    $fieldInfo->setIsArray(true);
                } elseif ($item['field'] == 'country') {
                    $fieldInfo->setFieldType(FieldConstant::TYPE_COUNTRY_REGION);
                } elseif ($item['field'] == 'source') {
                    $fieldInfo->setIsArray(true);
                }
            }

            if (!empty($item['ext_info']['value'])) {
                $extInfo = [];
                foreach ($item['ext_info']['value'] as $value) {
                    if (is_array($value)) {
                        $value = $value['value'] ?? '';
                    }

                    $extInfo[] = (new PaasFieldExtInfoOption())->setValue($value);
                }
                $fieldInfo->setExtInfo((new PaasFieldExtInfo)->setOptions($extInfo));
            }

            $fieldList[] = $fieldInfo;
        }

        $rsp = new PbPaasFieldInfoResponse();
        $rsp->setObjectName($objectName);
        $rsp->setFieldList($fieldList);


        $operateMap = [
            'view' => [
                'operate' => PaasObjectOperate::OPERATE_VIEW,
                'name' => \Yii::t('privilege', 'View'),
            ],
            'create' => [
                'operate' => PaasObjectOperate::OPERATE_CREATE,
                'name' => \Yii::t('privilege', 'Create'),
            ],
            'edit' => [
                'operate' => PaasObjectOperate::OPERATE_EDIT,
                'name' => \Yii::t('privilege', 'Edit'),
            ],
            'delete' => [
                'operate' => PaasObjectOperate::OPERATE_DELETE,
                'name' => \Yii::t('privilege', 'Delete'),
            ],
            'export' => [
                'operate' => PaasObjectOperate::OPERATE_EXPORT,
                'name' => \Yii::t('privilege', 'Export'),
            ],
            'change' => [
                'operate' => PaasObjectOperate::OPERATE_CHANGE,
                'name' => \Yii::t('privilege', 'Change'),
            ],
            'generate_object' => [
                'operate' => PaasObjectOperate::OPERATE_GENERATE_OBJECT,
                'name' => \Yii::t('privilege', 'Generate Object'),
            ],
        ];

        $operates = [];
        $privileges = \common\library\oms\common\OmsPrivilegeConstant::OPERATE_PRIVILEGE_MAP[PaasData::OBJECT_DATA_API_MAP[$objectName]['module_type']] ?? [];
        foreach ($privileges as $operate => $tasks) {
            foreach ($tasks as $task) {
                if (str_contains($task['task_class'], 'PrivilegeTask') || str_contains($task['task_class'], 'ScopeUserTask')) {
                    $operates[$operate] = is_array($task['setting']['privilege']) ? array_shift($task['setting']['privilege']) : $task['setting']['privilege'];
                }
            }
        }

        $operatePrivileges = [];
        $fieldList = \ArrayUtil::index($list[PaasData::OBJECT_DATA_API_MAP[$objectName]['object_name']], 'field');
        foreach ($operates as $operate => $privilege) {
            $operatePrivilege = new PaasObjectOperatePrivilege();
            $operatePrivilege->setOperate($operateMap[$operate]['operate'] ?? PaasObjectOperate::OPERATE_UNKNOWN);
            $operatePrivilege->setName($operateMap[$operate]['name'] ?? '');
            $operatePrivilege->setFlag(false);
            $operatePrivilege->setMessage(\Yii::t('common', '暂无权限'));

            if (str_contains($operate, 'change_')) {
                $fieldKey = str_replace('change_', '', $operate);
                $fieldInfo = $fieldList[$fieldKey] ?? null;
                $operatePrivilege->setOperate(PaasObjectOperate::OPERATE_CHANGE);
                $operatePrivilege->setFieldKey($fieldKey);
                $operatePrivilege->setName($operateMap['change']['name'] . ($fieldInfo['field_name'] ?? ''));
            } elseif (str_contains($operate, 'to_')) {
                $operatePrivilege->setOperate(PaasObjectOperate::OPERATE_GENERATE_OBJECT);
                $operatePrivilege->setName(\Yii::t('privilege', $operate));
                $operatePrivilege->setGenerateObject(PaasData::TO_OBJECT_NAME_MAP[$operate] ?? PaasObjectName::OBJECT_UNKNOWN);
            } elseif (empty($operateMap[$operate]['operate'])) {
                $extendOperate = new PaasObjectExtendOperate();
                $extendOperate->setName(\Yii::t('privilege', $operate));
                $extendOperate->setOperate($operate);
                $operatePrivilege->setOperate(PaasObjectOperate::OPERATE_EXTEND);
                $operatePrivilege->setName(\Yii::t('privilege', 'Expand operate'));
                $operatePrivilege->setExtendOperate($extendOperate);
            }

            if (PrivilegeHelper::hasPermission($user->getClientId(), $user->getUserId(), $privilege)) {
                $operatePrivilege->setFlag(true);
                $operatePrivilege->setMessage('');
            }

            if ($operatePrivilege->getOperate() == PaasObjectOperate::OPERATE_UNKNOWN) {
                continue;
            }

            $operatePrivileges[] = $operatePrivilege;
        }

        if (!empty($operatePrivileges)) {
            $rsp->setOperatePrivilege($operatePrivileges);
        }

//        dd($rsp->serializeToJsonString());

        $this->successPb($rsp);
    }
}