<?php
/**
 * Copyright (c) 2012 - 2018 <PERSON><PERSON>.All Rights Reserved
 * Author: nuxse
 * Data: 2018/6/20
 */

class ModuleService
{
    public static function getUserModules()
    {
        return [
            \common\library\version\Constant::USER_MODULE_USER_MAIL,
            \common\library\version\Constant::USER_MODULE_MAIL_FOLDER,
            \common\library\version\Constant::USER_MODULE_MAIL_TAG,
            \common\library\version\Constant::USER_MODULE_MAIL_SIGNATURE,
            \common\library\version\Constant::USER_MODULE_MAIL_SIGNATURE_SETTING,
            \common\library\version\Constant::USER_MODULE_MAIL_GENERAL_SETTING,
            \common\library\version\Constant::USER_MODULE_MAIL_RULE,
            \common\library\version\Constant::USER_MODULE_MAIL_PIN,
            \common\library\version\Constant::USER_MODULE_MAIL_TEXT,
            \common\library\version\Constant::USER_MODULE_CUSTOMER_TAG,
            \common\library\version\Constant::USER_MODULE_CONTACT,
            \common\library\version\Constant::USER_MODULE_MAIL_TODO,
            \common\library\version\Constant::USER_MODULE_MAIL_TEMPLATE,
            \common\library\version\Constant::USER_MODULE_PRIVILEGE,
        ];
    }


    public static function getClientModule()
    {
        return [
            \common\library\version\Constant::CLIENT_MODULE_CUSTOMER_GROUP,
            \common\library\version\Constant::CLIENT_MODULE_CUSTOMER_TRAIL_STATUS,
            \common\library\version\Constant::CLIENT_MODULE_COLLEAGUE,
            \common\library\version\Constant::CLIENT_MODULE_CONTACT,
            \common\library\version\Constant::CLIENT_MODULE_CUSTOMER_POOL,
            \common\library\version\Constant::CLIENT_MODULE_CUSTOMER_ORIGIN,
            \common\library\version\Constant::CLIENT_MODULE_SETTING,
            \common\library\version\Constant::CLIENT_MODULE_PRIVILEGE,
            \common\library\version\Constant::CLIENT_MODULE_MAIL_TEMPLATE,
            \common\library\version\Constant::CLIENT_MODULE_DISK_OVER_LIMIT_BUFFER
        ];
    }

    public static function getSystemModule()
    {
        return [
            \common\library\version\Constant::SYSTEM_MODULE_EMAIL_CONFIG,
            \common\library\version\Constant::SYSTEM_MODULE_MAIL_TEMPLATE,
        ];
    }
}