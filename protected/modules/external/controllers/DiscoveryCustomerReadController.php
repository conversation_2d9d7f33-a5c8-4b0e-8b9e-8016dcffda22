<?php
/**
 * Created by PhpStorm.
 * User: beenzhang
 * Date: 17/8/15
 * Time: 上午10:36
 */

use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;

class DiscoveryCustomerReadController extends Controller{
    public function filters(){
        return CMap::mergeArray(parent::filters(), [
            'checkPrivilege' => 'checkPrivilege - checkCrmPrivilege'
        ]);
    }

    public function actionGetBindCompanyStatus(array $discovery_company_ids)
    {
        $user = User::getLoginUser();

        if (empty($discovery_company_ids)) {
            return [];
        }

        $list = \common\library\customer\Helper::getHashIdAssocStatus($user->getUserId(), $discovery_company_ids);
        return $this->success($list);
    }

    public function actionGroupList()
    {
        $user = User::getLoginUser();
//        $client = \common\library\account\Client::getClient($user->getClientId());

        $customerPoolModel = false;
        $groupList = CustomerOptionService::getGroupList($user->getClientId());
        $customerPool = [];

        $data = [
            'group_list' => $groupList,
            'customer_pool_model' => $customerPoolModel,
            'customer_pool' => $customerPool,
        ];

        return $this->success($data);
    }


    /**
     * @return string
     * 可建档数量
     */
    public function actionAvailableCount()
    {
        $user = User::getLoginUser();

        $limit = $user->getCustomerLimit();

        if ($limit == 0)
            return $this->success(-1);

        $quota = $user->getCompanyCountData()['total_quota'];

        $ret = $limit - $quota;

        return $this->success($ret < 0 ? 0 : $ret);
    }

    public function actionCheckCrmPrivilege(){
        $user = User::getLoginUser();

        if (empty($user)) {
            return $this->success(false);
        }

        $privilegeService = PrivilegeService::getInstance($user->getClientId(), $user->getUserId());
        $access = $privilegeService->checkAccess();

        if (!$access->isPass()) {
            return $this->success(false);
        }

        return $this->success(true);
    }
}
