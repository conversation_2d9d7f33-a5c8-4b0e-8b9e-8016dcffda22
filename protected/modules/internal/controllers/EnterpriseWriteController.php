<?php

/**
 *
 * Author: ruisenlin
 * Date: 2023/6/8
 */

class EnterpriseWriteController extends InternalController
{
    public function actionSave($name, $logo, $tel, $fax, $homepage, $address, $email, $taxpayer_id, $business_license)
    {
        User::setLoginUserById($this->operatorUserId);
        $client_id = User::getLoginUser()->getClientId();

        $infos = [
            'name' => trim($name),
            'logo' => trim($logo),
            'tel' => trim($tel),
            'fax' => trim($fax),
            'homepage' => trim($homepage),
            'address' => trim($address),
            'email' => trim($email),
            'taxpayer_id' => trim($taxpayer_id),
            'business_license' => trim($business_license)
        ];

        $client = new \common\library\account\Client($client_id);
        $client->saveEnterpriseInfo($infos);
        $this->success([]);
    }

    /**
     * 开启关闭全员安全登录
     *
     * @param int $open
     * @param array $exclude_user_ids
     * @return void
     * @throws ProcessException
     */
    public function actionAllAccountSafeLogin($open = 1, array $exclude_user_ids = [])
    {
        $this->validate([
            'open' => 'int'
        ]);

        User::setLoginUserById($this->operatorUserId);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        if ($user->getUserId() != \common\library\privilege_v3\PrivilegeService::getInstance($user->getClientId())->getAdminUserId())
        {
            throw new \RuntimeException('账号无权限');
        }

        // 防并发处理
        $redis = RedisService::getInstance('redis');
        $lockKey = 'all_account_safe_login:lock:'. $clientId;
        $command = $redis->createCommand('set');
        $command->setArguments([$lockKey, 1, 'EX', 60, 'NX']); //1min
        $lock = $redis->executeCommand($command);

        if (!$lock) {
            $this->fail(ErrorCode::CODE_FAIL, '配置生效中，请稍后操作!');
        }

        try {
            $newConfig = [
                'open_flag' => intval($open),
                'exclude_user_ids' => $exclude_user_ids
            ];
            $job = new \common\library\queue_v2\job\AllAccountSafeLoginJob($user->getClientId(), $newConfig);
            $job->handle();

        }catch (Exception $e) {
            LogUtil::error("all_account_safe_login error ".var_export($newConfig,true) . $e->getMessage().$e->getTraceAsString());
            //将初始化所得的异常邮件通知
            throw new ProcessException($e->getMessage());
        }
        finally
        {
            $redis->del([$lockKey]);
        }

        $this->success(['client_id' => $user->getClientId()]);
    }
}