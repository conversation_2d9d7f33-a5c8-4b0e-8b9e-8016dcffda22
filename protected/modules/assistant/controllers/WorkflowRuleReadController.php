<?php

use common\library\workflow\WorkflowConstant as WorkflowConstant;

class WorkflowRuleReadController extends AssistantController
{

    public function actionList(
        array $refer_type = [],
        $trigger_type = '',
        $show_disabled = null,
        $start_date = '',
        $end_date = '',
        $page = 1,
        $curPage = 1,
        $page_size = 20,
        $pageSize = 20,
        $sort_type = 'desc',
        $sort_field = 'update_time',
        $rule_type = WorkflowConstant::RULE_TYPE_WORKFLOW,
        array $include_handlers = []
    ) {

        $this->validate([
            'refer_type' => 'array',
            'trigger_type' => 'string',
            'start_date' => 'string',
            'end_date' => 'string',
            'sort_type' => 'string|in:desc,asc',
            'sort_field' => 'string|in:update_time,create_time',
            'rule_type' => 'int',
            'include_handlers' => 'array',
        ]);

        $user = User::getLoginUser();
        $clientId = $user->getClientId();
        // 展示关闭，开启的
        $disableFlag = null;
        // 传入参数经过转换可能是字符串的数字
        is_numeric($show_disabled) && $show_disabled = (int)$show_disabled;
        if ($show_disabled === 1) {
            // 展示关闭的
            $disableFlag = 1;
        } elseif ($show_disabled === 0) {
            // 展示开启的
            $disableFlag = 0;
        }
        // 兼容任务列表
        if ($rule_type == 2) {
            $page = $curPage;
            $page_size = $pageSize;
        }
        $listQuery = new \common\library\workflow\WorkflowRuleList($clientId);
        $listQuery->setReferType($refer_type ?: null);
        $listQuery->setTriggerType($trigger_type ?: null);
        $listQuery->setOrder($sort_type);
        $listQuery->setOrderBy($sort_field);
        $listQuery->setDisableFlag($disableFlag);
        $listQuery->setOffset(($page - 1) * $page_size);
        $listQuery->setStartDate($start_date);
        $listQuery->setEndDate($end_date);
        $listQuery->setRuleType($rule_type);
        $listQuery->setLimit($page_size);
        $listQuery->setIncludeHandlers($include_handlers);

        $listQuery->getFormatter()->listInfo();

        $listTotal = $listQuery->count();
        $result = [
            'total' => $listTotal,
            'list' => $listTotal ? $listQuery->find() : [],
        ];

        return $this->success($result);
    }

}