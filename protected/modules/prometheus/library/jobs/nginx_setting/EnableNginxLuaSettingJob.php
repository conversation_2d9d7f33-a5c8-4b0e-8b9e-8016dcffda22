<?php
/**
 *
 * Author: ruisenlin
 * Date: 2024/3/29
 */


namespace common\modules\prometheus\library\jobs\nginx_setting;

use common\library\queue_v2\job\prometheus\PrometheusJob;
use common\library\report\error\ErrorReport;

class EnableNginxLuaSettingJob extends PrometheusJob
{

    protected $dcId;
    protected $cluster;
    protected $systemList;
    protected $batchId;
    protected $operator;
    protected $releasePlanId;

    protected $skipPodDeploy;
    protected $cancelGrey;

    public function __construct($dcId, $cluster, $systemList, $batchId = 0, $operator = 0, $releasePlanId = null, $skipPodDeploy = 0, $cancelGrey = 0)
    {
        $this->dcId       = $dcId;
        $this->cluster    = $cluster;
        $this->systemList = $systemList;
        $this->batchId = $batchId;
        $this->operator= $operator;
        $this->releasePlanId = $releasePlanId;
        $this->skipPodDeploy = $skipPodDeploy;
        $this->cancelGrey = $cancelGrey;
    }

    public function handle()
    {
        try {
            $nginxApply = new \common\modules\prometheus\library\nginx_configuration\apply\NginxLuaApply($this->dcId, $this->cluster);
            $nginxApply->setOperator($this->operator);
            $nginxApply->setSkipPodDeploy($this->skipPodDeploy);
            $nginxApply->setCancelGrey($this->cancelGrey);
            $nginxApply->apply($this->systemList, $this->batchId);
        } catch (\Exception $exception) {
            if ($this->releasePlanId) {
                $plan = new \common\modules\prometheus\library\release\plan\ReleasePlan($this->releasePlanId);
                $plan->updateDispatchStepInfo(\common\modules\prometheus\library\release\Constant::PLAN_DISPATCH_RULE_KEY_ENABLE_NGINX, $this->operator, \common\modules\prometheus\library\release\Constant::STATUS_FAIL);
            }
            \LogUtil::info(var_export($exception->getTraceAsString(), true));

            ErrorReport::phpError(new \CExceptionEvent(null, $exception), $exception->getTrace());
        }
    }
}