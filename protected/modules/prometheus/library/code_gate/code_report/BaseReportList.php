<?php

namespace common\modules\prometheus\library\code_gate\code_report;

use common\modules\prometheus\library\script\ScriptRelationModel;

class BaseReportList extends \MysqlList
{
    protected $reportIds = [];
    protected $projectIds = [];
    protected $branches = [];
    protected $module;
    protected $lastFlag;
    protected $type = '';
    protected $filed = '*';
    protected $groupBy;


    /**
     * @param array $reportIds
     */
    public function setReportIds(array $reportIds): void
    {
        $this->reportIds = $reportIds;
    }

    /**
     * @param array $projectIds
     */
    public function setProjectIds(array $projectIds): void
    {
        $this->projectIds = $projectIds;
    }

    /**
     * @param array $branches
     */
    public function setBranches(array $branches): void
    {
        $this->branches = $branches;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    /**
     * @param string $filed
     */
    public function setFiled(string $filed): void
    {
        $this->filed = $filed;
    }

    /**
     * @param mixed $module
     */
    public function setModule($module): void
    {
        $this->module = $module;
    }

    /**
     * @param mixed $groupBy
     */
    public function setGroupBy($groupBy): void
    {
        $this->groupBy = $groupBy;
    }

    /**
     * @param mixed $lastFlag
     */
    public function setLastFlag($lastFlag): void
    {
        $this->lastFlag = $lastFlag;
    }

    public function buildParams()
    {
        $sql = ' 1=1 ';
        $params = [];

        if ($this->reportIds)
        {
            $reportIds = implode(',', $this->reportIds);
            $sql .= " and report_id IN ({$reportIds}) ";
        }

        if ($this->projectIds)
        {
            $projectString = implode(',', $this->projectIds);
            $sql .= " and project_id IN ({$projectString}) ";
        }

        if ($this->type)
        {
            $sql .= " and type=:type";
            $params[':type'] = $this->type;
        }

        if ($this->branches){
            $branchString = implode("','", $this->branches);
            $sql .= " and branch IN ('{$branchString}') ";
        }

        if ($this->module){
            $sql .= " and module=:module";
            $params[':module'] = $this->module;
        }
        if ($this->lastFlag){
            $sql .= " and last_flag=:last_flag";
            $params[':last_flag'] = $this->lastFlag;
        }

        return [$sql, $params];
    }

    protected function getTable()
    {
        return '';
    }

    public function find() {
        $db = \Yii::app()->prometheus_db;
        $field = !empty($this->fields) ? $this->fields : '*';
        $table = $this->getTable();

        $orderBy = $this->buildOrderBy();
        $limit = $this->buildLimit();
        list($where, $params) = $this->buildParams();
        $sql = "SELECT {$field} FROM {$table} WHERE {$where} {$orderBy} {$limit}";
        $command = $db->createCommand($sql);
        $data = $command->queryAll(true, $params);

        return $data;
    }

    public function count() {
        $db = \Yii::app()->prometheus_db;
        $table = $this->getTable();

        list($where, $params) = $this->buildParams();
        $sql = "SELECT count(1) FROM {$table} WHERE {$where}";
        $command = $db->createCommand($sql);
        $count = $command->queryScalar($params);

        return intval($count);
    }
}