<?php

namespace common\modules\prometheus\library\app\version;

use common\modules\prometheus\library\app\AppList;

class AppVersionFormatter extends \ListItemFormatter
{
    const APP_PLATFORM_MAP = [
        0 => '未定义',
        1 => 'android',
        2 => 'windows',
        3 => 'ios'
    ];

    protected $showConvertPlatform = false;

    public function showConvertPlatform(): void
    {
        $this->showConvertPlatform = true;
    }


    public function buildMapData()
    {
        parent::buildMapData(); // TODO: Change the autogenerated stub

        $listData = $this->listData;

        $appKey = array_column($listData, 'app_key');

        $appList = new AppList();
        $appList->setAppKeys($appKey);
        $appDataList = array_column($appList->find(), null, 'app_key');

        $map = [
            'app' => $appDataList
        ];

        $this->setMapData($map);
    }


    protected function format($data)
    {
        $platform = $this->getMapData('app', $data['app_key'] ?? '')['app_platform'] ?? 0;
        if ($this->showConvertPlatform)
            $platform = self::APP_PLATFORM_MAP[$platform];
        $data['platform'] = $platform;

        return $data;
    }

}