<?php

use common\library\privilege_v3\Helper;
use common\library\privilege_v3\PrivilegeService;

/**
 * Copyright (c) 2012 - 2017 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2017/6/7
 */
class PrometheusService
{
    /**
     * @param $user_id
     * @param $client_id
     * @return array 用户绑定的邮件列表 含解密密码
     */
    public static function getUserMailList($user_id, $client_id)
    {
        $sql = "select user_mail_id,email_address,email_pwd_enc,email_pwd_salt,create_time,update_time from tbl_user_mail where user_id=:user_id and client_id=:client_id";
        $list = UserMail::model()->getDbConnection()->createCommand($sql)->queryAll(true,
            [
                ':user_id' => $user_id,
                ':client_id' => $client_id
            ]);

        if (!$list) {
            return [];
        }
        $data = [];
        foreach ($list as $key => $item) {
            $data[$key]['user_mail_id'] = $item['user_mail_id'];
            $data[$key]['email_address'] = $item['email_address'];
            $data[$key]['email_pwd'] = SecurityUtil::tripleDESDecrypt($item['email_pwd_enc'], $item['email_pwd_salt']);
            $data[$key]['create_time'] = $item['create_time'];
            $data[$key]['update_time'] = $item['update_time'];
        }
        return $data;
    }


    /**
     * 根据用户名称查询用户数据
     * @param string $nickname
     * @return mixed
     */
    public static function getUserByNickname(string $nickname)
    {

        $sql = "select user_id from tbl_user_info where nickname LIKE :nickname";
        $user = UserInfo::model()->getDbConnection()->createCommand($sql)->queryRow(true,
            [
                ':nickname' => '%' . $nickname . '%',
            ]);
        return $user ? User::getUserObject($user['user_id']) : null;
    }

    /**
     * 根据公司名称查询公司数据
     * @param string $name
     * @return mixed
     */
    public static function getClientByName(string $name)
    {

        $sql = "select client_id from tbl_client where `name` LIKE :name OR `full_name` LIKE :name";
        $client = Client::model()->getDbConnection()->createCommand($sql)->queryRow(true,
            [
                ':name' => '%' . $name . '%',
            ]);
        return $client ? \common\library\account\Client::getClient($client['client_id']) : null;
    }

    /**
     * 根据system_id查询client_id
     *
     * @return array|CDbDataReader
     * @throws CDbException
     * @throws CException
     */
    public static function getClientBySystemId($systemId , $clientIds = []) {

        if (is_array($systemId) || !empty($clientIds)) {
            $sql = "SELECT client_id , system_id FROM tbl_privilege_client_system WHERE `enable_flag` = 1  ";
            if(!empty($systemId)){
                $systemId = implode("','", $systemId);
                $sql = $sql . "AND `system_id` in ('{$systemId}') ";
            }
            if(empty($systemId) || !empty($clientIds)) {
                $clientIds = implode(",", $clientIds);
                $params[':client_ids'] = $clientIds;
                $sql = $sql . "AND `client_id` in ('{$clientIds}') ";
            }
            $sql = $sql."ORDER BY `client_id`";

        } else {
            $sql = "SELECT client_id FROM tbl_privilege_client_system 
				WHERE `system_id` = '{$systemId}' AND `enable_flag` = 1 
				ORDER BY `client_id`";
        }


        return Client::model()
            ->getDbConnection()
            ->createCommand($sql)
            ->queryAll();
    }


//	/**
//	 * 根据client_id查询client
//	 *
//	 * @param $clientId
//	 * @return array
//	 */
//	public static function getClientInfoById($clientId) {
//
//		$client = \common\library\account\Client::getClient($clientId);
//
//		if ($client->isNew()) {
//
//			throw new RuntimeException('client not exist');
//		}
//
//		return self::setClientInfoArr($client);
//	}
//
//	/**
//	 * 根据client_id修改client
//	 *
//	 * @param $clientId
//	 * @param $fullName
//	 * @return array
//	 */
//	public static function editClientInfoById($clientId, $fullName) {
//
//		$client = \common\library\account\Client::getClient($clientId);
//
//		if ($client->isNew()) {
//
//			throw new RuntimeException('client not exist');
//		}
//
//		$client->full_name = $fullName;
//
//		$client->save();
//
//		return self::setClientInfoArr($client);
//	}
//
//	/**
//	 * @param $client
//	 * @return array
//	 */
//	private static function setClientInfoArr($client) {
//
//		return [
//			'client_id'        => $client->client_id,
//			'client_name'      => $client->name,
//			'client_full_name' => $client->full_name,
//			'master_account'   => $client->master_account,
//			'mysql_set_id'     => $client->mysql_set_id,
//			'pgsql_set_id'     => $client->pgsql_set_id,
//			'systems'          => implode(',', PrivilegeService::getInstance($client->client_id)->getSystemIds())
//		];
//	}

    /**
     * 修改用户上限
     * @param $client_id
     * @param $num
     * @return \common\library\account\Client|null
     */
    public static function updateUserNum($client_id, $num)
    {
        $client = \common\library\account\Client::getClient($client_id);
        if ($client->isNew())
            return null;

        $client->user_num = $num;
        $client->save();
        return $client;
    }

    public static function getDbInfo($client_id)
    {
        $client = \common\library\account\Client::getClient($client_id);
        $mysql_set_id = $client->mysql_set_id;
        //    $mongo_set_id = $client->mongo_set_id;
        $pgsql_set_id = $client->pgsql_set_id;

        if( !$mysql_set_id )
        {
            return  [];
        }

        $mysql_info = Yii::app()->account_base_db->createCommand("SELECT * FROM tbl_db_set WHERE set_id=$mysql_set_id")->queryRow();
        //    $mongodb_info = Yii::app()->account_base_db->createCommand("SELECT * FROM tbl_db_set WHERE set_id=$mongo_set_id")->queryRow();

        $mysql_cmd = sprintf("mysql -h%s  -P%s -u%s -p%s -D%s",
            $mysql_info['host'],
            $mysql_info['port'],
            $mysql_info['user'],
            $mysql_info['password'],
            $mysql_info['name']
        );

        /*$mongodb_cmd = sprintf("/usr/local/mongodb3.2.6/bin/mongo --host %s --port %s -u%s -p%s %s",
            $mongodb_info['host'],
            $mongodb_info['port'],
            $mongodb_info['user'],
            $mongodb_info['password'],
            $mongodb_info['name']
        );*/
        $db = [
            'mysql' => [
                'type' => 'mysql',
                'host' => $mysql_info['host'],
                'port' => $mysql_info['port'],
                'name' => $mysql_info['name'],
                'schema_name' => $mysql_info['schema_name']??$mysql_info['name'],
//                'cmd' => $mysql_cmd
            ],
            /*'mongo' => [
                'type' => 'mongo',
                'host' => $mongodb_info['host'],
                'port' => $mongodb_info['port'],
                'name' => $mongodb_info['name'],
                'cmd' => $mongodb_cmd
            ]*/
        ];
        if ($pgsql_set_id) {
            $pgsql_info = Yii::app()->account_base_db->createCommand("SELECT * FROM tbl_db_set WHERE set_id=$pgsql_set_id")->queryRow();
            $pgsql_cmd = sprintf("psql -h %s  -p %s  \"dbname=%s user=%s password=%s\"",
                $pgsql_info['host'],
                $pgsql_info['port'],
                $pgsql_info['name'],
                $pgsql_info['user'],
                $pgsql_info['password']
            );
            $db['pgsql'] = [
                'type' => 'pgsql',
                'host' => $pgsql_info['host'],
                'port' => $pgsql_info['port'],
                'name' => $pgsql_info['name'],
                'schema_name' => $pgsql_info['schema_name']??'public',
//                'cmd' => $pgsql_cmd
            ];

        }
        return $db;

    }

    public static function updateDiskSpace($client_id, $space, $unit)
    {
        $client = \common\library\account\Client::getClient($client_id);
        if ($client->isNew())
            return null;

        $unit = strtoupper($unit);
        switch ($unit)
        {
            case 'B':
                $formula = 1;
                break;
            case 'KB':
                $formula = (1024);
                break;
            case 'MB':
                $formula = (1024 * 1024);
                break;
            case 'GB':
                $formula = (1024 * 1024 * 1024);
                break;
            case 'TB':
                $formula = (1024 * 1024 * 1024 * 1024);
                break;
            default:
                $formula = 1;
                break;
        }

        $space = $space * $formula;
        $client->disk_space = $space;
        $client->save();

        return $client;
    }

	/**
	 * 输出client csv
	 *
	 * @param $systemId
	 * @throws CDbException
	 * @throws CException
	 */
	public static function exportClientId($systemId) {

    	$title = [['client_id']];

		$data = self::getClientBySystemId($systemId);

		$data = array_merge($title, $data);

		self::exportCSV($systemId . '-' . time(), $data);
	}

	/**
	 * 输出 csv
	 *
	 * @param $fileName
	 * @param $data
	 */
	public static function exportCSV($fileName, $data) {

		header("Content-type: text/csv");

		header("Content-Disposition: attachment; filename={$fileName}.csv");

		header("Pragma: no-cache");

		header("Expires: 0");

		$outputBuffer = fopen("php://output", 'w');

		foreach ($data as $val) {

			foreach ($val as $key => $val2) {

				$val[$key] = iconv('utf-8', 'gbk', $val2);// CSV的Excel支持GBK编码，一定要转换，否则乱码
			}

			fputcsv($outputBuffer, $val);
		}

		fclose($outputBuffer);
	}


	public static function getFunctionalList() {

		$configList = Helper::getConfig();

		foreach ($configList as $systemId => $config) {

			foreach ($config['functional'] as $functionalId => $value) {

				if (!$value) {

					unset($configList[$systemId]['functional'][$functionalId]);
					continue;
				}

				$configList[$systemId]['functional'][$functionalId] = [
					'key'   => $functionalId,
					'name'  => \Yii::t('privilege', $functionalId),
				];
			}

			unset($configList[$systemId]['version'], $configList[$systemId]['role']);
		}

		return $configList;
	}

    public static function getClientByConditions($systemId=[], $client_type = '',$functional = '' , $switch = ''){
        $clientIds = $resultSystem = $resultPrivilege = [];
        if($switch) {
            $clientIds = self::getClientInfoExternal($switch);
        }

        if($functional) {
            $resultPrivilege = self::getClientPrivilege($clientIds,$functional);
            $clientIds = array_column($resultPrivilege, 'client_id');
        }

        $resultSystem = self::getClientBySystemId($systemId , $clientIds);
        $clientIds = array_column($resultSystem , 'client_id');

        if(empty($clientIds)){
            return [];
        }
        $clientIds = implode('\',\'', $clientIds);
        $sql ="SELECT client_id , master_account  FROM tbl_client WHERE `client_id` in ('{$clientIds}') AND `enable_flag` = 1 ";
        if($client_type) {
            $sql = $sql . " AND `client_type` = {$client_type}";
        }
        $sql = $sql." ORDER BY `client_id`";

        $result = Client::model()
            ->getDbConnection()
            ->createCommand($sql)
            ->queryAll(true);
        $result = array_combine(array_column($result , 'client_id') , array_column($result , 'master_account'));

        foreach ($resultSystem as $key => $value) {
            $resultSystem[$key]['master_account'] = $result[$value['client_id']] ?? '';
        }

        return $resultSystem;

    }

    public static function getClientInfoExternal($switch = ''){

        $sql = "SELECT client_id FROM tbl_client_info_external 
				WHERE `key` = '{$switch}' 
				ORDER BY `client_id`";
        $resultExternal = \Yii::app()->db->createCommand($sql)->queryAll(true);

        return array_column($resultExternal, 'client_id');

    }
    public static function getClientPrivilege($clientIds = [],$functional = ''){
        $sql = "SELECT client_id FROM tbl_client_privilege WHERE `enable_flag` = 1 ";
        if($clientIds){
            $clientIds = implode('\',\'', $clientIds);
            $sql = $sql."AND `client_id` in ('{$clientIds}')";
        }
        if($functional){
            $sql = $sql ."AND `privilege` = '{$functional}'" ;
        }

        $sql =  $sql."ORDER BY `client_id`";
        return Client::model()
            ->getDbConnection()
            ->createCommand($sql)
            ->queryAll(true);

    }


    /**
     * 根据ames邮箱 查询user_id
     * @param string $mail
     * @return string user_id
     */
    public static function getUserIdByAmesMail(string $mail){
        $sql = "select user_id from tbl_user_info where `ames_email` = '{$mail}'";
        $user_id = Client::model()->getDbConnection()->createCommand($sql)->queryScalar();
        return $user_id ?:  0;
    }

}
