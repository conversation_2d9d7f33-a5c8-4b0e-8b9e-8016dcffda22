<?php

use common\library\setting\library\link\LinkApi;
use common\library\speechcraft\Constant;
use common\library\speechcraft\SystemSpeechcraftApi;

/**
 * 成单话术管理
 */
class SpeechcraftController extends PrometheusController
{
    public function actionIndex(): void
    {
        $this->render('/speechcraft/index.js');
    }

    /**
     * 系统话术列表
     */
    public function actionList($keywords = '', $cur_page = 1, $page_size=20, $enable_flag = ''):string
    {
        $this->validate([
            'keywords' => 'string',
            'cur_page' => 'integer|min:1',
            'page_size' => 'integer|min:1|max:100',
            'enable_flag' => 'in:0,1'
        ]);
        $service = new SystemSpeechcraftApi();
        $service->setKeywords($keywords);
        $result = $service->getList($enable_flag, $cur_page, $page_size);

        return $this->success($result);
    }

    /**
     * 批量导入话术
     *
     * @param array $rows
     * @return string
     * @throws CDbException
     * @throws CException
     */
    public function actionBulkInsert(array $rows):string
    {
        $this->validate([
            'rows' => 'required|array',
            'rows.*.title'       => Constant::RULE_TITLE,
            'rows.*.content'     => Constant::getContentRule(),
            'rows.*.enable_flag' => Constant::RULE_ENABLE_FLAG,
            'rows.*.link_ids' => 'array',
            'rows.*.link_ids.*' => 'int',
        ]);
        $userId   = User::getLoginUser()->getUserId();

        $service = new SystemSpeechcraftApi();
        $service->bulkInsert($rows, $userId);
        return $this->success();
    }

    /**
     * 编辑话术
     * @throws CDbException
     */
    public function actionEdit($tips_id, string $title, string $content, $enable_flag, array $link_ids = []):string
    {
        $this->validate([
            'tips_id' => 'required|int',
            'title'          => Constant::RULE_TITLE,
            'content'        => Constant::getContentRule(),
            'enable_flag'    => Constant::RULE_ENABLE_FLAG,
            'link_ids' => 'array',
            'link_ids.*' => 'int',
        ]);

        $userId   = User::getLoginUser()->getUserId();

        $service = new SystemSpeechcraftApi();
        $service->update($userId, $tips_id, $title, $content, $enable_flag, $link_ids);

        return $this->success();
    }

    /**
     * 创建话术
     */
    public function actionCreate(string $title, string $content, $enable_flag, array $link_ids = []): string
    {
        $this->validate([
            'title'       => Constant::RULE_TITLE,
            'content'     => Constant::getContentRule(),
            'enable_flag' => Constant::RULE_ENABLE_FLAG,
            'link_ids'    => 'array',
            'link_ids.*'  => 'int',
        ]);
        $userId   = User::getLoginUser()->getUserId();
        $speechcraft = (new SystemSpeechcraftApi())->create($userId, $title, $content, $enable_flag, $link_ids);

        return $this->success([
            'tips_id' => $speechcraft->tips_id
        ]);
    }

    /**
     * 批量操作
     * @param string $action 操作：enable,disable,delete
     * @param array<int> $ids 系统话术IDs数组
     * @return string
     */
    public function actionBulk(string $action, array $ids): string
    {
        $this->validate([
            'action' => 'required|string|in:enable,disable,delete',
            'ids'    => 'required|array',
            'ids.*'  => 'required|int|min:1'
        ]);

        $userId   = User::getLoginUser()->getUserId();
        $service = new SystemSpeechcraftApi();

        switch ($action) {
            case  'enable': // 批量启用
                $service->bulkSetStatus($userId, $ids, Constant::FLAG_ENABLE);
                break;
            case 'disable':  // 批量禁用
                $service->bulkSetStatus($userId, $ids, Constant::FLAG_DISABLED);
                break;
            case 'delete':   // 批量删除
                $service->bulkDelete($userId, $ids);
                break;
        }
        return $this->success();
    }

    /**
     * 获取销售环节
     */
    public function actionLinks()
    {
        return $this->success([
            'links' => LinkApi::getDefaultLinks()
        ]);
    }
}
