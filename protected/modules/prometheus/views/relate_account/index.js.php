<?php
$template = <<<HTML
<el-tabs>
    <el-tab-pane label="授权管理">
        <el-form :inline="true"  class="demo-form-inline">
            <el-form-item label="">
                <el-button type="primary" @click="addRelateAccountDialog = true">新增授权账号</el-button>
            </el-form-item>
        </el-form>
        
        <el-table :data="relateAccountList" style="width: 100%;">
            <el-table-column prop="email" label="email"></el-table-column>
            <el-table-column prop="relate_account_sg" label="关联账号"></el-table-column>
            <el-table-column fixed="right" label="操作">
                <template slot-scope="scope">
                    <el-button @click="releaseRelateAccount(scope.row)" type="text" size="small">取消授权</el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <el-dialog title="新增授权账号" :visible.sync="addRelateAccountDialog" width="30%">
            <el-form ref="form" :model="addRelateAccountObj">
                <el-form-item label="email">
                  <el-input v-model="addRelateAccountObj.email"></el-input>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="addRelateAccount(addRelateAccountObj, 1);">新增授权账号</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </el-tab-pane>
</el-tabs>
HTML;
?>

Vue.component('relate-account-center', {
    template: `<?php  echo $template?>`,
    data: function () {
        return {
            relateAccountList: [],
            addRelateAccountObj: {email: ''},
            addRelateAccountDialog: false,
        }
    },
    created: function () {
        this.getRelateAccountList();
    },
    methods: {
        //获取角色列表
        getRelateAccountList: function() {
            this.$http.get('/prometheus/relateAccountRead/list').then(function (response) {
                if (response.data.code == 0 && response.data.data) {
                    this.relateAccountList = response.data.data.list;
                } else {
                    this.$message.error(response.data.msg);
                }
            });
        },

        addRelateAccount: function (row) {
            this.$http.get('/prometheus/relateAccountWrite/create?email=' + row.email).then(function (response) {
                if (response.data.code == 0 && response.data.data) {
                    this.getRelateAccountList();
                    row.email = '';
                    this.addRelateAccountDialog = false; //关闭弹出层
                } else {
                    this.$message.error(response.data.msg);
                }
            });
        },

        releaseRelateAccount: function (row) {
            this.$http.get('/prometheus/relateAccountWrite/release?email=' + row.email).then(function (response) {
                if (response.data.code == 0 && response.data.data) {
                    this.getRelateAccountList();
                } else {
                    this.$message.error(response.data.msg);
                }
            });
        },
    },
    filters: {
        prettyJson: function (data) {
            return JSON.stringify(data, null, 2);
        }
    },
})
