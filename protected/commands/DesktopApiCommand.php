<?php
/**
 * Copyright (c) 2012 - 2018 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2018/4/27
 */

class DesktopApiCommand  extends  CrontabCommand
{


    public function actionTestSyncInfo()
    {
        \User::setLoginUserById(6438);

        $userId = 6438;
        $clientId = 3;

        $userMailId = 6438;
        $version = 39;
        $maxMailId = 0;

        $data = [];
        $db = ProjectActiveRecord::getDbByClientId($clientId);

        if ($userMailId)
        {
            if (!$version) // 需要同步老数据
            {
                $sql = "select mail_id from tbl_mail_version where user_mail_id=$userMailId and type=1 order by version asc limit 1";
                $syncMinMailId = $db->createCommand($sql)->queryScalar();

                $constType = \common\library\version\Constant::MAIL_MODULE_ADD;

                if ($syncMinMailId)
                {
                    // 只取需要同步的
                    $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id not in (0,9,10,11) and mail_id>$maxMailId and mail_id<$syncMinMailId order by mail_id asc";
                }
                else
                {
                    $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id not in (0,9,10,11) and mail_id>$maxMailId order by mail_id asc";
                }
                $data = $db->createCommand($sql)->queryAll(false);
            }

            $data = array_merge($data, $db->createCommand("select mail_id, type, version from tbl_mail_version where user_mail_id=$userMailId and version>$version order by version asc")->queryAll(false));

        }
        else
        {
            if (!$version)
            {
                $constType = \common\library\version\Constant::MAIL_MODULE_DRAFT;

                $sql = "select mail_id from tbl_mail_draft_version where user_id=$userId and type=$constType order by version asc limit 1";
                $syncMinMailId = $db->createCommand($sql)->queryScalar();

                if ($syncMinMailId)
                {
                    // 只取需要同步的
                    $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id=0 and mail_id>$maxMailId and mail_id<$syncMinMailId order by mail_id asc";
                }
                else
                {
                    $sql = "select mail_id,$constType,0 from tbl_mail where user_mail_id=$userMailId and folder_id=0 and mail_id>$maxMailId order by mail_id asc";
                }
                $data = $db->createCommand($sql)->queryAll(false);
            }

            $data = array_merge($data, $db->createCommand("select mail_id, type, version from tbl_mail_draft_version where user_id=$userId and version>$version order by version asc")->queryAll(false));

        }

        $mapping = [];
        foreach ($data as $datum)
        {
            $mailId = $datum[0];
            $type = $datum[1];
            $ver = $datum[2];

            $syncInfo = 0;
            switch ($type)
            {
                case \common\library\version\Constant::MAIL_MODULE_ADD:
                    $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG);
                    break;
                case \common\library\version\Constant::MAIL_MODULE_EDIT:
                    $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_STATUS_INFO;
                    break;
                case \common\library\version\Constant::MAIL_MODULE_TAG:
                    $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG;
                    break;
                case \common\library\version\Constant::MAIL_MODULE_REMOVE:
                    $syncInfo = (1 << \common\library\version\Constant::MAIL_SYNC_BIT_STATUS_INFO) | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_TAG);
                    break;
                case \common\library\version\Constant::MAIL_MODULE_DRAFT:
                    $syncInfo = 1 << \common\library\version\Constant::MAIL_SYNC_BIT_ALL_INFO | (1 << \common\library\version\Constant::MAIL_SYNC_BIT_DRAFT_INFO);
                    break;
                case \common\library\version\Constant::MAIL_MODULE_DRAFT_DELETE:
                    $syncInfo = 0;
                    break;
                default:
                    LogUtil::info('unsupported mail version type ' . $type);
                    continue;
            }

            if (array_key_exists($mailId, $mapping))
            {
                if ($syncInfo)
                    $mapping[$mailId][1] |= $syncInfo;
                else
                    $mapping[$mailId][1] = 0;
            }
            else
            {
                $mapping[$mailId] = [$mailId, $syncInfo, $ver];
            }
        }

        $mapping = array_values($mapping);

        print_r($data);
        print_r($mapping);
    }
}