<?php


use common\library\export_v2\Export;
use common\library\notification\Constant;
use common\library\privilege_v3\PrivilegeConstants;
use common\library\privilege_v3\PrivilegeService;
use common\library\push\Browser;
use common\library\setting\user\UserSetting;
use common\library\work_journal\export\WorkJournalExportExecutor;

/**
 * 工作报告导出脚本
 */
class WorkJournalCommand extends \CrontabCommand
{

    public function actionExport($user_id, $export_id)
    {

        $beginTime = microtime(true);
        self::info('begin: user_id:' . $user_id . ' export_id:' . $export_id);

        \User::setLoginUserById($user_id);
        $user = \User::getLoginUser();

        $export = new Export($user->getClientId(), $export_id);

        $executor = new WorkJournalExportExecutor($export);
        $executor->run();

        $endTime = microtime(true);

        self::info('finish: user_id:' . $user_id . ' export_id:' . $export_id . ' time:' . ($endTime - $beginTime));

    }

    /**
     * 工作报告提交提醒
     *
     * @param $setIds
     * @return void
     * @throws ProcessException
     */
    public function actionWorkJournalSubmitRemind($dryRun = 0)
    {
        $workJournalSubmitRemindStartTime = time();
        LogUtil::info("工作报告提交提醒开始时间：{$workJournalSubmitRemindStartTime}");

        // 获取当前分钟的零秒时间戳，使用0秒时间戳，可以兼容一分钟内的误差
        $currentMinuteTimestamp = strtotime(date('Y-m-d H:i:00', time()));

        $compareDay = strtotime(date('Y-m-d 00:00:00'));
        // 提交截止时间是当日的情况
        $currentDayTimestamp = [$currentMinuteTimestamp - $compareDay];
        // 提交截止时间是次日的情况
        $currentDayTimestamp[] = $currentMinuteTimestamp - $compareDay + 86400;

        // 提交周期是本周一 ~ 本周日的情况
        // 提交截止时间是当周的情况
        $compareWeek = strtotime(date('Y-m-d', strtotime('this week')) . ' 00:00:00');
        $currentWeekTimestamp1 = [$currentMinuteTimestamp - $compareWeek];
        $weekDay = date('N');
        if ($weekDay <= 4) {
            // 周报的提交截止时间最晚是次周四午夜零点
            // 提交截止时间是次周的情况
            $currentWeekTimestamp1[] = $currentMinuteTimestamp - $compareWeek + 86400 * 7;
        }
        $currentWeekTimestamp1 = array_unique($currentWeekTimestamp1);
        // 提交周期是上周日 ~ 本周六的情况
        // 提交截止时间是当周的情况
        $compareWeek2 = strtotime(date('Y-m-d', strtotime('last sunday')) . ' 00:00:00');
        $currentWeekTimestamp2[] = $currentMinuteTimestamp - $compareWeek2;
        if ($weekDay <= 3) {
            // 周报的提交截止时间最晚是次周三午夜零点
            // 提交截止时间是次周的情况
            $currentWeekTimestamp2[] = $currentMinuteTimestamp - $compareWeek2 + 86400 * 7;
        }
        $currentWeekTimestamp2 = array_unique($currentWeekTimestamp2);
        // 提交周期是上周六 ~ 本周五的情况
        // 提交截止时间是当周的情况
        $compareWeek3 = strtotime(date('Y-m-d', strtotime('last saturday')) . ' 00:00:00');
        $currentWeekTimestamp3[] = $currentMinuteTimestamp - $compareWeek3;
        if ($weekDay <= 2) {
            // 周报的提交截止时间最晚是次周二午夜零点
            // 提交截止时间是次周的情况
            $currentWeekTimestamp3[] = $currentMinuteTimestamp - $compareWeek3 + 86400 * 7;
        }
        $currentWeekTimestamp3 = array_unique($currentWeekTimestamp3);
        // 提交周期是上周五 ~ 本周四的情况
        // 提交截止时间是当周的情况
        $compareWeek4 = strtotime(date('Y-m-d', strtotime('last friday')) . ' 00:00:00');
        $currentWeekTimestamp4[] = $currentMinuteTimestamp - $compareWeek4;
        if ($weekDay <= 1) {
            // 周报的提交截止时间最晚是次周一午夜零点
            // 提交截止时间是次周的情况
            $currentWeekTimestamp4[] = $currentMinuteTimestamp - $compareWeek4 + 86400 * 7;
        }
        $currentWeekTimestamp4 = array_unique($currentWeekTimestamp4);

        $compareMonth = strtotime(date('Y-m-01 00:00:00'));
        // 提交截止时间是当月的情况
        $currentMonthTimestamp = [$currentMinuteTimestamp - $compareMonth];
        // 特殊逻辑，假如是一个月的最后一天，那么就要生成大于今天的增量时间戳
        // 例如：2.28日是最后一天，那么提醒时间设置为 29号，30号，31号，32号（设置最后一天就存32号的）的都要查询
        $monthLastDay = date('t', $currentMinuteTimestamp);
        $nowDay = date('d', $currentMinuteTimestamp);
        if ($monthLastDay == $nowDay) {
            $nowDay = (int)$nowDay;
            for ($i = ($nowDay + 1); $i <= 32; $i++) {
                $currentMonthTimestamp[] = ($i - $nowDay) * 86400 + $currentMinuteTimestamp - $compareMonth;
            }
        }
        $monthDay = date('j', $currentMinuteTimestamp);
        if ($monthDay <= 15) {
            // 月报的提交截止时间最晚是次月的15号午夜零点
            // 提交截止时间是次月的情况
            $lastMonth = strtotime('-1 month', $currentMinuteTimestamp);
            $daysInLastMonth = date('t', $lastMonth);
            $currentMonthTimestamp[] = $currentMinuteTimestamp - $compareMonth + $daysInLastMonth * 86400;
        }
        $currentMonthTimestamp = array_unique($currentMonthTimestamp);

        //周报的时间戳 跟 月报的时间戳 存在交集 --bug=https://www.tapd.cn/21404721/bugtrace/bugs/view/1121404721001090843
        $commitRemindTimeFilter = "commit_remind_time != '{}' and (".
        "(template_type=:template_type_daily and commit_remind_time && ARRAY[". implode(',', $currentDayTimestamp)."]::bigint[]) or ".
        "(template_type=:template_type_week and commit_remind_time && ARRAY[". implode(',', $currentWeekTimestamp1)."]::bigint[] and week_cycle_type = 1) or ".
        "(template_type=:template_type_week and commit_remind_time && ARRAY[". implode(',', $currentWeekTimestamp2)."]::bigint[] and week_cycle_type = 2) or ".
        "(template_type=:template_type_week and commit_remind_time && ARRAY[". implode(',', $currentWeekTimestamp3)."]::bigint[] and week_cycle_type = 3) or ".
        "(template_type=:template_type_week and commit_remind_time && ARRAY[". implode(',', $currentWeekTimestamp4)."]::bigint[] and week_cycle_type = 4) or ".
        "(template_type=:template_type_month and commit_remind_time && ARRAY[". implode(',', $currentMonthTimestamp)."]::bigint[]))";
        $params = [
            ':template_type_daily' => \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY,
            ':template_type_week'  => \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_WEEK,
            ':template_type_month' => \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_MONTH
        ];

        $getSetIdStartTime = microtime(true);
        //测试环境只有一个db 只需要执行一次
        if (\Yii::app()->params['env'] == 'test') {
            $sql = 'SELECT distinct pgsql_set_id from tbl_client WHERE pgsql_set_id > 0 and dc_id = 1 LIMIT 1';
            $setIds = Client::model()->getDbConnection()->createCommand($sql)->queryColumn();
        } else {
            $sql = "SELECT distinct pgsql_set_id from tbl_client WHERE pgsql_set_id > 0 and dc_id = 1";
            $setIds = Client::model()->getDbConnection()->createCommand($sql)->queryColumn();
        }
        $getSetIdEndTime = microtime(true);
        $getSetIdTimeConsuming = round(($getSetIdEndTime - $getSetIdStartTime) * 1000);
        self::info("获取setIds消耗的时间：{$getSetIdTimeConsuming}ms");

        $privilege = PrivilegeConstants::FUNCTIONAL_WORK_JOURNAL;
        $nowDay = date('Y-m-d 00:00:00');
        $today = date('Y-m-d', strtotime($nowDay));
        $nowYear = date('Y', strtotime($nowDay));
        $service = new \common\library\schedule\HolidayService();
        $holidays = $service->loadChineseHoliday($nowYear);

        foreach ($setIds as $setId)
        {
            $setIdStartTime = date('Y-m-d H:i:s');
            LogUtil::info("setId：{$setId}，开始执行：{$setIdStartTime}");

            $sql = " select distinct(client_id) from tbl_client_privilege where client_id in (select client_id from tbl_client where pgsql_set_id={$setId} and  valid_to >='{$nowDay}') and privilege = '{$privilege}' and enable_flag=1";
            $privilegeClientId = \Yii::app()->account_base_db->createCommand($sql)->queryColumn();
            if (empty($privilegeClientId)) {
                continue;
            }

            $setIdGetTemplateStartTime = microtime(true);
            $sql = "select client_id,template_id,name,commit_user_type,commit_user_value,repeat_day,template_type,commit_end_time,skip_holiday,week_cycle_type from tbl_work_journal_template where enable_flag = 1 and delete_flag = 0 and {$commitRemindTimeFilter} and client_id in (" . implode(',', $privilegeClientId) . ")";
            $needRemindTemplateList = \PgActiveRecord::getDbByDbSetId($setId)->createCommand($sql)->queryAll(true, $params);
            $setIdGetTemplateEndTime = microtime(true);
            $getSetIdTimeConsuming = round(($setIdGetTemplateEndTime - $setIdGetTemplateStartTime) * 1000);
            self::info("setId：{$setId}，setId获取需要提醒的模板的时间：{$getSetIdTimeConsuming}ms");

            $needRemindTemplateCount = count($needRemindTemplateList);
            LogUtil::info("setId：{$setId}，需要提醒的模板数量：{$needRemindTemplateCount}");

            // 组装 key为client_id，value为模板列表，减少client_id循环次数
            $templateListKeyByClientId = [];
            foreach ($needRemindTemplateList as $itemNeedRemindTemplate)
            {
                if ($itemNeedRemindTemplate['template_type'] == \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY) {
                    $repeatDays = \common\library\util\PgsqlUtil::trimArray($itemNeedRemindTemplate['repeat_day'] ?? []);
                    $skipHolidayFlag = $itemNeedRemindTemplate['skip_holiday'] ?? 0;
                    $commitEndTimestamp = $itemNeedRemindTemplate['commit_end_time'] ?? 86400;
                    if ($commitEndTimestamp <= 86400) {
                        // 判断今天是否需要提交日报
                        $comparaWeekDay = $today;
                    } else {
                        // 判断昨天是否需要提交日报
                        $weekDay = date('N', strtotime($today . ' -1 day'));
                        $comparaWeekDay = date('Y-m-d', strtotime($today . ' -1 day'));
                    }
                    // 日报类型会有提交日期，假如存在提交日期，根据今天是周几，判断是否需要提交。
                    if (!empty($repeatDays) && !in_array($weekDay, $repeatDays)) {
                        if (empty(array_diff([1, 2, 3, 4, 5], $repeatDays)) && $skipHolidayFlag) {
                            // 如果周一 ~ 周五5天已选满，调休日当天的日报需要提交。
                            $isSpecialWorkDay = false;
                            foreach ($holidays as $holiday) {
                                if ($holiday['holiday_type'] == \common\models\admin\Holiday::HOLIDAY_TYPE_CN_SPECIAL_WORKDAY) {
                                    if ($holiday['holiday_date'] == $comparaWeekDay) {
                                        $isSpecialWorkDay = true;
                                        break;
                                    }
                                }
                            }
                            if (!$isSpecialWorkDay) {
                                // 是周末，但不是调休日，日报不需要提交
                                continue;
                            }
                        } else {
                            // 不在选中的日期范围内，日报不需要提交
                            continue;
                        }
                    } elseif (!empty($repeatDays) && in_array($weekDay, $repeatDays) && $skipHolidayFlag) {
                        $isHolidayDay = false;
                        foreach ($holidays as $holiday) {
                            if ($holiday['holiday_type'] == \common\models\admin\Holiday::HOLIDAY_TYPE_CN_HOLIDAY) {
                                if ($holiday['holiday_date'] == $comparaWeekDay) {
                                    $isHolidayDay = true;
                                    break;
                                }
                            }
                        }
                        if ($isHolidayDay) {
                            // 假如是节假日，日报不需要提交
                            continue;
                        }
                    }
                }

                $itemClientId = $itemNeedRemindTemplate['client_id'] ?? '';
                if (isset($templateListKeyByClientId[$itemClientId])) {
                    $templateListKeyByClientId[$itemClientId][] = $itemNeedRemindTemplate;
                } else {
                    $templateListKeyByClientId[$itemClientId] = [$itemNeedRemindTemplate];
                }
            }

            foreach ($templateListKeyByClientId as $itemClientId => $workJournalTemplateList)
            {
                $mysqlDb = ProjectActiveRecord::getDbByClientId($itemClientId);
                $workJournalTemplateCount = count($workJournalTemplateList);
                LogUtil::info("client_id：{$itemClientId}，需要提醒的模板数量：{$workJournalTemplateCount}");

                $departmentRedis = new \common\library\department\DepartmentRedis();
                $userPrivilegeService = new \common\library\privilege_v3\UserPrivilegeService($itemClientId);
                $userIdsKeyByDepartmentId = [];
                $userIdsKeyByRoleId = [];

                // 先统计整个client设置的部门和角色，避免重复获取
                $departmentIds = [];
                $roleIds = [];
                $allUserIds = [];
                foreach ($workJournalTemplateList as $itemWorkJournalTemplate)
                {
                    $commitUserType = $itemWorkJournalTemplate['commit_user_type'];
                    $commitUserValue = \common\library\util\PgsqlUtil::trimArray($itemWorkJournalTemplate['commit_user_value']);

                    if ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_DEPARTMENT) {
                        $departmentIds = array_merge($departmentIds, $commitUserValue);
                    } elseif ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_USER) {
                        $allUserIds = array_merge($allUserIds,$commitUserValue);
                    } elseif ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_ROLE) {
                        $roleIds = array_merge($roleIds, $commitUserValue);
                    }
                }
                $departmentIds = array_unique($departmentIds);
                $roleIds = array_unique($roleIds);
                foreach ($departmentIds as $departmentId) {
                    $userIdsKeyByDepartmentId[$departmentId] = $departmentRedis->getUserList($itemClientId, $departmentId);
                    $allUserIds = array_merge($allUserIds, $userIdsKeyByDepartmentId[$departmentId]);
                }
                foreach ($roleIds as $roleId) {
                    $tempUserIdsKeyByRoleId = $userPrivilegeService->getRoleUserIdsMap($roleId);
                    $userIdsKeyByRoleId[$roleId] = $tempUserIdsKeyByRoleId[$roleId];
                    // 空字符串会导致下面对merge报错
                    if (!is_array($userIdsKeyByRoleId[$roleId])) {
                        $userIdsKeyByRoleId[$roleId] = [];
                    }
                    $allUserIds = array_merge($allUserIds, $userIdsKeyByRoleId[$roleId]);
                }
                $allUserIds = array_unique($allUserIds);
                if (empty($allUserIds)) continue;

                $allUserIdStr = implode(",",$allUserIds);
                // 获取userSetting配置
                $key = UserSetting::NOTIFICATION_APP_PUSH_SETTING;

                $userSettingSql = "select user_id,value from tbl_user_setting where client_id = {$itemClientId} and `key` = '{$key}' and user_id in ({$allUserIdStr})";
                $userIdToSettingMap = array_column($mysqlDb->createCommand($userSettingSql)->queryAll(),'value','user_id');
                // 获取到work_journal_remind 的配置
                $userIdToWorkJournalRemindSettingMap = array_map(function ($value)
                {
                    $value = json_decode($value,true);
                    $tempMap = array_column($value,null,'module');
                    return $tempMap[Constant::APP_MODULE_WORK_JOURNAL_REMIND] ?? [
                        'module' => Constant::APP_MODULE_WORK_JOURNAL_REMIND,
                        'push_flag' => \common\library\setting\user\process\Notification::APP_PUSH_DEFAULT_SETTING[Constant::APP_MODULE_WORK_JOURNAL_REMIND],
                    ];
                },$userIdToSettingMap);


                // 循环遍历模版进行消息通知
                foreach ($workJournalTemplateList as $itemWorkJournalTemplate)
                {
                    $itemTemplateId = $itemWorkJournalTemplate['template_id'] ?? '';
                    $itemTemplateType = $itemWorkJournalTemplate['template_type'] ?? \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY;
                    $itemWeekCycleType = $itemWorkJournalTemplate['week_cycle_type'] ?? \common\models\client\WorkJournalTemplate::WEEK_CYCLE_TYPE_THIS_WEEK;
                    $itemCommitUserValue = \common\library\util\PgsqlUtil::trimArray($itemWorkJournalTemplate['commit_user_value']);

                    // 获取全部提交人
                    $commitUserType = $itemWorkJournalTemplate['commit_user_type'] ?? \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_USER;
                    $workJournalTemplateUserIds = [];
                    if ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_USER) {
                        $workJournalTemplateUserIds = $itemCommitUserValue;
                    } elseif ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_DEPARTMENT) {
                        $workJournalTemplateUserIds = array_reduce($itemCommitUserValue, function ($result, $key) use ($userIdsKeyByDepartmentId) {
                            return array_merge($result, $userIdsKeyByDepartmentId[$key]);
                        }, []);
                    } elseif ($commitUserType == \common\models\client\WorkJournalTemplate::COMMIT_USER_TYPE_ROLE) {
                        $workJournalTemplateUserIds = array_reduce($itemCommitUserValue, function ($result, $key) use ($userIdsKeyByRoleId) {
                            return array_merge($result, $userIdsKeyByRoleId[$key]);
                        }, []);
                    }

                    $endTimeStamp = $itemWorkJournalTemplate['commit_end_time'] ?? '';
                    $timeDesc = "";
                    $isValid = true;
                    switch ($itemTemplateType) {
                        case \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY:
                            if ($endTimeStamp > 86400) {
                                $endTimeDayTimestamp = $endTimeStamp - 86400 + $compareDay;
                            } else {
                                $endTimeDayTimestamp = $endTimeStamp + $compareDay;
                            }
                            $endTimeMinuteAndSecond = date('H:i', $endTimeDayTimestamp);
                            $timeDesc = "今天{$endTimeMinuteAndSecond}之前";

                            $endTime = date('Y-m-d H:i:s', $endTimeDayTimestamp);
                            $startTime = date('Y-m-d 00:00:00', strtotime($endTime) - $endTimeStamp);
                            break;
                        case \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_WEEK:
                            switch ($itemWeekCycleType) {
                                case \common\models\client\WorkJournalTemplate::WEEK_CYCLE_TYPE_THIS_WEEK:
                                    $endTimeWeekTimestamp = $endTimeStamp + $compareWeek;
                                    break;
                                case \common\models\client\WorkJournalTemplate::WEEK_CYCLE_TYPE_LAST_WEEK_SUNDAY:
                                    $endTimeWeekTimestamp = $endTimeStamp + $compareWeek - 86400;
                                    break;
                                case \common\models\client\WorkJournalTemplate::WEEK_CYCLE_TYPE_LAST_WEEK_SATURDAY:
                                    $endTimeWeekTimestamp = $endTimeStamp + $compareWeek - 86400 * 2;
                                    break;
                                case \common\models\client\WorkJournalTemplate::WEEK_CYCLE_TYPE_LAST_WEEK_FRIDAY:
                                    $endTimeWeekTimestamp = $endTimeStamp + $compareWeek - 86400 * 3;
                                    break;
                            }
                            $endTimeMinuteAndSecond = date('H:i', $endTimeWeekTimestamp);
                            $dayOfWeek = \common\library\work_journal\Helper::getDayOfWeek($endTimeWeekTimestamp);
                            $timeDesc = "{$dayOfWeek}{$endTimeMinuteAndSecond}之前";

                            $endTime = date('Y-m-d H:i:s', $endTimeWeekTimestamp);
                            $startTime = date('Y-m-d 00:00:00', strtotime($endTime) - $endTimeStamp);
                            break;
                        case \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_MONTH:
                            $lastMonth = strtotime('-1 month', $currentMinuteTimestamp);
                            $daysInLastMonth = date('t', $lastMonth);
                            if ($monthDay <= 15 && $endTimeStamp > $daysInLastMonth * 86400) {
                                $endTimeMonthTimestamp = $endTimeStamp - 86400 * $daysInLastMonth + $compareMonth;
                            } else {
                                $endTimeMonthTimestamp = $endTimeStamp + $compareMonth;
                            }
                            $endTimeMinuteAndSecond = date('H:i', $endTimeMonthTimestamp);
                            $dayOfMonth = date('d', $endTimeMonthTimestamp);
                            $timeDesc = "本月{$dayOfMonth}日{$endTimeMinuteAndSecond}之前";

                            $endTime = date('Y-m-d H:i:s', $endTimeMonthTimestamp);
                            $startTime = date('Y-m-d 00:00:00', strtotime($endTime) - $endTimeStamp);
                            break;
                        default:
                            $isValid = false;
                            \LogUtil::info("client_id：{$itemClientId}，templateId：{$itemTemplateId}，模版类型不合法，模版类型：{$itemTemplateType}");
                            break;
                    }
                    if (!$isValid) {
                        continue;
                    }

                    // 获取已经提交该模板报告的用户
                    $workJournal = new \common\library\work_journal\WorkJournalList($itemClientId);
                    $workJournal->setTemplateId($itemTemplateId);
                    $workJournal->setStartTime($startTime);
                    $workJournal->setEndTime($endTime);
                    $workJournal->setEnableFlag(WorkJournal::ENABLE_FLAG_TRUE);
                    $workJournal->setSkipPermissionCheck(true);
                    $workJournalList = $workJournal->find();
                    $workJournalTemplateCommitUserIds = array_column($workJournalList, 'user_id');

                    // 获取未提交成员
                    $workJournalTemplateUnCommitUserIds = array_diff($workJournalTemplateUserIds, $workJournalTemplateCommitUserIds);
                    $workJournalTemplateUnCommitUserCount = count($workJournalTemplateUnCommitUserIds);
                    LogUtil::info("需要提醒成员数，clientId：{$itemClientId}，templateId：{$itemTemplateId}，unCommitUserCount：{$workJournalTemplateUnCommitUserCount}");

                    // 消息通知
                    $workJournalTemplateName = $itemWorkJournalTemplate['name'] ?? '';
                    foreach ($workJournalTemplateUnCommitUserIds as $workJournalTemplateUnCommitUserId)
                    {
                        $remindTimestamp = strtotime($endTime) - time();
                        $hours = ceil($remindTimestamp / 3600); // 计算经过的小时数，向上取整
                        $hours = intval(max($hours, 0)); //最小为0

                        $processedDetailMap = [
                            'client_id' => $itemClientId,
                            'title' => '工作报告提交通知',
                            'desc' => "{$workJournalTemplateName}提交时间为【{$timeDesc}】",
                            'template_id' => $itemTemplateId
                        ];

                        $appProcessedDetailMap = [
                            'client_id' => $itemClientId,
                            'title' => '你有一份工作报告提交提醒',
                            'desc' => "距离{$workJournalTemplateName}还剩{$hours}小时",
                            'template_id' => $itemTemplateId
                        ];
                        if (!$dryRun) {
                            $privilegeService = PrivilegeService::getInstance($itemClientId, $workJournalTemplateUnCommitUserId);
                            // 没有工作报告模块权限不通知
                            $hasPrivilege = $privilegeService->hasFunctional(PrivilegeConstants::FUNCTIONAL_WORK_JOURNAL) && $privilegeService->hasPrivilege(PrivilegeConstants::PRIVILEGE_CRM_WORK_JOURNAL_VIEW);

                            Browser::push($workJournalTemplateUnCommitUserId, \common\library\push\Browser::TYPE_REMIND_WORK_JOURNAL, $processedDetailMap);
                            $modelMap = $userIdToWorkJournalRemindSettingMap[$workJournalTemplateUnCommitUserId] ?? [];
                            $pushAppFlag = $modelMap['push_flag'] ?? \common\library\setting\user\process\Notification::APP_PUSH_DEFAULT_SETTING[Constant::APP_MODULE_WORK_JOURNAL_REMIND];

                            $canNotifyWorkJournal = $hasPrivilege && $pushAppFlag;

                            if ($canNotifyWorkJournal)
                            {
                                \common\library\notification\PushHelper::pushAppWorkJournalRemind($itemClientId,$workJournalTemplateUnCommitUserId,$appProcessedDetailMap);
                            }
                        }
                    }
                }
            }

            $setIdEndTime = date('Y-m-d H:i:s');
            LogUtil::info("setId：{$setId}，结束执行：{$setIdEndTime}");
        }

        $workJournalSubmitRemindEndTime = time();
        $timeConsuming = $workJournalSubmitRemindEndTime - $workJournalSubmitRemindStartTime;
        LogUtil::info("工作报告提交提醒结束时间：{$workJournalSubmitRemindEndTime}，总耗时：{$timeConsuming}s");
    }

    /*
     * 预热数据，维护用户收到的工作报告的模版的redis
     */
    public function actionPreheatHaveReceiveWorkJournalTemplate(int $clientId = 0, int $dryRun = 1)
    {
        if (empty($clientId)) {
            $clientIds = array_column($this->getClientList(0, true, true, null, 0, 0), 'client_id');
        } else {
            $clientIds = [$clientId];
        }

        foreach ($clientIds as $clientId)
        {
            $db = PgActiveRecord::getDbByClientId($clientId);
            if (empty($db)) {
                continue;
            }
            try {
                $userList = new \common\library\account\UserList();
                $userList->setClientId($clientId);
                $userList->setEnableFlag(true);
                $userList->setFields(['user_id']);
                $userIds = array_column($userList->find(), 'user_id');
                $enableTemplateSql = "select template_id from tbl_work_journal_template where client_id={$clientId} and enable_flag=1 and delete_flag=0";
                $enableTemplateIds = $db->createCommand($enableTemplateSql)->queryColumn();
                if (empty($userIds) || empty($enableTemplateIds)) {
                    continue;
                }
                $userTemplateSql = "select user_id,template_id from tbl_work_journal_record where client_id={$clientId} and user_id in (" . implode(',', $userIds) . ") and record_type=1 and template_id in (" . implode(',', $enableTemplateIds) . ") and enable_flag=1 group by user_id,template_id";
                $userTemplateList = $db->createCommand($userTemplateSql)->queryAll();
                $userIdToTemplateIdsMap = [];
                foreach ($userTemplateList as $userTemplate)
                {
                    $userId = $userTemplate['user_id'];
                    $userIdToTemplateIdsMap[$userId][] = $userTemplate['template_id'];
                }

                $keysToValue = [];
                foreach ($userIdToTemplateIdsMap as $userId => $templateIds)
                {
                    $key = "{{$clientId}}." . \common\library\work_journal\Constant::PREFIX_OF_USER_TEMPLATE . $userId;
                    $keysToValue[$key] = implode(',', $templateIds);
                }
                self::info(sprintf("dryRun={$dryRun}, client_id={$clientId}, keysToValue=[%s]", json_encode($keysToValue)));
                if (!$dryRun && !empty($keysToValue)) {
                    $redis = \common\library\cache\RedisCache::getCache();
                    $redis->mset($keysToValue);
                }
            } catch (Exception $exception) {
                self::info("client_id={$clientId},error message={$exception->getMessage()}");
            }

        }
    }

    // ./yiic-test workJournal deleteWorkJournalWhileTemplateDelete --clientId=9650 --templateId=1
    public function actionDeleteWorkJournalWhileTemplateDelete(int $clientId, int $templateId)
    {
        $client = new \common\library\account\Client($clientId);
        $masterUser = $client->getMasterUser();
        $userId = $masterUser->getUserId();
        \User::setLoginUserById($userId);

        $template = \common\models\client\WorkJournalTemplate::model()->find('template_id=:template_id', [':template_id' => $templateId]);
        if (empty($template) || $template->delete_flag != \common\models\client\WorkJournalTemplate::DELETE_FLAG_TRUE) {
            return;
        }
        $now = date('Y-m-d H:i:s');
        self::info("当前时间=[{$now},client_id={$clientId},删除了工作报告模版template_id={$templateId}]");

        $ruleInfo = is_array($template->performance_rule_info) ? $template->performance_rule_info : json_decode($template->performance_rule_info, true);
        $ruleIds = array_column($ruleInfo, 'rule_id');
        if (!empty($ruleIds))
        {
            $recordList = new \common\library\performance_v2\record\PerformanceV2RecordList($clientId,$userId);
            $recordList->setReferType(\Constants::TYPE_WORK_JOURNAL);
            $recordList->setRuleId($ruleIds);
            $recordList->delete();
        }

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($pgDb);

        $limit = 1000;
        $recordCount = 0;
        $workJournalCount =  0;
        //先删除副本record再处理原数据
        foreach (\common\library\util\SqlUtil::queryAll($pgDb, 'tbl_work_journal_record', 'record_id', 'record_id', 0, $limit, " and client_id={$clientId} and template_id={$templateId} and enable_flag=1")  as $recordList) {
            $recordCount += count($recordList);
            $recordIds = array_column($recordList, 'record_id');
            $updateRecordSql = "UPDATE tbl_work_journal_record SET enable_flag=0 WHERE client_id={$clientId} and record_id in (" . implode(',', $recordIds) . ')';
            $pgDb->createCommand($updateRecordSql)->execute();
        }
        self::info("client_id={$clientId},template_id={$templateId},work_journal_record_count={$recordCount}");

        foreach (\common\library\util\SqlUtil::queryAll($pgDb, 'tbl_work_journal', 'journal_id', 'journal_id', 0, $limit, " and  client_id={$clientId} and template_id={$templateId} and enable_flag=1")  as $workJournalList) {
            $workJournalCount += count($workJournalList);
            $workJournalIds = array_column($workJournalList, 'journal_id');
            $updateWorkJournalSql = "UPDATE tbl_work_journal SET enable_flag=0 WHERE client_id={$clientId} and journal_id in (" . implode(',', $workJournalIds) . ')';
            $pgDb->createCommand($updateWorkJournalSql)->execute();
        }
        self::info("client_id={$clientId},template_id={$templateId},work_journal_count={$workJournalCount}");

        //清除用户的设置-记住模版上一次接收人
        $mysqlDb = ProjectActiveRecord::getDbByClientId($clientId);
        $userSettingSql = "select * from tbl_user_setting where client_id=:client_id and `key`=:key";
        $key = \common\library\setting\user\UserSetting::WORK_JOURNAL_REMEMBER_LAST_RECEIVE_USER_IDS;

        $userSettingList = $mysqlDb->createCommand($userSettingSql)->queryAll(true, [
            ':client_id' => $clientId,
            ':key' => $key,
        ]);
        $caseWhen = '';
        $needUpdateSettingUserIds = [];
        foreach ($userSettingList as $setting) {

            $value = json_decode($setting['value'],true);
            if (isset($value[$templateId])) {
                unset($value[$templateId]);
                $needUpdateSettingUserIds[] = $setting['user_id'];
                $caseWhen .= " WHEN user_id={$setting['user_id']} THEN '". json_encode($value) . "' ";
            }
        }
        if (!empty($needUpdateSettingUserIds)) {
            $needUpdateSettingUserIdsStr = implode(',', $needUpdateSettingUserIds);
            $updateSettingSql = "UPDATE tbl_user_setting SET value = ( CASE {$caseWhen} ELSE value END) WHERE client_id={$clientId} and user_id in ({$needUpdateSettingUserIdsStr}) and `key` = '{$key}'";
            $rows = $mysqlDb->createCommand($updateSettingSql)->execute();
            self::info(sprintf("client_id={$clientId},template_id={$templateId},key={$key},rows={$rows},清除的用户user_id=[%s]", $needUpdateSettingUserIdsStr, ));
        }
    }

    // ./yiic-test workJournal updateWorkJournalCommitEndTime --clientId=333383 --templateId=**********
    public function actionUpdateWorkJournalCommitEndTime(int $clientId, int $templateId)
    {
        $client = new \common\library\account\Client($clientId);
        $masterUser = $client->getMasterUser();
        $userId = $masterUser->getUserId();
        \User::setLoginUserById($userId);

        $template = \common\models\client\WorkJournalTemplate::model()->find('template_id=:template_id and enable_flag=1 and delete_flag = 0', [':template_id' => $templateId]);
        if (empty($template)) {
            return;
        }
        $now = date('Y-m-d H:i:s');
        self::info("当前时间=[{$now},client_id={$clientId},工作报告模版template_id={$templateId}]提交截止时间更改commit_end_time={$template->commit_end_time}");
        $commitEndTimeStamp = $template->commit_end_time;
        $commitEndTimeStampOffsetDays = intval($commitEndTimeStamp / 86400);
        $monthT = date("t");

        $templateTypeDayMap = [
            \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_DAY => [
                24*60*60,
                24*60*60*2,
            ],
            \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_WEEK => [
                7*24*60*60,
                8*24*60*60,
                9*24*60*60,
                10*24*60*60,
                11*24*60*60,
            ],
            \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_MONTH => [
                32*24*60*60, // 每月最后一天
                33*24*60*60, // 次月1号
                34*24*60*60, // 次月2号
                35*24*60*60,
                36*24*60*60,
                37*24*60*60,
                38*24*60*60,
                39*24*60*60,
                40*24*60*60,
                41*24*60*60,
                42*24*60*60,
                43*24*60*60,
                44*24*60*60,
                45*24*60*60,
                46*24*60*60,
                47*24*60*60, // 次月15号
            ],
        ];

        $templateType = $template->template_type;
        if (empty($commitEndTimeStamp)) {
            $commitEndTimeStamp = $templateTypeDayMap[$templateType][0];
        }

        $pgDb = PgActiveRecord::getDbByClientId($clientId);
        PgActiveRecord::setConnection($pgDb);

        $limit = 1000;
        $recordCount = 0;
        $workJournalCount =  0;
        //先更新副本record再处理原数据
        foreach (\common\library\util\SqlUtil::queryAll($pgDb, 'tbl_work_journal_record', 'record_id', 'record_id,start_time', 0, $limit, " and client_id={$clientId} and template_id={$templateId} and enable_flag=1")  as $recordList) {
            $recordCount += count($recordList);
            $recordIds = [];
            $caseWhen = '';
            foreach ($recordList as $record)
            {
                $recordId = $record['record_id'];
                $recordIds[] = $recordId;
                $startTime = $record['start_time'];
                $endTime = date('Y-m-d H:i:s', (strtotime($startTime) + $commitEndTimeStamp));
                $caseWhen .= " WHEN record_id={$recordId} THEN '{$endTime}' ";
            }
            $recordIdsStr = implode(',', $recordIds);
            $updateRecordSql = "UPDATE tbl_work_journal_record SET end_time = ( CASE {$caseWhen} ELSE end_time END) WHERE client_id={$clientId} and record_id in ({$recordIdsStr})";
            self::info("UpdateWorkJournalCommitEndTime,client_id={$clientId},template_id={$templateId},record_id={$recordIdsStr}");
            $pgDb->createCommand($updateRecordSql)->execute();
        }
        self::info("client_id={$clientId},template_id={$templateId},work_journal_record_count={$recordCount}");

        foreach (\common\library\util\SqlUtil::queryAll($pgDb, 'tbl_work_journal', 'journal_id', 'journal_id,start_time', 0, $limit, " and  client_id={$clientId} and template_id={$templateId} and enable_flag=1")  as $workJournalList) {
            $workJournalCount += count($workJournalList);
            $journalIds = [];
            $caseWhen = '';
            foreach ($workJournalList as $workJournal)
            {
                $journalId = $workJournal['journal_id'];
                $journalIds[] = $journalId;
                $startTime = $workJournal['start_time'];
                $endTime = date('Y-m-d H:i:s', (strtotime($startTime) + $commitEndTimeStamp));
                $cycleEndTime = \common\library\work_journal\Helper::getCycleEndTimeByTemplateType($startTime, $templateType);
                // 特殊处理月报的截止时间设置为每月最后一天和次月的情况
                if ($templateType == \common\models\client\WorkJournalTemplate::WORK_REPORT_TYPE_MONTH && $commitEndTimeStampOffsetDays >= 31) {
                    $hourMinutes = date('H:i', strtotime($endTime));
                    if ($commitEndTimeStampOffsetDays == 31) {
                        $endTime = date("Y-m-d {$hourMinutes}:00", strtotime($cycleEndTime));
                    } else {
                        $tmpTime = date('Y-m-t 23:59:59', strtotime($startTime));
                        $offsetDays = $commitEndTimeStampOffsetDays - 31;
                        $tmpTime = date('Y-m-d 23:59:59', strtotime("+{$offsetDays} day", strtotime($tmpTime)));
                        $endTime = date("Y-m-d {$hourMinutes}:00", strtotime($tmpTime));
                    }
                }
                if (strtotime($cycleEndTime) < strtotime($endTime)) {
                    // 24点的情况需要特殊处理
                    if (in_array($commitEndTimeStamp, $templateTypeDayMap[$template->template_type]))
                    {
                        $endTime = date('Y-m-d H:i:s', (strtotime($endTime) - 1));
                    }
                }
                $caseWhen .= " WHEN journal_id={$journalId} THEN '{$endTime}' ";
            }

            $journalIdsStr = implode(',', $journalIds);
            $updateRecordSql = "UPDATE tbl_work_journal SET end_time = (CASE {$caseWhen} ELSE end_time END) WHERE client_id={$clientId} and journal_id in ({$journalIdsStr})";
            self::info("UpdateWorkJournalCommitEndTime,client_id={$clientId},template_id={$templateId},journal_id={$journalIdsStr}");
            $pgDb->createCommand($updateRecordSql)->execute();
        }
        self::info("client_id={$clientId},template_id={$templateId},work_journal_count={$workJournalCount}");
    }


    // 修改工作报告的提交时间
    // ./yiic-test workJournal ChangeWorkJournalCommitTime --clientId=9650 --journalId=********** --commitTime='2023-07-25 10:51:28' --dryRun=1
    public function actionChangeWorkJournalCommitTime(int $clientId, int $journalId, string $commitTime, int $dryRun = 1)
    {
        $client = new \common\library\account\Client($clientId);
        $user = $client->getMasterUser();
        \User::setLoginUserById($user->getUserId());
        $workJournal = new \common\library\work_journal\WorkJournal($clientId, $user->getUserId(), $journalId);

        if ($workJournal->isNew())
        {
            self::info("client_id={$clientId},journal_id={$journalId},no exist");
            return false;
        }

        self::info("client_id={$clientId},journal_id={$journalId},old_commit_time={$workJournal->commit_time},new_commit_time={$commitTime}");
        $db = PgActiveRecord::getDbByClientId($clientId);
        $updateSql = "update tbl_work_journal set commit_time=:commit_time where client_id={$clientId} and journal_id={$journalId}";
        $updateRecordSql = "update tbl_work_journal_record set commit_time=:commit_time where client_id={$clientId} and journal_id={$journalId}";

        if (!$dryRun) {
            $rows = $db->createCommand($updateSql)->execute([':commit_time' => $commitTime]);
            $recordRows = $db->createCommand($updateRecordSql)->execute([':commit_time' => $commitTime]);
            self::info("client_id={$clientId},journal_id={$journalId},update_journal_rows={$rows},update_record_rows={$recordRows}");
        } else {
            \Util::batchLogInfo([
                'sql' => 'update的sql',
                'tbl_work_journal' => $updateSql,
                'tbl_work_journal_record' => $updateRecordSql
            ]);
        }
        return true;
    }
}