<?php
/**
 * Copyright (c) 2012 - 2018 Xiaoman.All Rights Reserved
 * Author: nuxse
 * Data: 2018/9/26
 */

class RepairMailIdCommand extends CrontabCommand
{

    public function actionRepairAll()
    {

        //获取所有client 遍历
        $clients = Yii::app()->account_base_db->createCommand("SELECT `client_id`,`name`,`mysql_set_id` FROM `tbl_client`")->queryAll(true);
        $count = 0;
        echo '共需处理client数量：', count($clients), PHP_EOL;
        foreach ($clients as $client) {

            if(empty($client['mysql_set_id'])) {
                echo 'client_id 数据库连接错误,跳过',PHP_EOL;
                continue;
            }

            $this->actionRepairClient($client['client_id']);
            $count++;
        }

        echo '成功处理：', $count, PHP_EOL;
    }

    public function actionRepairClient($client_id)
    {
        $users = Yii::app()->account_base_db->createCommand("SELECT user_id FROM `tbl_user_info` where client_id={$client_id}")->queryAll(true);
        foreach ($users as $user) {
            if($user['user_id'] == 0) {
                continue;
            }
            $this->actionRepairUser($user['user_id']);
        }
    }

    public function actionRepairUser($user_id)
    {
        \User::setLoginUserById($user_id);
        //找出当前节点的所有小于25W的邮件
        $db = ProjectActiveRecord::getDbByUserId($user_id);
        $data = $db->createCommand("select * from tbl_mail where mail_id<=250000 and user_id={$user_id} and create_time>'2018-09-25'")->queryAll(true);

        if(empty($data)) {
            echo '需要处理的邮件数量为0 忽略处理',PHP_EOL;
            return ;
        }

        echo '需要处理的邮件数量为',count($data),PHP_EOL;

        foreach ($data as $datum){
            $this->mail($datum,$user_id);
        }
    }

    public function mail($mail,$user_id)
    {
        $mail_id = $mail['mail_id'];
        $db = ProjectActiveRecord::getDbByUserId($user_id);
        //如果能找到扩展属性 那么就是系统的邮件
        if($db->createCommand("select count(1) from tbl_mail_external where mail_id={$mail_id} and create_time>'2018-09-25'")->queryScalar()){
            $this->systemMail($mail,$user_id);
        }else{
            $this->syncMail($mail,$user_id);
        }

    }

    public function syncMail($mail,$user_id)
    {
        $mail_id = $mail['mail_id'];
        $user_mail_id = $mail['user_mail_id'];
        $client_id = \User::getLoginUser()->getClientId();
        echo '正在处理同步邮件',$mail_id,PHP_EOL;
        $db = ProjectActiveRecord::getDbByUserId($user_id);

        //删除附件
        \MailAttach::batchDeleteByMailId($mail_id);

        //删除动态
        $pg = PgActiveRecord::getDbByUserId($user_id);
        $pg->createCommand("delete from tbl_dynamic_trail WHERE  client_id={$client_id} and refer_id={$mail_id} and create_time>'2018-09-25'")->execute();

        //删除正文
        \common\models\mongo\MailContent::getInstance()->setUserMailId($user_mail_id)->deleteByIds([$mail_id]);

        //删除版本号
        $db->createCommand("delete from tbl_mail_version WHERE  mail_id={$mail_id} and create_time>'2018-09-25'")->execute();

        //删除邮件模型
        $result = $db->createCommand("delete from tbl_mail WHERE  mail_id={$mail_id}")->execute();
        echo "delete from tbl_mail WHERE  mail_id={$mail_id}",PHP_EOL;

    }

    public function systemMail($mail,$user_id)
    {
        $mail_id = $mail['mail_id'];
        $user_mail_id = $mail['user_mail_id'];
        echo '正在处理系统邮件',$mail_id,PHP_EOL;
        $db = ProjectActiveRecord::getDbByUserId($user_id);

        User::setLoginUserById($user_id);
        $user = User::getLoginUser();
        $clientId = $user->getClientId();

        $new_mail_id = ProjectActiveRecord::produceAutoIncrementId();

        //修改附件
        $db->createCommand("update tbl_mail_attach set mail_id={$new_mail_id} where mail_id={$mail_id} and create_time>'2018-09-25'")->execute();


        //修改扩展属性
        $db->createCommand("update tbl_mail_attach set mail_id={$new_mail_id} WHERE  mail_id={$mail_id} and create_time>'2018-09-25'")->execute();

        //如果存在发送任务 表明该邮件逻辑正常 只需要替换mail_id
        if($db->createCommand("select count(1) from tbl_async_mail_task where mail_id={$mail_id} and create_time>'2018-09-25'")->queryScalar()) {


            $db->createCommand("update tbl_mail_version set mail_id={$new_mail_id} WHERE  mail_id={$mail_id} and create_time>'2018-09-25'")->execute();

            $pg = PgActiveRecord::getDbByUserId($user_id);
            $pg->createCommand("update tbl_dynamic_trail set  refer_id={$new_mail_id} where  refer_id={$mail_id}  and create_time>'2018-09-25'")->execute();



            $db->createCommand("update tbl_async_mail_task set mail_id={$new_mail_id} WHERE  mail_id={$mail_id}")->execute();


            \common\models\mongo\MailContent::getInstance()->setUserMailId($user_mail_id)->getCollection()->updateOne([
                '_id' => $mail_id
            ],
                [
                    '_id' => $new_mail_id
                ],
                [
                    'upsert' => true,
                    'writeConcern' => new \MongoDB\Driver\WriteConcern(1)
                ]);


            //修改主属性
            $db->createCommand("update tbl_mail set mail_id={$new_mail_id} WHERE  mail_id={$mail_id}")->execute();

            return ;
        }


        //删除正文
        \common\models\mongo\MailContent::getInstance()->setUserMailId($user_mail_id)->deleteByIds([$mail_id]);




        //删除版本号
        $db->createCommand("delete from tbl_mail_version WHERE  mail_id={$mail_id} and create_time>'2018-09-25'")->execute();


        //查询是否存在草稿
        $mailDraftContent = new \common\library\mail\draft\MailDraftContent($mail_id,$user_id);
        $draft = $mailDraftContent->getAttributes();

        $date = date('Y-m-d H:i:s',time());

        if(!$draft){

            $mailDraftModel = new \common\library\mail\draft\MailDraftContent($new_mail_id,$user_id);
            //保存草稿
            $mailDraftModel->user_id     = $user_id;
            $mailDraftModel->client_id   = $clientId;
            $mailDraftModel->content     = '';
            $mailDraftModel->plain_text  = '';
            $mailDraftModel->create_time = $date;
            $mailDraftModel->update_time = $date;
            $result                      = $mailDraftModel->save();
        }else{

            $mailDraftContent->delete();

            //保存草稿
            $mailDraftModel = new \common\library\mail\draft\MailDraftContent($new_mail_id,$user_id);
            $mailDraftModel->user_id     = $user_id;
            $mailDraftModel->client_id   = $clientId;
            $mailDraftModel->content     = $draft['content'];
            $mailDraftModel->plain_text  = $draft['plain_text'];
            $mailDraftModel->create_time = $draft['create_time'];
            $mailDraftModel->update_time = $draft['update_time'];
            $result                      = $mailDraftModel->save();
        }

        //更新版本号
        $user = \User::getLoginUser();
        $version = new \common\library\version\MailDraftVersion($user->getClientId(), $user->getUserId());
        $version->setMailId($new_mail_id);
        $version->setType(\common\library\version\Constant::MAIL_MODULE_DRAFT);
        $version->add();

        //删除动态
        $pg = PgActiveRecord::getDbByUserId($user_id);
        $pg->createCommand("delete from tbl_dynamic_trail WHERE  refer_id={$mail_id} and create_time>'2018-09-25'")->execute();

        //修改主属性
        $db->createCommand("update tbl_mail set folder_id=0,send_status=0,mail_id={$new_mail_id} WHERE  mail_id={$mail_id}")->execute();
        echo "update tbl_mail set folder_id=0,send_status=0,mail_id={$new_mail_id} WHERE  mail_id={$mail_id}",PHP_EOL;

    }


}