<?php

use common\library\export_v2\Export;
use common\library\oms\cash_collection_invoice\export\CashCollectionInvoiceExportExecutor;

class CashCollectionInvoiceExportCommand  extends \CrontabCommand
{
    public function actionExport($user_id,$export_id)
    {
        ini_set("memory_limit", "2048M");

        $beginTime = microtime(true);
        self::info('begin: user_id:'.$user_id.' export_id:'.$export_id);

        \User::setLoginUserById($user_id);
        $user = \User::getLoginUser();

        $export = new Export($user->getClientId(),$export_id);

        $executor = new CashCollectionInvoiceExportExecutor($export);
        $executor->run();

        $endTime = microtime(true);

        self::info('finish: user_id:'.$user_id.' export_id:'.$export_id .' time:'.($endTime-$beginTime));

    }
}