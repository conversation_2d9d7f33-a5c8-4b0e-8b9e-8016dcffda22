<?php

namespace common\commands\iteration\iteration_4_5_1095944;

use common\library\account\service\LoginService;
use common\library\account\UserInfo;
use common\library\privilege_v3\PrivilegeService;
use common\library\setting\user\process\UserScheduleSetting;
use common\library\setting\user\UserSetting;
use common\models\admin\DingdingAccount;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeCustomTrait;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\dispatcher\task\RepeatableExecuteTrait;

// ./yiic-test iterationScript developRun --folder=iteration_4_5_1095944 --taskClass=UnbindDingTalkUser
class UnbindDingTalkUser extends IterationScriptTask
{
    use DispatchTypeCustomTrait;
    use RepeatableExecuteTrait;

    /**
     *  解绑重复绑定的用户数据
     */
    public function execute()
    {
        $unbindUserList = [
            56608 => ********
        ];
        $config = \Yii::app()->params['dingtalk'];
        foreach ($unbindUserList as $clientId => $userId) {
            \User::setLoginUserById(PrivilegeService::getInstance($clientId)->getAdminUserId());
            $dingDingService = new \common\library\account\service\DingDingService($config['app_client_id'], $config['app_client_secret']);
            $data = $dingDingService->unBindAccountByCrmUserId($userId, false);
        }

        // 此公司不存在，仅清除数据
        $unbindUserList = [
            26738 => ********,
        ];
        foreach ($unbindUserList as $clientId => $userId) {
            $accountModel = DingdingAccount::getUserByCrmId($userId);
            if (!empty($accountModel) && $accountModel->crm_user_id > 0) {
                $crmUserId                   = $accountModel->crm_user_id;
                $crmClientId                 = $accountModel->crm_client_id;
                $updateDate                  = date('Y-m-d H:i:s');
                $accountModel->unbind_time   = $updateDate;
                $accountModel->update_time   = $updateDate;
                $accountModel->crm_client_id = 0;
                $accountModel->crm_user_id   = 0;

                // 设置用户绑定过钉钉缓存
                if ($crmClientId && $crmUserId) {
                    $crmUserInfo = new UserInfo($crmUserId, $crmClientId);
                    $crmUserInfo->setExtentAttributes([
                        \common\library\account\external\UserInfoExternal::EXTERNAL_KEY_USER_HAS_BIND_DINGDING => json_encode([
                            'id' => 0, 'ding_user_id' => '', 'corp_id' => ''
                        ]),
                    ]);
                    $crmUserInfo->saveExtentAttributes();
                }

                // 最后保存
                $accountModel->save();
            }
        }
    }

}
