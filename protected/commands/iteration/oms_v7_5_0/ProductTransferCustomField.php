<?php

namespace common\commands\iteration\oms_v7_5_0;

use common\library\oms\command\ProductTransferOutboundFieldSetting;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeClientTrait;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\dispatcher\task\RepeatableExecuteTrait;

class ProductTransferCustomField extends IterationScriptTask
{
    use DispatchTypeClientTrait;
    use RepeatableExecuteTrait;
    use ProductTransferOutboundFieldSetting;
    public function execute()
    {
        $clientId = $this->clientId;
        $field_setting = $this->shippingTransferFieldSetting();
        $afterFields[\Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND] = [
            'order_id' => 'shipping_invoice_id',
            'refer_shipping_record_id'=> 'shipping_invoice_id',
            'shipping_count' => 'product_unit',
        ];
        \common\library\custom_field\Helper::addField($clientId, $field_setting, $afterFields);
    }

}