<?php

namespace common\commands\iteration\oms_v7_4_0;

use common\library\custom_field\Helper;
use common\library\oms\command\OmsFieldCommand;
use common\modules\prometheus\library\script\dispatcher\task\DispatchTypeClientTrait;
use common\modules\prometheus\library\script\dispatcher\task\IterationScriptTask;
use common\modules\prometheus\library\script\dispatcher\task\RepeatableExecuteTrait;

/**
 * 跟单协同 - 客户表 新增「任务名称」、「最新评论」字段
 */
class ProductTransferCustomField extends IterationScriptTask
{
    use DispatchTypeClientTrait;
    use RepeatableExecuteTrait;
    use OmsFieldCommand;

    public function execute()
    {
        $afterFields = [
            \Constants::TYPE_PRODUCT_TRANSFER_PURCHASE => [
                'name' => 'serial_id',
                'status' => 'name',
                'last_comment' => 'expect_time',
            ],
            \Constants::TYPE_PRODUCT_TRANSFER_INBOUND => [
                'name' => 'serial_id',
                'status' => 'name',
                'last_comment' => 'expect_time',
            ],
            \Constants::TYPE_PRODUCT_TRANSFER_OUTBOUND => [
                'name' => 'serial_id',
                'status' => 'name',
                'last_comment' => 'expect_time',
            ],
            \Constants::TYPE_PRODUCT_TRANSFER_OTHER => [
                'name' => 'serial_id',
                'last_comment' => 'expect_time',
            ],
        ];

        $system_fields = $this->actionProductTransferField();
        Helper::addField($this->clientId, $system_fields, $afterFields);

    }
}