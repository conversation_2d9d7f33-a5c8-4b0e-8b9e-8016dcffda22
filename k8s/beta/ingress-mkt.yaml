apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mkt-beta-ingress
  namespace: app-crm
  labels:
    app: mkt-beta-ingress
    developer: PHP-CRM
spec:
  defaultBackend:
    service:
      name: mkt-web-fe-shell-beta
      port:
        number: 8080
  rules:
    - host: 'mkt.feature.beta.xiaoman.cn'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
    - host: mkt.beta.xiaoman.cn
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: mkt-web-fe-shell-beta
                port:
                  number: 8080
          - path: /cms_dist
            pathType: ImplementationSpecific
            backend:
              service:
                name: mkt-web-fe-cms-beta
                port:
                  number: 8080
          - path: /target_dist
            pathType: ImplementationSpecific
            backend:
              service:
                name: mkt-web-fe-target-beta
                port:
                  number: 8080
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
          - path: /interface
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
          - path: /app
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
          - path: /prometheus
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
          - path: /track
            pathType: Prefix
            backend:
              service:
                name: mkt-fpm-beta
                port:
                  number: 80
  tls:
    - hosts:
        - '*.beta.xiaoman.cn'
      secretName: beta-xiaoman-cn-tls
    - hosts:
        - '*.feature.beta.xiaoman.cn'
      secretName: beta-feature-xiaoman-cn-tls
status:
  loadBalancer:
    ingress:
      - ip: ************
