kind: Deployment
apiVersion: apps/v1
metadata:
  name: crm-queue-binlog-mailconversation-dev
  namespace: app-crm
  labels:
    app: crm-queue-binlog-mailconversation-dev
    developer: PHP-CRM
    lang: php
  annotations:
    deployment.kubernetes.io/revision: '41'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: crm-queue-binlog-mailconversation-dev
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: crm-queue-binlog-mailconversation-dev
        app_env: dev
        service: crm-queue-binlog-mailconversation
        sidecar-injection: enabled
      annotations:
        kubectl.kubernetes.io/restartedAt: '2023-09-05T18:42:12+08:00'
        kubesphere.io/restartedAt: '2023-11-23T05:36:35.053Z'
        sidecarConfigName: crm-queue-dev
    spec:
      volumes:
        - name: volume-data
          hostPath:
            path: /data/codebase/crm_root/dev
            type: DirectoryOrCreate
      containers:
        - name: crm-queue-binlog-mailconversation-dev
          image: >-
            xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:yii
          command:
            - /usr/local/php/bin/php
            - '-c/usr/local/php/etc/php-cli.ini'
            - >-
              /data/codebase/crm_root/dev/feature/mail1105/php-crm/protected/vendor/bin/queue
            - 'worker:listen'
            - '--env=test'
            - '--channel=binlog_mail_job'
            - '--workers=4'
          env:
            - name: RUNTIME_ENV
              value: k8s
            - name: aliyun_logs_queue-service
              value: stdout
            - name: aliyun_logs_queue-service_tags
              value: app=queue
            - name: aliyun_logs_php-error
              value: /data/logs/php_errors.log
            - name: aliyun_logs_php-error_tags
              value: app=queue
            - name: CLI.grpc.enable_fork_support
              value: '0'
            - name: PHP.opcache.huge_code_pages
              value: '0'
            - name: CLI.opcache.huge_code_pages
              value: '0'
            - name: SIDECAR_ENABLE
              value: '1'
          resources:
            limits:
              cpu: 500m
              ephemeral-storage: 1Gi
              memory: 2Gi
            requests:
              cpu: 20m
              ephemeral-storage: 100Mi
              memory: 68719476736m
          volumeMounts:
            - name: volume-data
              mountPath: /data/codebase/crm_root/dev
              mountPropagation: Bidirectional
          livenessProbe:
            exec:
              command:
                - sh
                - '-c'
                - pgrep -f channel=binlog_mail_job
            initialDelaySeconds: 10
            timeoutSeconds: 1
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - sh
                - '-c'
                - pgrep -f channel=binlog_mail_job
            initialDelaySeconds: 10
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          lifecycle:
            preStop:
              exec:
                command:
                  - /usr/local/php/bin/php
                  - '-c/usr/local/php/etc/php-cli.ini'
                  - >-
                    /data/codebase/crm_root/dev/feature/mail1105/php-crm/protected/vendor/bin/queue
                  - 'worker:stop'
                  - '--env=test'
                  - '--channel=binlog_mail_job'
                  - '--force=1'
                  - '--wait=300'
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
          securityContext:
            capabilities:
              add:
                - SYS_ADMIN
                - SYS_PTRACE
            privileged: true
            allowPrivilegeEscalation: true
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: None
      nodeSelector:
        xiaoman.cn/server-type: php-server
      securityContext: {}
      schedulerName: default-scheduler
      tolerations:
        - key: xiaoman.cn/server-type
          operator: Equal
          value: dev-php-server
          effect: NoSchedule
      dnsConfig:
        nameservers:
          - 169.254.20.10
          - 192.168.96.10
        searches:
          - app-crm.svc.cluster.local
          - svc.cluster.local
          - cluster.local
        options:
          - name: ndots
            value: '3'
          - name: timeout
            value: '1'
          - name: attempts
            value: '2'
          - name: single-request-reopen
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
