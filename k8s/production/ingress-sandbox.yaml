apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sandbox-prod-ingress
  namespace: app-crm
  labels:
    app: sandbox-prod-ingress
    developer: PHP-CRM
spec:
  defaultBackend:
    service:
      name: crm-web-fe-shell-prod
      port:
        number: 8080
  rules:
    - host: '*.sandbox.xiaoman.cn'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-shell-prod
                port:
                  number: 8080
          - path: /crm_dist
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-customer-prod
                port:
                  number: 8080
          - path: /mail_dist
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-mail-prod
                port:
                  number: 8080
          - path: /sales_dist
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-sales-prod
                port:
                  number: 8080
          - path: /dx_dist
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-discovery-prod
                port:
                  number: 8080
          - path: /mk_dist
            pathType: Prefix
            backend:
              service:
                name: crm-web-fe-marketing-prod
                port:
                  number: 8080
          - path: /valar_subapp
            pathType: Prefix
            backend:
              service:
                name:  crm-web-fe-valar-prod
                port:
                  number: 8080
          - path: /common_qiankun
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fe-common-qiankun-prod
                port:
                  number: 8080
          - path: /tms_subapp
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fe-tms-prod
                port:
                  number: 8080
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: crm-web-fpm-prod
                port:
                  number: 80
          - path: /interface
            pathType: Prefix
            backend:
              service:
                name: crm-web-fpm-prod
                port:
                  number: 80
          - path: /app
            pathType: Prefix
            backend:
              service:
                name: crm-web-fpm-prod
                port:
                  number: 80
          - path: /prometheus
            pathType: Prefix
            backend:
              service:
                name: crm-web-fpm-prod
                port:
                  number: 80
          - path: /track
            pathType: Prefix
            backend:
              service:
                name: crm-web-fpm-prod
                port:
                  number: 80
          - path: /api/discoveryRead
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/discoveryWrite
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/customerDiscoveryRead
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/customerDiscoveryWrite
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/ciqRead
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/ciqWrite
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/discoveryCompanyRead
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
          - path: /api/discoveryCompanyWrite
            pathType: ImplementationSpecific
            backend:
              service:
                name: crm-web-fpm-dx-prod
                port:
                  number: 80
status:
  loadBalancer:
    ingress:
      - ip: ************
