# dockerfile
# xiaoman-php8-cli
# centos7
#
FROM xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:xhprof
#FROM xiaoman-registry-vpc.cn-hangzhou.cr.aliyuncs.com/xiaoman-php/php8:yii
ARG APP_TAG
ENV CODEBARE_ROOT="/data/codebase/crm_root"
ENV CODEBASE_PATH="/data/codebase/crm_root/production/php-crm"
ENV YII_PATH="/data/codebase/yii"
ENV APP_TAG=APP_TAG
#为兼容 .nginx lua 后续再优化目录
COPY protected /data/codebase/crm_root/production/php-crm/protected
COPY index* /data/codebase/crm_root/production/php-crm/
COPY ping.js /data/codebase/crm_root/production/php-crm/
COPY favicon.ico /data/codebase/crm_root/production/php-crm/
COPY robots.txt /data/codebase/crm_root/production/php-crm/
COPY sitemap.xml /data/codebase/crm_root/production/php-crm/
RUN set -eux; \
    mkdir -p ${CODEBARE_ROOT}/production/php; \
    mkdir -p /data/v4_logs;\
    mkdir -p $CODEBASE_PATH/protected/runtime; \
    ln -s ${YII_PATH} ${CODEBARE_ROOT}/production/yii;\
    ln -s ${CODEBASE_PATH} ${CODEBARE_ROOT}/production/php-app ; \
    ln -s ${CODEBASE_PATH} ${CODEBARE_ROOT}/production/php-desktop ; \
    ln -s ${CODEBASE_PATH} ${CODEBARE_ROOT}/production/php-crm-inner ; \
    ln -s ${CODEBASE_PATH} ${CODEBARE_ROOT}/production/php-lighthouse ; \
    ln -s ${YII_PATH} ${CODEBARE_ROOT}/production/php/yii;\
    ln -s ${CODEBASE_PATH} ${CODEBARE_ROOT}/production/php/mkt ; \
    ln -s ${CODEBARE_ROOT}/production ${CODEBARE_ROOT}/grey ; \
    ln -s ${CODEBARE_ROOT}/production ${CODEBARE_ROOT}/beta ; \
    ln -s ${CODEBARE_ROOT}/production ${CODEBARE_ROOT}/dev ; \
    chmod  777 /data/v4_logs; \
    chmod  777 /data/logs; \
    chmod  777  $CODEBASE_PATH/protected/runtime; \
    chmod  +x $CODEBASE_PATH/protected/yiic; \
    chmod  +x $CODEBASE_PATH/protected/yiic-grey; \
    chmod  +x $CODEBASE_PATH/protected/yiic-test; \
    chmod  +x $CODEBASE_PATH/protected/yiic-omg; \
    chmod  +x $CODEBASE_PATH/protected/yiic-exp; \

    ls -al  $CODEBASE_PATH; \
    ls -al ${CODEBARE_ROOT}; \
    ls -al ${CODEBARE_ROOT}/production; \
    ls -al ${CODEBARE_ROOT}/production/php; \
    ls -al ${CODEBARE_ROOT}/production/php-crm; \

